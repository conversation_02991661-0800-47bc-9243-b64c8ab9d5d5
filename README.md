# ZECO AI - All-in-One AI Platform

<div align="center">
  <img src="public/favicon-32x32.png" alt="ZECO AI Logo" width="64" height="64">
  
  **One subscription for everything AI**
  
  [![Node.js Version](https://img.shields.io/badge/node-%3E%3D20.12.0-green)](https://nodejs.org/)
  [![Next.js](https://img.shields.io/badge/Next.js-15.2.4-black)](https://nextjs.org/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-5.8.2-blue)](https://www.typescriptlang.org/)
  [![Status](https://img.shields.io/badge/status-private-red)](https://zeco.ai)
  
  <p>
    <img src="https://supabase.com/badge-made-with-supabase-dark.svg" alt="Made with Supabase" height="24">
    <img src="https://img.shields.io/badge/Deployed%20on-Vercel-000000?style=for-the-badge&logo=vercel&logoColor=white" alt="Deployed on Vercel" height="24">
  </p>
</div>

---

## 🚀 About ZECO AI

ZECO AI is a comprehensive AI platform that provides access to the latest AI models, tools, and workflows in a single, unified interface. Whether you're generating text, images, videos, processing documents, or building complex AI workflows, ZECO AI simplifies the complexity of the AI landscape.

### ✨ Key Features

#### 🤖 **Multi-Model AI Chat**

- Access to leading AI models: GPT-4, Claude, Gemini, Mistral, Grok, and more
- Switch between models within the same conversation
- Contextual conversations with memory retention (soon)
- Web search integration for real-time information

#### 🎨 **AI Creative Suite**

- **Image Generation**: Create stunning visuals with DALL-E, Midjourney, Stable Diffusion
- **Image Enhancement**: Upscale, restore, and edit images with AI
- **Video Generation**: Create videos from text prompts

#### 🔗 **AI Workflows**

- Chain multiple AI models together
- Create custom automation pipelines
- Visual workflow builder (coming soon)
- Reusable workflow templates

#### 🛡️ **Enterprise Features**

- SOC 2-ready security and encryption (soon)
- User authentication and authorization
- Usage analytics and dashboard
- Team collaboration features

---

## 🛠️ Technology Stack

### Frontend

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: HeroUI (NextUI v2)
- **Animation**: Framer Motion
- **State Management**: React 19 with built-in features

### Backend & Database

- **Authentication**: Supabase Auth
- **Database**: PostgreSQL with Supabase
- **Real-time**: Supabase Realtime
- **File Storage**: Supabase Storage

### Development Tools

- **Package Manager**: PNPM
- **Linting**: ESLint with Prettier
- **Git Hooks**: Husky with lint-staged
- **Deployment**: Vercel

---

## 🚀 Quick Start

### Prerequisites

- Node.js 20.12.0 or higher
- PNPM 9.2.0 or higher
- Git

### Installation

1. **Clone the repository**

    ```bash
    git clone <repository-url>
    cd zecoai-fe
    ```

2. **Install dependencies**

    ```bash
    pnpm install
    ```

3. **Set up environment variables**

    ```bash
    cp .env.example .env.local
    ```

    Fill in the required environment variables:

    ```env
    # Supabase
    NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
    NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
    SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

    # AI Provider Keys
    OPENAI_API_KEY=your_openai_key
    ANTHROPIC_API_KEY=your_anthropic_key
    GOOGLE_API_KEY=your_google_key
    MISTRAL_API_KEY=your_mistral_key
    XAI_API_KEY=your_xai_key

    # Other services
    ELEVENLABS_API_KEY=your_elevenlabs_key
    FAL_KEY=your_fal_key
    ```

4. **Run the development server**

    ```bash
    pnpm dev
    ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

---

## 📁 Project Structure

```
zecoai-fe/
├── app/                     # Next.js app directory
│   ├── (post-auth)/        # Protected routes
│   │   ├── (main)/         # Main app features
│   │   │   ├── chat/       # Chat interface
│   │   │   ├── image/      # Image generation
│   │   │   └── video/      # Video generation
│   └── api/                # API routes
├── components/             # React components
│   ├── chat/              # Chat-related components
│   ├── landing-page/      # Landing page components
│   └── ui/                # Reusable UI components
├── lib/                   # Utility libraries
├── models/                # AI model configurations
├── types/                 # TypeScript type definitions
└── utils/                 # Utility functions
```

---

## 🎯 Core Features

### Chat Interface

The chat interface supports multiple AI models with seamless switching:

- Real-time streaming responses
- Code syntax highlighting
- Markdown rendering with LaTeX support
- Image and file uploads
- Conversation history

### Image Generation

Advanced image generation capabilities:

- Multiple model support (DALL-E, Midjourney, Stable Diffusion)
- Parameter customization (size, style, quality)
- Batch generation
- Image enhancement and editing tools

### Video Generation

Create videos from text prompts:

- Multiple aspect ratios
- Customizable duration and quality
- Video editing capabilities
- Audio integration

---

## 🔧 Configuration

### Model Configuration

Models are configured in the `models/` directory:

- `conversational/` - Chat model configurations
- `image/` - Image generation models
- `video/` - Video generation models
- `audio/` - Audio processing models

---

## 🚀 Deployment

Deployment is handled automatically via GitHub Actions using Vercel hooks. Push to main branch triggers the `#deploy-to-prod` workflow.

---

## 📋 Scripts

```bash
# Development
pnpm dev          # Start development server
pnpm build        # Build for production
pnpm start        # Start production server

# Code Quality
pnpm lint         # Run ESLint
pnpm format       # Format code with Prettier
pnpm format:check # Check code formatting

```

---

---

<div align="center">
  <p>Built with ❤️ by the ZECO AI team</p>
  <p>© 2024 ZECO AI. All rights reserved.</p>
</div>
