import { ModelTier } from '@/models/model-tiers';
import {
	GeneratedVideoDetails,
	miniMaxV1Examples,
	stableVideoDiffusionExamples,
	klingV16StandardExamples,
	klingV16ProExamples,
	klingV2MasterExamples,
	lumaDreamMachineExamples,
	lumaRay2Examples,
	lumaRay2FlashExamples,
	veo2Examples,
	veo3Examples,
} from './video-generation-examples';

export enum VideoModelProvider {
	Google = 'Google',
	KlingAI = 'KlingAI',
	Luma = 'Luma AI',
	MiniMax = 'MiniMax',
	StabilityAI = 'Stability AI',
}

export enum VideoModelDisplayName {
	MiniMax_V1 = 'MiniMax v1',
	Stable_Video_Diffusion = 'Stable Video Diffusion',
	Kling_V1_6_Standard = 'Kling v1.6',
	Kling_V1_6_Pro = 'Kling v1.6 Pro',
	Kling_V2_Master = 'Kling v2 Master',
	Luma_Dream_Machine = 'Luma Dream Machine v1.5',
	Luma_Ray_2 = 'Luma Ray 2',
	Lu<PERSON>_Ray_2_Flash = 'Luma Ray 2 Flash',
	Veo_2 = 'Veo 2',
	Veo_3 = 'Veo 3',
}

export const videoModelDisplayNameToProviderMap: Record<VideoModelDisplayName, VideoModelProvider> =
	{
		[VideoModelDisplayName.MiniMax_V1]: VideoModelProvider.MiniMax,
		[VideoModelDisplayName.Stable_Video_Diffusion]: VideoModelProvider.StabilityAI,
		[VideoModelDisplayName.Kling_V1_6_Standard]: VideoModelProvider.KlingAI,
		[VideoModelDisplayName.Kling_V1_6_Pro]: VideoModelProvider.KlingAI,
		[VideoModelDisplayName.Kling_V2_Master]: VideoModelProvider.KlingAI,
		[VideoModelDisplayName.Luma_Dream_Machine]: VideoModelProvider.Luma,
		[VideoModelDisplayName.Luma_Ray_2]: VideoModelProvider.Luma,
		[VideoModelDisplayName.Luma_Ray_2_Flash]: VideoModelProvider.Luma,
		[VideoModelDisplayName.Veo_2]: VideoModelProvider.Google,
		[VideoModelDisplayName.Veo_3]: VideoModelProvider.Google,
	};

export const videoModelDisplayNameToTechnicalNameMap: Record<VideoModelDisplayName, string> = {
	[VideoModelDisplayName.MiniMax_V1]: 'fal-ai/minimax/video-01',
	[VideoModelDisplayName.Stable_Video_Diffusion]: 'fal-ai/fast-svd/text-to-video',
	[VideoModelDisplayName.Kling_V1_6_Standard]: 'fal-ai/kling-video/v1.6/standard/text-to-video',
	[VideoModelDisplayName.Kling_V1_6_Pro]: 'fal-ai/kling-video/v1.6/pro/text-to-video',
	[VideoModelDisplayName.Kling_V2_Master]: 'fal-ai/kling-video/v2/master/text-to-video',
	[VideoModelDisplayName.Luma_Dream_Machine]: 'fal-ai/luma-dream-machine',
	[VideoModelDisplayName.Luma_Ray_2]: 'fal-ai/luma-dream-machine/ray-2',
	[VideoModelDisplayName.Luma_Ray_2_Flash]: 'fal-ai/luma-dream-machine/ray-2-flash',
	[VideoModelDisplayName.Veo_2]: 'veo-2.0-generate-001',
	[VideoModelDisplayName.Veo_3]: 'veo-3.0-generate-preview',
};

export const videoModelDisplayNameToEditEndpointMap: Record<VideoModelDisplayName, string> = {
	[VideoModelDisplayName.MiniMax_V1]: 'fal-ai/minimax/video-01/image-to-video',
	[VideoModelDisplayName.Stable_Video_Diffusion]: 'fal-ai/stable-video',
	[VideoModelDisplayName.Kling_V1_6_Standard]: 'fal-ai/kling-video/v1.6/standard/image-to-video',
	[VideoModelDisplayName.Kling_V1_6_Pro]: 'fal-ai/kling-video/v1.6/pro/image-to-video',
	[VideoModelDisplayName.Kling_V2_Master]: 'fal-ai/kling-video/v2/master/image-to-video',
	[VideoModelDisplayName.Luma_Dream_Machine]: 'fal-ai/luma-dream-machine/image-to-video',
	[VideoModelDisplayName.Luma_Ray_2]: 'fal-ai/luma-dream-machine/ray-2/image-to-video',
	[VideoModelDisplayName.Luma_Ray_2_Flash]:
		'fal-ai/luma-dream-machine/ray-2-flash/image-to-video',
	[VideoModelDisplayName.Veo_2]: 'veo-2.0-generate-001',
	[VideoModelDisplayName.Veo_3]: 'veo-3.0-generate-preview',
};

export const videoModelExamples: Record<VideoModelDisplayName, GeneratedVideoDetails[]> = {
	[VideoModelDisplayName.MiniMax_V1]: miniMaxV1Examples,
	[VideoModelDisplayName.Stable_Video_Diffusion]: stableVideoDiffusionExamples,
	[VideoModelDisplayName.Kling_V1_6_Standard]: klingV16StandardExamples,
	[VideoModelDisplayName.Kling_V1_6_Pro]: klingV16ProExamples,
	[VideoModelDisplayName.Kling_V2_Master]: klingV2MasterExamples,
	[VideoModelDisplayName.Luma_Dream_Machine]: lumaDreamMachineExamples,
	[VideoModelDisplayName.Luma_Ray_2]: lumaRay2Examples,
	[VideoModelDisplayName.Luma_Ray_2_Flash]: lumaRay2FlashExamples,
	[VideoModelDisplayName.Veo_2]: veo2Examples,
	[VideoModelDisplayName.Veo_3]: veo3Examples,
};

export interface VideoGenerationModel {
	key: VideoModelDisplayName;
	description: string;
	provider: VideoModelProvider;
	tier: ModelTier;
	videoExamples: GeneratedVideoDetails[];
	disabled?: boolean;
}

export const VideoModelsList: VideoGenerationModel[] = [
	{
		key: VideoModelDisplayName.Stable_Video_Diffusion,
		description: 'Image-to-video model for high-quality visual coherence and motion.',
		provider: VideoModelProvider.StabilityAI,
		tier: ModelTier.Standard,
		videoExamples: videoModelExamples[VideoModelDisplayName.Stable_Video_Diffusion],
		disabled: true, // TODO: Improve UX as this model only takes an image.
	},
	{
		key: VideoModelDisplayName.Kling_V1_6_Standard,
		description:
			"Kling's standard effects video generator with enhanced prompt understanding and visual effects, delivering high-quality cinematic videos with natural motion and detailed scene composition.",
		provider: VideoModelProvider.KlingAI,
		tier: ModelTier.Standard,
		videoExamples: videoModelExamples[VideoModelDisplayName.Kling_V1_6_Standard],
	},
	{
		key: VideoModelDisplayName.Kling_V1_6_Pro,
		description:
			"Advanced version of Kling's v1.6 model with professional-grade capabilities, offering superior visual quality, more complex camera movements, and better handling of intricate prompts for premium video generation.",
		provider: VideoModelProvider.KlingAI,
		tier: ModelTier.Premium,
		videoExamples: videoModelExamples[VideoModelDisplayName.Kling_V1_6_Pro],
	},
	{
		key: VideoModelDisplayName.Kling_V2_Master,
		description:
			"Kling's flagship 2.0 Master model generates exceptional quality, realistic video clips with sophisticated camera movements including zooms and pans, delivering cinema-grade results with outstanding prompt understanding and visual coherence.",
		provider: VideoModelProvider.KlingAI,
		tier: ModelTier.Premium,
		videoExamples: videoModelExamples[VideoModelDisplayName.Kling_V2_Master],
	},
	{
		key: VideoModelDisplayName.Luma_Dream_Machine,
		description:
			"Luma's Dream Machine v1.5 creates immersive, high-fidelity videos with natural motion and realistic physics, offering advanced creative control for generating cinematic content from text or image inputs.",
		provider: VideoModelProvider.Luma,
		tier: ModelTier.Premium,
		videoExamples: videoModelExamples[VideoModelDisplayName.Luma_Dream_Machine],
	},
	{
		key: VideoModelDisplayName.MiniMax_V1,
		description:
			'High-quality videos from text prompts or images with precise camera control and cinematic storytelling.',
		provider: VideoModelProvider.MiniMax,
		tier: ModelTier.Premium,
		videoExamples: videoModelExamples[VideoModelDisplayName.MiniMax_V1],
	},
	{
		key: VideoModelDisplayName.Luma_Ray_2,
		description:
			"Luma's large-scale video generative model capable of creating realistic visuals with natural, coherent motion and strong understanding of text instructions, featuring advanced capabilities for fast coherent motion, ultra-realistic details, and logical event sequences.",
		provider: VideoModelProvider.Luma,
		tier: ModelTier.Premium,
		videoExamples: videoModelExamples[VideoModelDisplayName.Luma_Ray_2],
	},
	{
		key: VideoModelDisplayName.Luma_Ray_2_Flash,
		description:
			'A faster, more efficient version of Luma Ray 2 that generates videos 3x faster at a third of the cost while maintaining high quality, offering production-ready text-to-video and image-to-video capabilities with audio and control features.',
		provider: VideoModelProvider.Luma,
		tier: ModelTier.Standard,
		videoExamples: videoModelExamples[VideoModelDisplayName.Luma_Ray_2_Flash],
	},
	{
		key: VideoModelDisplayName.Veo_2,
		description:
			'Veo 2 creates videos from images with camera controls, realistic motion and very high quality output.',
		provider: VideoModelProvider.Google,
		tier: ModelTier.Premium,
		videoExamples: videoModelExamples[VideoModelDisplayName.Veo_2],
	},
	{
		key: VideoModelDisplayName.Veo_3,
		description: "Sound on: Google's flagship Veo 3 text to video model, with audio",
		provider: VideoModelProvider.Google,
		tier: ModelTier.Premium,
		videoExamples: videoModelExamples[VideoModelDisplayName.Veo_3],
	},
];
