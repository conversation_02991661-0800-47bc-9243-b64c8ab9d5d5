import { VideoModelDisplayName } from './video-generation-models';

interface VideoModelConstraints {
	aspectRatio?: `${number}:${number}`[];
	duration?: string[];
	resolution?: `${number}p`[];
	generateAudio?: boolean[];
	//cfg?: number[];
}

export interface VideoGenerationParams {
	aspectRatio?: `${number}:${number}`;
	duration?: string;
	resolution?: `${number}p`;
	generateAudio?: boolean;
	//cfg?: number; from 0.1 to 1
}

export const VIDEO_MODEL_CONSTRAINTS: Record<VideoModelDisplayName, VideoModelConstraints> = {
	[VideoModelDisplayName.MiniMax_V1]: {},
	[VideoModelDisplayName.Stable_Video_Diffusion]: {},
	[VideoModelDisplayName.Kling_V1_6_Standard]: {
		aspectRatio: ['16:9', '9:16', '1:1'],
		duration: ['5', '10'],
	},
	[VideoModelDisplayName.Kling_V1_6_Pro]: {
		aspectRatio: ['16:9', '9:16', '1:1'],
		duration: ['5', '10'],
	},
	[VideoModelDisplayName.Kling_V2_Master]: {
		aspectRatio: ['16:9', '9:16', '1:1'],
		duration: ['5', '10'],
	},
	[VideoModelDisplayName.Luma_Dream_Machine]: {
		aspectRatio: ['16:9', '9:16', '4:3', '3:4', '21:9', '9:21'],
	},
	[VideoModelDisplayName.Luma_Ray_2]: {
		aspectRatio: ['16:9', '9:16', '4:3', '3:4', '21:9', '9:21'],
		duration: ['5s', '9s'],
		resolution: ['540p', '720p', '1080p'],
	},
	[VideoModelDisplayName.Luma_Ray_2_Flash]: {
		aspectRatio: ['16:9', '9:16', '4:3', '3:4', '21:9', '9:21'],
		duration: ['5s', '9s'],
		resolution: ['540p', '720p', '1080p'],
	},
	[VideoModelDisplayName.Veo_2]: {
		aspectRatio: ['16:9', '9:16'],
		duration: ['5', '6', '7', '8'],
	},
	[VideoModelDisplayName.Veo_3]: {
		aspectRatio: ['16:9'],
		duration: ['8'],
		resolution: ['720p', '1080p'],
		generateAudio: [true, false],
	},
};

type SupportedFileType = {
	extension: string;
	description: string;
};

export const getSupportedImageTypesForVideoModels = (
	model: VideoModelDisplayName
): SupportedFileType[] => {
	switch (model) {
		case VideoModelDisplayName.MiniMax_V1:
		case VideoModelDisplayName.Stable_Video_Diffusion:
		case VideoModelDisplayName.Kling_V1_6_Standard:
		case VideoModelDisplayName.Kling_V1_6_Pro:
		case VideoModelDisplayName.Kling_V2_Master:
		case VideoModelDisplayName.Luma_Dream_Machine:
		case VideoModelDisplayName.Luma_Ray_2:
		case VideoModelDisplayName.Luma_Ray_2_Flash:
		case VideoModelDisplayName.Veo_2:
		case VideoModelDisplayName.Veo_3:
			return [
				{ extension: '.jpg', description: 'JPEG Image' },
				{ extension: '.jpeg', description: 'JPEG Image' },
				{ extension: '.png', description: 'PNG Image' },
				{ extension: '.webp', description: 'WebP Image' },
				{ extension: '.gif', description: 'GIF Image' },
				{ extension: '.avif', description: 'AVIF Image' },
			];

		default:
			return [];
	}
};
