export enum GeneratedVideoFilter {
	ImageToVideo = 'ImageToVideo',
	Photorealism = 'Photorealism',
	Character = 'Character',
	Animation = 'Animation',
	Landscape = 'Landscape',
	Cinematic = 'Cinematic',
	Portrait = 'Portrait',
	Surreal = 'Surreal',
	CGI = 'CGI',
	Product = 'Product',
	Anime = 'Anime',
}

export interface GeneratedVideoDetails {
	videoUrl: string;
	prompt: string;
	filter?: GeneratedVideoFilter[];
	parameters?: {
		[key: string]: string | number | boolean;
	};
}

const supabaseStorageUrl =
	'https://pasiolwuajbjqtdrnueq.supabase.co/storage/v1/object/public/ai-generated-videos/public';
const minimaxVideo1Url = `${supabaseStorageUrl}/minimax-video-1`;
const kling16Url = `${supabaseStorageUrl}/kling1_6`;
const kling16ProUrl = `${supabaseStorageUrl}/kling1_6pro`;
const kling2Url = `${supabaseStorageUrl}/kling-2`;
const lumaDreamMachineUrl = `${supabaseStorageUrl}/luma-dream-machine`;
const lumaRay2Url = `${supabaseStorageUrl}/luma-ray-2`;
const lumaRay2FlashUrl = `${supabaseStorageUrl}/luma-ray-2-flash`;
const veo2Url = `${supabaseStorageUrl}/veo-2`;
const veo3Url = `${supabaseStorageUrl}/veo-3`;

export const miniMaxV1Examples: GeneratedVideoDetails[] = [
	{
		videoUrl: `${minimaxVideo1Url}/01.mp4`,
		prompt: 'A woman sitting at a cafe, starring at the window',
		filter: [GeneratedVideoFilter.ImageToVideo, GeneratedVideoFilter.Photorealism],
	},
	{
		videoUrl: `${minimaxVideo1Url}/02.mp4`,
		prompt: 'A jellyfish swimming through deep waters',
		filter: [GeneratedVideoFilter.ImageToVideo, GeneratedVideoFilter.Photorealism],
	},
	{
		videoUrl: `${minimaxVideo1Url}/03.mp4`,
		prompt: 'A humanoid rhino at an english pub, smoking a cigar',
		filter: [GeneratedVideoFilter.ImageToVideo, GeneratedVideoFilter.Character],
	},
	{
		videoUrl: `${minimaxVideo1Url}/04.mp4`,
		prompt: 'an animated scene shows a dog running through long grass',
		filter: [GeneratedVideoFilter.Animation],
	},
	{
		videoUrl: `${minimaxVideo1Url}/05.mp4`,
		prompt: 'a resplendent quetzal is sitting in a tree',
		filter: [GeneratedVideoFilter.Photorealism],
	},
	{
		videoUrl: `${minimaxVideo1Url}/06.mp4`,
		prompt: 'a calm and tranquil tropical beach scene, the waves are crashing on the shore',
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Landscape],
	},
	{
		videoUrl: `${minimaxVideo1Url}/07.mp4`,
		prompt: 'a woman is walking through a busy Tokyo street at night, she is wearing dark sunglasses',
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${minimaxVideo1Url}/08.mp4`,
		prompt: 'A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage. She wears a black leather jacket, a long red dress, and black boots, and carries a black purse.',
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
];

export const stableVideoDiffusionExamples: GeneratedVideoDetails[] = [];

export const klingV16StandardExamples: GeneratedVideoDetails[] = [
	{
		videoUrl: `${kling16Url}/01.mp4`,
		prompt: 'A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage. She wears a black leather jacket, a long red dress, and black boots, and carries a black purse.',
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${kling16Url}/02.mp4`,
		prompt: 'Snowflakes fall as a car moves forward along the road.',
		filter: [GeneratedVideoFilter.ImageToVideo, GeneratedVideoFilter.Photorealism],
	},
	{
		videoUrl: `${kling16Url}/03.mp4`,
		prompt: 'Teenager skateboarding through city rain, motion blur, cinematic shot, hyperrealistic photo, 8K, Canon EOS R5',
		parameters: { aspectRatio: '1:1', duration: 5 },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${kling16Url}/04.mp4`,
		prompt: 'Teenager skateboarding through city rain, motion blur, cinematic shot, hyperrealistic photo, 8K, Canon EOS R5',
		parameters: { aspectRatio: '1:1', duration: 5 },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${kling16Url}/05.mp4`,
		prompt: 'a portrait photo of a woman underwater with flowing hair',
		parameters: { aspectRatio: '16:9', duration: 5 },
		filter: [
			GeneratedVideoFilter.ImageToVideo,
			GeneratedVideoFilter.Portrait,
			GeneratedVideoFilter.Photorealism,
		],
	},
];

export const klingV16ProExamples: GeneratedVideoDetails[] = [
	{
		videoUrl: `${kling16ProUrl}/01.mp4`,
		prompt: 'Reflections in crystal mirrors, rainbow light, geometric world',
		parameters: { aspectRatio: '16:9', duration: 5 },
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
	{
		videoUrl: `${kling16ProUrl}/02.mp4`,
		prompt: 'A white human-like cat wears a red and blue Super Mario costume. She kneads a large ball of bread dough while flour floats in the air. The camera pans around the cat. The light comes through the window making the fur, costume and dough look more real.',
		parameters: { aspectRatio: '9:16', duration: 5 },
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
	{
		videoUrl: `${kling16ProUrl}/03.mp4`,
		prompt: "Create a realistic, heartwarming animation of a capybara relaxing in a bathtub, sitting upright with water rippling around it. The capybara's front paws are rubbing its round belly gently, mimicking the motions of washing itself. The actions should be smooth, slightly quick, and natural, as if the capybara is enjoying a self-cleaning bath. Ensure the setting includes soft, warm lighting, steam rising slightly from the water, and visible water droplets on its fur. The overall vibe should convey calmness and contentment.",
		parameters: { aspectRatio: '16:9', duration: 5 },
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
];

export const klingV2MasterExamples: GeneratedVideoDetails[] = [
	{
		videoUrl: `${kling2Url}/01.mp4`,
		prompt: 'slow-motion sequence captures the catastrophic implosion of a skyscraper, dust and debris billowing outwards in a chaotic ballet of destruction, while a haunting, orchestral score underscores the sheer power and finality of the event.',
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
	{
		videoUrl: `${kling2Url}/02.mp4`,
		prompt: "A close-up reveals a woman's delicate fingers gracefully holding a perfume bottle, its crystal facets catching the light, a slow, deliberate movement emphasizing the product's luxurious quality;  the scene is bathed in soft, warm lighting, creating an intimate and sensual atmosphere. Camera zoom out slowly.",
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
	{
		videoUrl: `${kling2Url}/03.mp4`,
		prompt: 'Under a vast, clear sky, a Shub-Niggurath shepherd in futuristic soldier gear and a shaved-sides haircut smiles and laughs faintly while casually posing near a cybernetics clinic set against a sandy, rocky landscape, a relaxed picnic spread laid out nearby.',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
	{
		videoUrl: `${kling2Url}/04.mp4`,
		prompt: 'A warm, golden light bathes a cozy bedroom where a fluffy Persian cat and a scruffy terrier sleep curled together on a plush bed, their gentle breaths creating a peaceful atmosphere; the scene unfolds in a slow, deliberate pace with the camera maintaining a steady position, focusing on the subtle movements and heartwarming intimacy shared as they drift off to sleep.',
		parameters: { aspectRatio: '9:16' },
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
	{
		videoUrl: `${kling2Url}/05.mp4`,
		prompt: 'A sweeping, romantic waltz unfolds under the warm glow of a thousand candlelit chandeliers, the bride and groom twirling effortlessly across the polished dance floor, their joy palpable and infectious.',
		parameters: { aspectRatio: '9:16' },
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
	{
		videoUrl: `${kling2Url}/06.mp4`,
		prompt: 'A sea of mud-caked faces, grim and determined, stretches before the towering figure of the commander, his weathered armor gleaming dully under a bruised, twilight sky;  the air itself vibrates with the unspoken tension of a thousand breaths held, punctuated by the rhythmic clang of steel and the low rumble of distant drums.',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
	{
		videoUrl: `${kling2Url}/07.mp4`,
		prompt: 'Sweat glistens on his chiseled jawline, the harsh studio lighting highlighting the intricate texture of his skin, a stark contrast to the deep shadows in his intense, focused eyes; a palpable tension hangs in the air.',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
	{
		videoUrl: `${kling2Url}/08.mp4`,
		prompt: 'Slowly facing camera, sweat glistens on her forehead, highlighting the sharp angles of her cheekbones against the backdrop of a vibrant, almost hyper-saturated sunset; a single, determined bead traces a path down her temple as her breath hitches, the raw exertion palpable in the close-up.',
		parameters: { aspectRatio: '9:16' },
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
	{
		videoUrl: `${kling2Url}/09.mp4`,
		prompt: 'Sunbeams dust motes dancing in the air of a warmly lit living room, illuminating the soft textures of a worn armchair where a mother and son share an intimate moment, reading a book together; the scene is filled with a quiet contentment, their laughter echoing softly and their eyes brimming with unspoken affection, punctuated by the occasional rustle of turning pages and the gentle clinking of teacups.',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
	{
		videoUrl: `${kling2Url}/10.mp4`,
		prompt: 'A cute koala slowly getting close to camera',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
	{
		videoUrl: `${kling2Url}/11.mp4`,
		prompt: 'aztecs sun-drenched pyramids rise against a vibrant turquoise sky, their stone surfaces textured with the passage of centuries, while intricately carved glyphs whisper stories of ancient rituals and human sacrifice, their meanings slowly revealed through a mesmerizing blend of historical fact and artistic interpretation.',
		parameters: { aspectRatio: '9:16' },
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
	{
		videoUrl: `${kling2Url}/12.mp4`,
		prompt: 'A lone figure stands silhouetted against a vast, grey canvas, the gentle patter of rain a constant, soothing rhythm;  the melancholic beauty of the scene is enhanced by the soft, diffused light filtering through the downpour, creating a world of muted colors and subtle textures, where each raindrop leaves its ephemeral mark on the glistening surfaces below.',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
	{
		videoUrl: `${kling2Url}/13.mp4`,
		prompt: "Dust motes dance in the fading sunlight filtering through the grimy windows of a forgotten garage, illuminating the worn leather of an old man's hands as they gently caress the chipped paint of a vintage roadster, its faded crimson a whisper of bygone glory.  A sense of quiet contemplation pervades the scene, the old man's face a roadmap of time etched by laughter and loss, mirroring the car's own patina of age and resilience, a shared history painted in hues of rust and memory.",
		parameters: { aspectRatio: '9:16' },
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
	{
		videoUrl: `${kling2Url}/14.mp4`,
		prompt: 'A cute hamster lies leisurely on a lifebuoy, wearing fashionable sunglasses, and drifts with the gentle waves on the shimmering sea surface. The hamster reclines comfortably, enjoying a peaceful and pleasant time. Cartoon style, the camera follows the subject moving, with a heartwarming and high picture quality.',
		parameters: { aspectRatio: '16:9' },
	},
	{
		videoUrl: `${kling2Url}/15.mp4`,
		prompt: 'Aerial view of the city with flying vehicles swiftly navigating through the neon-lit skyscrapers. the camera follows the subject moving',
		parameters: { aspectRatio: '16:9' },
	},
	{
		videoUrl: `${kling2Url}/16.mp4`,
		prompt: 'she lets down her hair and then puts on a baseball cap',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.ImageToVideo],
	},
	{
		videoUrl: `${kling2Url}/17.mp4`,
		prompt: 'A woman is talking directly to the camera, twitch live stream, pink and gold uplighting behind',
		parameters: { aspectRatio: '16:9' },
	},
];

export const lumaDreamMachineExamples: GeneratedVideoDetails[] = [
	{
		videoUrl: `${lumaDreamMachineUrl}/01.mp4`,
		prompt: 'A teddy bear in sunglasses playing electric guitar and dancing',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.Animation, GeneratedVideoFilter.Character],
	},
	{
		videoUrl: `${lumaDreamMachineUrl}/02.mp4`,
		prompt: 'Low-angle shot of a majestic tiger prowling through a snowy landscape, leaving paw prints on the white blanket',
		parameters: { aspectRatio: '16:9' },
		filter: [
			GeneratedVideoFilter.ImageToVideo,
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Landscape,
		],
	},
	{
		videoUrl: `${lumaDreamMachineUrl}/03.mp4`,
		prompt: 'Low-angle shot of a majestic tiger prowling through a snowy landscape, leaving paw prints on the white blanket',
		parameters: { aspectRatio: '16:9' },
		filter: [
			GeneratedVideoFilter.ImageToVideo,
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Landscape,
		],
	},
	{
		videoUrl: `${lumaDreamMachineUrl}/04.mp4`,
		prompt: 'a person going to musical world through cosmic musical pathway with music raining in background and magic light falling on the path hyper HD ultra Realistic fantasy cinematic dreamy',
		parameters: { aspectRatio: '4:3' },
		filter: [
			GeneratedVideoFilter.ImageToVideo,
			GeneratedVideoFilter.Surreal,
			GeneratedVideoFilter.Cinematic,
		],
	},
	{
		videoUrl: `${lumaDreamMachineUrl}/05.mp4`,
		prompt: 'a jellyfish swiming',
		parameters: { aspectRatio: '3:4' },
		filter: [GeneratedVideoFilter.ImageToVideo, GeneratedVideoFilter.Photorealism],
	},
	{
		videoUrl: `${lumaDreamMachineUrl}/06.mp4`,
		prompt: 'A dragon flying through cloudy skies, spitting fire',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.ImageToVideo, GeneratedVideoFilter.CGI],
	},
	{
		videoUrl: `${lumaDreamMachineUrl}/07.mp4`,
		prompt: 'Create a 5-second slow-motion video of a Formula 1 car navigating a challenging S-curve. Start with the car entering the frame, capture its precise line through the first bend, then follow as it transitions into the second curve. Show details of suspension compression, tire deformation, and airflow disruption. End with the car accelerating out of the turn, leaving a trail of disturbed air and rubber particles',
		parameters: { aspectRatio: '16:9' },
		filter: [
			GeneratedVideoFilter.ImageToVideo,
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Cinematic,
		],
	},
	{
		videoUrl: `${lumaDreamMachineUrl}/08.mp4`,
		prompt: "A majestic kangaroo stands poised on the edge of a sun-drenched Australian outback, its muscular frame accentuated by the golden light of a late afternoon. The kangaroo's rich reddish-brown fur glistens, contrasting with the deep greens of sparse bushland and the warm ochres of the dirt beneath its feet. It gazes into the distance, its large, expressive eyes reflecting curiosity and strength",
		parameters: { aspectRatio: '3:4' },
		filter: [
			GeneratedVideoFilter.ImageToVideo,
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Landscape,
		],
	},
	{
		videoUrl: `${lumaDreamMachineUrl}/09.mp4`,
		prompt: 'majestic waterfall scene with the distant castle. Begin with a close-up of the rushing water, then slowly pan upwards and zoom out to reveal the full scale of the falls. As the camera continues to pull back, show the misty environment and gradually bring the castle into view on the hilltop. End with a wide shot that captures both the dynamic waterfall and the mysterious castle, with subtle movement in the mist and trees. Include ambient sounds of the roaring water and gentle wind',
		parameters: { aspectRatio: '3:4' },
		filter: [
			GeneratedVideoFilter.ImageToVideo,
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Landscape,
		],
	},
	{
		videoUrl: `${lumaDreamMachineUrl}/10.mp4`,
		prompt: 'A silver-haired schoolgirl with violet eyes, walking through a cherry blossom park, her uniform and hair moving gently in the breeze. Show her turning to face the camera, her expression changing from thoughtful to determined, with a glowing butterfly fluttering around her and cherry blossoms falling in the background',
		parameters: { aspectRatio: '3:4' },
		filter: [
			GeneratedVideoFilter.ImageToVideo,
			GeneratedVideoFilter.Anime,
			GeneratedVideoFilter.Portrait,
		],
	},
	{
		videoUrl: `${lumaDreamMachineUrl}/11.mp4`,
		prompt: 'This video shows the majestic beauty of a waterfall cascading down a cliff into a serene lake. The waterfall, with its powerful flow, is the central focus of the video. The surrounding landscape is lush and green, with trees and foliage adding to the natural beauty of the scene',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Landscape],
	},
];

export const lumaRay2Examples: GeneratedVideoDetails[] = [
	{
		videoUrl: `${lumaRay2Url}/01.mp4`,
		prompt: 'A herd of wild horses galloping across a dusty desert plain under a blazing midday sun, their manes flying in the wind; filmed in a wide tracking shot with dynamic motion, warm natural lighting, and an epic.',
		parameters: { aspectRatio: '16:9', resolution: '540p', duration: '5s' },
		filter: [
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Cinematic,
			GeneratedVideoFilter.Landscape,
		],
	},
	{
		videoUrl: `${lumaRay2Url}/02.mp4`,
		prompt: 'A violinist performing on a rainy street at night, amber streetlights illuminating her and the violin, gentle rain falling around her and she is',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${lumaRay2Url}/03.mp4`,
		prompt: 'An overhead shot follows a vintage car winding through autumn-painted mountain roads, its polished surface reflecting the fiery canopy above. Fallen leaves swirl in its wake while sunlight filters through branches, creating a dappled dance of light across the hood.',
		parameters: { aspectRatio: '16:9' },
		filter: [
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Cinematic,
			GeneratedVideoFilter.Landscape,
		],
	},
	{
		videoUrl: `${lumaRay2Url}/04.mp4`,
		prompt: 'Gorilla surfing on a wave',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.Animation, GeneratedVideoFilter.Character],
	},
	{
		videoUrl: `${lumaRay2Url}/05.mp4`,
		prompt: 'insane camera flythrough of a turtle in an aquarium',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${lumaRay2Url}/06.mp4`,
		prompt: 'insane camera flythrough, a snow leopard crouched on a rocky ledge, staring directly at camera, snowflakes falling around it.',
		parameters: { aspectRatio: '16:9' },
		filter: [
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Cinematic,
			GeneratedVideoFilter.Landscape,
		],
	},
	{
		videoUrl: `${lumaRay2Url}/07.mp4`,
		prompt: 'insane camera flythrough, A miniature baby cat is walking and exploring on the surface of a fingertip',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${lumaRay2Url}/08.mp4`,
		prompt: 'A pair of hands skillfully slicing a perfectly cooked steak on a wooden cutting board, with faint steam rising from it',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Product],
	},
	{
		videoUrl: `${lumaRay2Url}/09.mp4`,
		prompt: 'Ship captain smokes a pipe, turns and looks at a looming storm in the distance',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${lumaRay2Url}/10.mp4`,
		prompt: 'A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage.',
		parameters: { aspectRatio: '16:9', resolution: '540p', duration: '5s' },
		filter: [
			GeneratedVideoFilter.ImageToVideo,
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Cinematic,
		],
	},
	{
		videoUrl: `${lumaRay2Url}/11.mp4`,
		prompt: 'Giant waterfall crashes down ancient cliffs, camera spirals through rainbow mist and crystal pools',
		parameters: { aspectRatio: '16:9', duration: '5s' },
		filter: [
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Cinematic,
			GeneratedVideoFilter.Landscape,
		],
	},
	{
		videoUrl: `${lumaRay2Url}/12.mp4`,
		prompt: 'Ice comet streaks through northern lights',
		parameters: { aspectRatio: '16:9', duration: '5s' },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${lumaRay2Url}/13.mp4`,
		prompt: 'Frantic camera weaves between boxers trading punches, lights flaring',
		parameters: { aspectRatio: '16:9', duration: '5s' },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${lumaRay2Url}/14.mp4`,
		prompt: 'Intense tracking shot navigates rugby scrum, mud and rain flying',
		parameters: { aspectRatio: '16:9', duration: '5s' },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${lumaRay2Url}/15.mp4`,
		prompt: 'Visceral camera work follows skydiver falling through clouds',
		parameters: { aspectRatio: '16:9', duration: '5s' },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${lumaRay2Url}/16.mp4`,
		prompt: 'A pirate ship in a raging sea',
		parameters: { aspectRatio: '16:9', duration: '5s' },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${lumaRay2Url}/17.mp4`,
		prompt: 'Insane camera flythrough of female surfer rideing massive wave at sunset, golden light streaming through the curl',
		parameters: { aspectRatio: '16:9', duration: '5s' },
		filter: [
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Cinematic,
			GeneratedVideoFilter.Landscape,
		],
	},
	{
		videoUrl: `${lumaRay2Url}/18.mp4`,
		prompt: 'Mind-bending dolly zoom on motocross rider soaring through sunset',
		parameters: { aspectRatio: '16:9', duration: '5s' },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${lumaRay2Url}/19.mp4`,
		prompt: 'Aggressive camera circles Russian ballet performance in grand setting',
		parameters: { aspectRatio: '16:9', duration: '5s' },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${lumaRay2Url}/20.mp4`,
		prompt: 'Chaotic camera ducks between breakdancers battling, spotlights pulsing overhead',
		parameters: { aspectRatio: '16:9', duration: '5s' },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
];

export const lumaRay2FlashExamples: GeneratedVideoDetails[] = [
	{
		videoUrl: `${lumaRay2FlashUrl}/01.mp4`,
		prompt: 'A herd of wild horses galloping across a dusty desert plain under a blazing midday sun, their manes flying in the wind; filmed in a wide tracking shot with dynamic motion, warm natural lighting, and an epic.',
		parameters: { aspectRatio: '16:9', resolution: '540p', duration: '5s' },
		filter: [
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Cinematic,
			GeneratedVideoFilter.Landscape,
		],
	},
	{
		videoUrl: `${lumaRay2FlashUrl}/02.mp4`,
		prompt: 'A stylish woman walks down a Tokyo street filled with warm glowing neon and animated city signage.',
		parameters: { aspectRatio: '16:9', resolution: '540p', duration: '5s' },
		filter: [
			GeneratedVideoFilter.ImageToVideo,
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Cinematic,
		],
	},
	{
		videoUrl: `${lumaRay2FlashUrl}/03.mp4`,
		prompt: 'A cinematic anime character intimate closeup, she is sitting at a cafe on a busy city street in the morning, drinking coffee, it is cold, a dynamic scene',
		parameters: { aspectRatio: '16:9', duration: '5s' },
		filter: [
			GeneratedVideoFilter.Anime,
			GeneratedVideoFilter.Cinematic,
			GeneratedVideoFilter.Portrait,
		],
	},
	{
		videoUrl: `${lumaRay2FlashUrl}/04.mp4`,
		prompt: 'A cinematic anime character intimate closeup, she is sitting at a cafe on a busy city street in the morning, it is cold',
		parameters: { aspectRatio: '16:9', duration: '5s' },
		filter: [
			GeneratedVideoFilter.Anime,
			GeneratedVideoFilter.Cinematic,
			GeneratedVideoFilter.Portrait,
		],
	},
	{
		videoUrl: `${lumaRay2FlashUrl}/05.mp4`,
		prompt: 'A snow leopard crouched on a rocky ledge, staring directly at camera, snowflakes falling around it',
		parameters: { aspectRatio: '16:9', duration: '5s' },
		filter: [
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Cinematic,
			GeneratedVideoFilter.Landscape,
		],
	},
	{
		videoUrl: `${lumaRay2FlashUrl}/06.mp4`,
		prompt: 'A polar bear swimming underwater',
		parameters: { aspectRatio: '16:9', duration: '5s' },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
];

export const veo2Examples: GeneratedVideoDetails[] = [
	{
		videoUrl: `${veo2Url}/01.mp4`,
		prompt: "The camera floats gently through rows of pastel-painted wooden beehives, buzzing honeybees gliding in and out of frame. The motion settles on the refined farmer standing at the center, his pristine white beekeeping suit gleaming in the golden afternoon light. He lifts a jar of honey, tilting it slightly to catch the light. Behind him, tall sunflowers sway rhythmically in the breeze, their petals glowing in the warm sunlight. The camera tilts upward to reveal a retro farmhouse with mint-green shutters, its walls dappled with shadows from swaying trees. Shot with a 35mm lens on Kodak Portra 400 film, the golden light creates rich textures on the farmer's gloves, marmalade jar, and weathered wood of the beehives.",
		parameters: { aspectRatio: '16:9', duration: '5s' },
		filter: [
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Cinematic,
			GeneratedVideoFilter.Product,
		],
	},
	{
		videoUrl: `${veo2Url}/02.mp4`,
		prompt: 'A lego chef cooking eggs',
		parameters: { duration: '5s' },
		filter: [
			GeneratedVideoFilter.ImageToVideo,
			GeneratedVideoFilter.Animation,
			GeneratedVideoFilter.Character,
		],
	},
	{
		videoUrl: `${veo2Url}/03.mp4`,
		prompt: 'Ghibli style a middle age woman mad at his father',
		filter: [
			GeneratedVideoFilter.ImageToVideo,
			GeneratedVideoFilter.Anime,
			GeneratedVideoFilter.Portrait,
		],
	},
	{
		videoUrl: `${veo2Url}/04.mp4`,
		prompt: 'a formula 1 car racing, facing front, with old tv scaline effects and the image glitches like an old VCR video',
		filter: [
			GeneratedVideoFilter.ImageToVideo,
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Cinematic,
		],
	},
	{
		videoUrl: `${veo2Url}/05.mp4`,
		prompt: 'A man on a skate park riding his skateboard.',
		parameters: { aspectRatio: '16:9' },
		filter: [
			GeneratedVideoFilter.ImageToVideo,
			GeneratedVideoFilter.Photorealism,
			GeneratedVideoFilter.Cinematic,
		],
	},
	{
		videoUrl: `${veo2Url}/06.mp4`,
		prompt: 'Animate it',
		parameters: { aspectRatio: '16:9' },
		filter: [GeneratedVideoFilter.ImageToVideo, GeneratedVideoFilter.Animation],
	},
	{
		videoUrl: `${veo2Url}/07.mp4`,
		prompt: 'a dog riding a skateboard',
		parameters: { aspectRatio: '16:9', duration: '5s' },
		filter: [GeneratedVideoFilter.Animation, GeneratedVideoFilter.Character],
	},
];

export const veo3Examples: GeneratedVideoDetails[] = [
	{
		videoUrl: `${veo3Url}/01.mp4`,
		prompt: 'First-person view soaring low over a medieval battlefield at dawn, gliding past clashing knights in armor, fire-lit arrows whizzing overhead, splintered catapults burning near fallen soldiers, flying inches above torn flags and mud-soaked ground, ambient sounds of swords striking, war cries, galloping hooves, and wind rushing in your ears, raw, terrifying, epic',
		parameters: { aspectRatio: '16:9', duration: '8s', generateAudio: true },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${veo3Url}/02.mp4`,
		prompt: 'A colossal, ancient library with impossibly high shelves, where books fly and pages turn on their own. Audio: the rustle of thousands of pages turning, the soft whoosh of flying books, distant, echoing whispers, a grand, magical orchestral piece with swirling harps and enchanted woodwinds.',
		parameters: { aspectRatio: '16:9', duration: '8s', generateAudio: true },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${veo3Url}/03.mp4`,
		prompt: "A luxury yatch sets sail in the Bosphorus Strait. As it gracefully navigates through the current, the Maiden's Tower comes into view. While continuing its journey, seagulls greet it. The sounds of waves and seagulls can be heard.",
		parameters: { aspectRatio: '16:9', duration: '8s', generateAudio: true },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Landscape],
	},
	{
		videoUrl: `${veo3Url}/04.mp4`,
		prompt: 'Alfred (calm, measured tone):\n"Batman, there\'s a roadblock ahead. You need to reroute."\nBatman (gruff, determined voice):\n"Copy that, Alfred. Taking the alternate path."',
		parameters: { aspectRatio: '16:9', duration: '8s', generateAudio: true },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${veo3Url}/05.mp4`,
		prompt: 'A male singer in his 30s with medium-length curly hair, a thin mustache, and a soulful gaze stands in a warm 1980s-style recording studio. He wears a loose open-collar shirt and softly sways while singing the lyric: "We are the prompts". The scene has a soft VHS grain filter, warm amber lighting, and is filmed in a vintage 4:3 aspect ratio. The cinematography mimics a music charity anthem from the 1980s, with a slow push-in toward the singer\'s face, highlighting his emotional expression.',
		parameters: { aspectRatio: '16:9', duration: '8s', generateAudio: true },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Portrait],
	},
	{
		videoUrl: `${veo3Url}/06.mp4`,
		prompt: "A cinematic handheld selfie-style video shot, showing a young man in ancient Middle Eastern robes with shoulder-length dark hair and a short beard, holding the camera like a selfie cam. He's inside a dimly lit stone den with rugged cave walls. Behind him, several large lions slowly pace or rest, casting shadows in the flickering torchlight. The man speaks directly into the camera he holds, slightly amused expression as he whispers.\nHe says: Alright, welcome to my crib. That one is asleep, he gets grouchy in the mornings.\n\nHe looks happy as he whispers, as if he knows a secret. He pans the camera to show the lions in the background behind him.\n\nThe light pours from the cave entrance above that illuminates the dark cave chamber with the lions below.\nTime of Day: day\nLens: ultra-wide selfie lens, shallow depth of field\nFilm Stock: vintage 35mm documentary style, selfie camera view\nAudio: (implied) ambient lion growls\nBackground: Lions sleeping behind the young man.",
		parameters: { aspectRatio: '16:9', duration: '8s', generateAudio: true },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${veo3Url}/07.mp4`,
		prompt: "the camera hangs back and ascends to a high angle. As a police car speeds fowards with it's lights on entering the frame. The camera finishes at rear tracking shot.",
		parameters: { aspectRatio: '16:9', duration: '8s', generateAudio: true },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${veo3Url}/08.mp4`,
		prompt: "A cyberpunk woman glides through neon-lit streets on a high-tech hover skateboard. Her cybernetic enhancements glow with electric blue circuits as she performs gravity-defying tricks between holographic advertisements. The city's towering skyscrapers reflect in her mirrored visor, while her augmented reality display shows optimal trick trajectories. Steam rises from street vents as she weaves through the crowded, rain-slicked streets of the future metropolis",
		parameters: { aspectRatio: '16:9', duration: '8s', generateAudio: true },
		filter: [GeneratedVideoFilter.CGI, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${veo3Url}/09.mp4`,
		prompt: 'gorilla riding a moped through busy italian city',
		parameters: { aspectRatio: '16:9', duration: '8s', generateAudio: true },
		filter: [GeneratedVideoFilter.Animation, GeneratedVideoFilter.Character],
	},
	{
		videoUrl: `${veo3Url}/10.mp4`,
		prompt: 'Bearded ancient philosopher in classical robes teaching wisdom to students in a marble garden setting, speaking with modern youthful language and expressions. The teacher gestures while sharing philosophical concepts using contemporary slang. Students in period clothing listen attentively. Warm natural lighting, classical architecture background, blending timeless wisdom with current speech pattern',
		parameters: { aspectRatio: '16:9', duration: '8s', generateAudio: true },
		filter: [GeneratedVideoFilter.Photorealism, GeneratedVideoFilter.Cinematic],
	},
	{
		videoUrl: `${veo3Url}/11.mp4`,
		prompt: 'Ultra-fast tracking shot through a sprawling futuristic cityscape where towering buildings are made of reflective organic chrome, glistening under a bright midday sun. Rainbow light flares and crystalline bokeh scatter across the frame as the camera dynamically weaves between structures. The sequence transitions into a seamless close-up zoom into a translucent chrome hive, where a highly detailed robotic worker bee is seen crafting with mechanical precision. The scene is rendered with hyperrealistic 4K clarity, soft lens depth, and ambient sci-fi audio humming in the background, evoking the mood of a high-budget cyber-futurist film.',
		parameters: { aspectRatio: '16:9', duration: '8s', generateAudio: true },
		filter: [GeneratedVideoFilter.CGI, GeneratedVideoFilter.Cinematic],
	},
];
