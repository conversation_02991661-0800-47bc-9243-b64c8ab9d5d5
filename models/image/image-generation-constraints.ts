import { ImageModelDisplayName } from './image-generation-models';

interface NumberConstraint {
	min: number;
	max: number;
	step?: number;
}

interface SizeConstraint {
	allowedSizes: `${number}x${number}`[];
}

interface AspectRatioConstraint {
	allowedRatios: `${number}:${number}`[];
}

interface StyleConstraint {
	value: string;
	label: string;
	description?: string;
	exampleImage?: string;
	colors?: string[];
}

interface ImageModelConstraints {
	n: NumberConstraint;
	size?: SizeConstraint;
	image_size?: string[];
	aspectRatio?: AspectRatioConstraint;
	style?: StyleConstraint[];
	color_palette?: StyleConstraint[];
	background?: StyleConstraint[];
	quality?: string[];
	num_inference_steps?: NumberConstraint;
	guidance_scale?: NumberConstraint;
}

export interface ImageGenerationParams {
	n?: number;
	style?: string;
	quality?: string;
	background?: string;
	aspectRatio?: `${number}:${number}`;
	size?: `${number}x${number}`;
	image_size?: string;
	color_palette?: string;
	num_inference_steps?: number;
	guidance_scale?: number;
}

// TODO: Limit number of images generated, post finalizing Business Model.
export const IMAGE_MODEL_CONSTRAINTS: Record<ImageModelDisplayName, ImageModelConstraints> = {
	[ImageModelDisplayName.GPT_Image_1]: {
		n: { min: 1, max: 10 },
		size: {
			allowedSizes: ['1024x1024', '1536x1024', '1024x1536'],
		},
		quality: ['low', 'medium', 'high'],
		background: [
			{
				value: 'transparent',
				label: 'Transparent',
				exampleImage:
					'https://pasiolwuajbjqtdrnueq.supabase.co/storage/v1/object/public/ai-generated-images/public/gpt-image-1/background_transparent.webp',
			},
			{
				value: 'opaque',
				label: 'Opaque',
				exampleImage:
					'https://pasiolwuajbjqtdrnueq.supabase.co/storage/v1/object/public/ai-generated-images/public/gpt-image-1/background_opaque.webp',
			},
		],
	},
	[ImageModelDisplayName.DALL_E_3]: {
		n: { min: 1, max: 1 },
		size: {
			allowedSizes: ['1024x1024', '1024x1792', '1792x1024'],
		},
		style: [
			{
				value: 'vivid',
				label: 'Vivid',
				exampleImage:
					'https://pasiolwuajbjqtdrnueq.supabase.co/storage/v1/object/public/ai-generated-images/public/dall-e-3/10.webp',
			},
			{
				value: 'natural',
				label: 'Natural',
				exampleImage:
					'https://pasiolwuajbjqtdrnueq.supabase.co/storage/v1/object/public/ai-generated-images/public/dall-e-3/09.webp',
			},
		],
		quality: ['standard', 'hd'],
	},
	[ImageModelDisplayName.FLUX_1_1_PRO]: {
		n: { min: 1, max: 4 },
		aspectRatio: {
			allowedRatios: ['9:21', '9:16', '2:3', '3:4', '1:1', '4:3', '3:2', '16:9', '21:9'],
		},
	},
	[ImageModelDisplayName.FLUX_KONTEXT_PRO]: {
		n: { min: 1, max: 1 }, // allows 4 but restricting to 1.
		aspectRatio: {
			allowedRatios: ['9:21', '9:16', '2:3', '3:4', '1:1', '4:3', '3:2', '16:9', '21:9'],
		},
	},
	[ImageModelDisplayName.Ideogram_V2]: {
		n: { min: 1, max: 1 },
		aspectRatio: {
			allowedRatios: [
				'1:3',
				'9:16',
				'10:16',
				'2:3',
				'3:4',
				'1:1',
				'4:3',
				'3:2',
				'16:10',
				'16:9',
				'3:1',
			],
		},
		style: [
			{
				value: 'auto',
				label: 'Auto',
				exampleImage:
					'https://pasiolwuajbjqtdrnueq.supabase.co/storage/v1/object/public/ai-generated-images/public/ideogram/styles/Auto.jpeg',
			},
			{
				value: 'general',
				label: 'General',
				exampleImage:
					'https://pasiolwuajbjqtdrnueq.supabase.co/storage/v1/object/public/ai-generated-images/public/ideogram/styles/general.jpeg',
			},
			{
				value: 'realistic',
				label: 'Realistic',
				exampleImage:
					'https://pasiolwuajbjqtdrnueq.supabase.co/storage/v1/object/public/ai-generated-images/public/ideogram/styles/realistic.webp',
			},
			{
				value: 'design',
				label: 'Design',
				exampleImage:
					'https://pasiolwuajbjqtdrnueq.supabase.co/storage/v1/object/public/ai-generated-images/public/ideogram/styles/design.webp',
			},
			{
				value: 'render_3D',
				label: '3D Render',
				exampleImage:
					'https://pasiolwuajbjqtdrnueq.supabase.co/storage/v1/object/public/ai-generated-images/public/ideogram/styles/3drender.webp',
			},
			{
				value: 'anime',
				label: 'Anime',
				exampleImage:
					'https://pasiolwuajbjqtdrnueq.supabase.co/storage/v1/object/public/ai-generated-images/public/ideogram/styles/anime.webp',
			},
		],
	},
	[ImageModelDisplayName.Ideogram_V3]: {
		n: { min: 1, max: 8, step: 1 },
		style: [
			{
				value: 'AUTO',
				label: 'Auto',
				description: 'Auto chooses the optimal style based on your prompt.',
			},
			{
				value: 'GENERAL',
				label: 'General',
				description:
					'General is a flexible style suitable for most uses, including artistic and abstract work.',
			},
			{
				value: 'REALISTIC',
				label: 'Realistic',
				description:
					'Realistic creates lifelike photography with authentic colors, textures and lighting.',
			},
			{
				value: 'DESIGN',
				label: 'Design',
				description: 'Design is ideal for vector-style art like logos, t-shirts, and more.',
			},
		],
		image_size: [
			'square_hd',
			'square',
			'portrait_4_3',
			'portrait_16_9',
			'landscape_4_3',
			'landscape_16_9',
		],
		// TODO: Show the colours in the palette in the UI.
		color_palette: [
			{
				value: 'EMBER',
				label: 'Ember',
				description: 'Warm, earthy colors with a touch of orange and red.',
				colors: ['#D62828', '#333C48', '#E6A26A', '#D46A6A', '#BC2C54'],
			},
			{
				value: 'FRESH',
				label: 'Fresh',
				description: 'Cool, refreshing colors with a touch of blue and green.',
				colors: ['#F7C98C', '#F27D52', '#3D61F2', '#A3F7A3', '#4D8BF2'],
			},
			{
				value: 'JUNGLE',
				label: 'Jungle',
				description: 'Rich, vibrant colors with a touch of green and brown.',
				colors: ['#0A2A2A', '#0D836F', '#1A5A3A', '#2D8B38', '#0F3D3D'],
			},
			{
				value: 'MAGIC',
				label: 'Magic',
				description: 'Enchanting colors with a touch of purple and gold.',
				colors: ['#ECCFF7', '#C8B0F2', '#3D6D8B', '#3D1C8B', '#F76B9B'],
			},
			{
				value: 'MELON',
				label: 'Melon',
				description: 'Bright, summery colors with a touch of yellow and orange.',
				colors: ['#F75D5D', '#9BF73D', '#C1F73D', '#F7B5A3', '#CC3D1C'],
			},
			{
				value: 'MOSAIC',
				label: 'Mosaic',
				description: 'Bold, graphic colors with a touch of black and white.',
				colors: ['#E63E73', '#325C8C', '#28B8A8', '#F7AA00', '#F76D00'],
			},
			{
				value: 'PASTEL',
				label: 'Pastel',
				description: 'Soft, muted colors with a touch of pink and blue.',
				colors: ['#F7B5C1', '#A8EBF7', '#FFF2D9', '#E6F7E6', '#E6C9F7'],
			},
			{
				value: 'ULTRAMARINE',
				label: 'Ultramarine',
				description: 'Deep, saturated colors with a touch of blue and purple.',
				colors: ['#1C2A38', '#0F2040', '#0A28F7', '#A3CED9', '#4D7DED'],
			},
		],
	},
	[ImageModelDisplayName.Stable_Diffusion_XL]: {
		n: { min: 1, max: 8 },
		size: {
			allowedSizes: ['512x512', '576x1024', '1024x576', '768x1024', '1024x768', '1024x1024'],
			// SD allows for custom sizes, but we're not supporting that in the UI.
		},
		num_inference_steps: { min: 1, max: 50, step: 1 },
		guidance_scale: { min: 1, max: 20, step: 0.5 },
	},
	[ImageModelDisplayName.Imagen_3]: { n: { min: 1, max: 4 } },
	[ImageModelDisplayName.Imagen_4]: { n: { min: 1, max: 4 } },
};

type SupportedFileType = {
	extension: string;
	description: string;
};

// TODO: Add correct image modality for each model
export const getSupportedImageTypesForImageModels = (
	model: ImageModelDisplayName
): SupportedFileType[] => {
	switch (model) {
		case ImageModelDisplayName.GPT_Image_1:
		case ImageModelDisplayName.Stable_Diffusion_XL:
		case ImageModelDisplayName.Ideogram_V2:
		case ImageModelDisplayName.Ideogram_V3:
		case ImageModelDisplayName.FLUX_1_1_PRO:
		case ImageModelDisplayName.FLUX_KONTEXT_PRO:
			return [
				{ extension: '.jpg', description: 'JPEG Image' },
				{ extension: '.jpeg', description: 'JPEG Image' },
				{ extension: '.png', description: 'PNG Image' },
				{ extension: '.webp', description: 'WebP Image' },
				{ extension: '.gif', description: 'GIF Image' },
				{ extension: '.avif', description: 'AVIF Image' },
			];

		default:
			return [];
	}
};
