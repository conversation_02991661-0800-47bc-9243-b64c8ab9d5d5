import { ModelTier } from '@/models/model-tiers';
import {
	GeneratedImageDetails,
	dallE3Examples,
	gptImage1Examples,
	stableDiffusionExamples,
	fluxExamples,
	ideogramV2Examples,
	ideogramV3Examples,
	imagen3Examples,
	imagen4Examples,
} from './image-generation-examples';

export enum ImageModelProvider {
	BlackForestLabs = 'BlackForestLabs',
	OpenAI = 'OpenAI',
	Google = 'Google',
	StabilityAI = 'Stability AI',
	Ideogram = 'Ideogram',
	XAI = 'Grok',
}

export enum ImageModelVendor {
	FalAI = 'FalAI',
	GoogleVertexAI = 'Google Vertex AI',
	OpenAI = 'OpenAI',
}

export enum ImageModelDisplayName {
	// Standard Tier (Tier 1)
	Stable_Diffusion_XL = 'Stable Diffusion XL',

	// Premium Tier (Tier 2)
	DALL_E_3 = 'DALL·E 3',
	GPT_Image_1 = 'GPT Image 1',
	Imagen_3 = 'Imagen 3',
	Imagen_4 = 'Imagen 4',
	FLUX_1_1_PRO = 'FLUX1.1 [pro]',
	FLUX_KONTEXT_PRO = 'FLUX.1 Kontext [pro]', // TODO: Add the max variant for best results.
	Ideogram_V2 = 'Ideogram 2.0',
	Ideogram_V3 = 'Ideogram 3.0',
}

export const IMAGE_MODEL_ENPOINTS: Record<ImageModelVendor, string[]> = {
	[ImageModelVendor.FalAI]: [
		'fal-ai/fast-sdxl',
		'fal-ai/fast-sdxl/image-to-image',
		'fal-ai/flux-pro/v1.1-ultra',
		'fal-ai/flux-pro/v1.1-ultra/redux',
		'fal-ai/flux-pro/kontext',
		'fal-ai/ideogram/v2',
		'fal-ai/ideogram/v2/remix',
		'fal-ai/ideogram/v3',
		'fal-ai/ideogram/v3/remix',
	],
	[ImageModelVendor.GoogleVertexAI]: [
		'imagen-3.0-capability-001',
		'imagen-3.0-fast-generate-001',
		'imagen-3.0-generate-002',
		'imagen-4.0-generate-preview-06-06',
	],
	[ImageModelVendor.OpenAI]: ['dall-e-3', 'gpt-image-1'],
};

export const getImageModelEndpoint = (
	model: ImageModelDisplayName,
	isEdit = false,
	isFast = false
) => {
	switch (model) {
		case ImageModelDisplayName.Stable_Diffusion_XL:
			return isEdit ? 'fal-ai/fast-sdxl/image-to-image' : 'fal-ai/fast-sdxl';
		case ImageModelDisplayName.DALL_E_3:
			return 'dall-e-3';
		case ImageModelDisplayName.GPT_Image_1:
			return 'gpt-image-1';
		case ImageModelDisplayName.Imagen_3:
			return isEdit
				? 'imagen-3.0-capability-001' // TODO: Request access on Vertex AI
				: isFast
					? 'imagen-3.0-fast-generate-001'
					: 'imagen-3.0-generate-002';
		case ImageModelDisplayName.Imagen_4:
			return 'imagen-4.0-generate-preview-06-06';
		case ImageModelDisplayName.FLUX_1_1_PRO:
			return isEdit ? 'fal-ai/flux-pro/v1.1-ultra/redux' : 'fal-ai/flux-pro/v1.1-ultra';
		case ImageModelDisplayName.FLUX_KONTEXT_PRO:
			return 'fal-ai/flux-pro/kontext';
		case ImageModelDisplayName.Ideogram_V2:
			return isEdit ? 'fal-ai/ideogram/v2/remix' : 'fal-ai/ideogram/v2';
		case ImageModelDisplayName.Ideogram_V3:
			return isEdit ? 'fal-ai/ideogram/v3/remix' : 'fal-ai/ideogram/v3';
	}
};

export const getImageModelDisplayName = (endpoint: string): ImageModelDisplayName | undefined => {
	switch (endpoint) {
		case 'fal-ai/fast-sdxl':
		case 'fal-ai/fast-sdxl/image-to-image':
			return ImageModelDisplayName.Stable_Diffusion_XL;
		case 'dall-e-3':
			return ImageModelDisplayName.DALL_E_3;
		case 'gpt-image-1':
			return ImageModelDisplayName.GPT_Image_1;
		case 'imagen-3.0-capability-001':
		case 'imagen-3.0-fast-generate-001':
		case 'imagen-3.0-generate-002':
			return ImageModelDisplayName.Imagen_3;
		case 'imagen-4.0-generate-preview-06-06':
			return ImageModelDisplayName.Imagen_4;
		case 'fal-ai/flux-pro/v1.1-ultra':
		case 'fal-ai/flux-pro/v1.1-ultra/redux':
			return ImageModelDisplayName.FLUX_1_1_PRO;
		case 'fal-ai/flux-pro/kontext':
			return ImageModelDisplayName.FLUX_KONTEXT_PRO;
		case 'fal-ai/ideogram/v2':
		case 'fal-ai/ideogram/v2/remix':
			return ImageModelDisplayName.Ideogram_V2;
		case 'fal-ai/ideogram/v3':
		case 'fal-ai/ideogram/v3/remix':
			return ImageModelDisplayName.Ideogram_V3;
		default:
			return undefined;
	}
};

export const imageModelDisplayNameToProviderMap: Record<ImageModelDisplayName, ImageModelProvider> =
	{
		[ImageModelDisplayName.Stable_Diffusion_XL]: ImageModelProvider.StabilityAI,
		[ImageModelDisplayName.DALL_E_3]: ImageModelProvider.OpenAI,
		[ImageModelDisplayName.GPT_Image_1]: ImageModelProvider.OpenAI,
		[ImageModelDisplayName.Imagen_3]: ImageModelProvider.Google,
		[ImageModelDisplayName.Imagen_4]: ImageModelProvider.Google,
		[ImageModelDisplayName.FLUX_1_1_PRO]: ImageModelProvider.BlackForestLabs,
		[ImageModelDisplayName.FLUX_KONTEXT_PRO]: ImageModelProvider.BlackForestLabs,
		[ImageModelDisplayName.Ideogram_V2]: ImageModelProvider.Ideogram,
		[ImageModelDisplayName.Ideogram_V3]: ImageModelProvider.Ideogram,
	};

export const imageModelExamples: Record<ImageModelDisplayName, GeneratedImageDetails[]> = {
	[ImageModelDisplayName.Stable_Diffusion_XL]: stableDiffusionExamples,
	[ImageModelDisplayName.DALL_E_3]: dallE3Examples,
	[ImageModelDisplayName.GPT_Image_1]: gptImage1Examples,
	[ImageModelDisplayName.Imagen_3]: imagen3Examples,
	[ImageModelDisplayName.Imagen_4]: imagen4Examples,
	[ImageModelDisplayName.FLUX_1_1_PRO]: fluxExamples,
	[ImageModelDisplayName.FLUX_KONTEXT_PRO]: [], // TODO: Add the image edit examples for flux kontext.
	[ImageModelDisplayName.Ideogram_V2]: ideogramV2Examples,
	[ImageModelDisplayName.Ideogram_V3]: ideogramV3Examples,
};

export interface ImageGenerationModel {
	key: ImageModelDisplayName;
	description: string;
	provider: ImageModelProvider;
	tier: ModelTier;
	imageExamples: GeneratedImageDetails[];
	disabled?: boolean;
}

export const ImageModelsList: ImageGenerationModel[] = [
	{
		key: ImageModelDisplayName.Stable_Diffusion_XL,
		description:
			'For high-fidelity art or photos in any style; skip it when you need flawless in-image text or the absolute fastest render times.',
		provider: ImageModelProvider.StabilityAI,
		tier: ModelTier.Standard,
		imageExamples: imageModelExamples[ImageModelDisplayName.Stable_Diffusion_XL],
	},
	{
		key: ImageModelDisplayName.DALL_E_3,
		description:
			'Best for turning wordy, imaginative prompts into coherent storybook-style or illustrative images with minimal tinkering; still fumbles on perfectly rendered in-image text or hyper-strict brand realism.',
		provider: ImageModelProvider.OpenAI,
		tier: ModelTier.Premium,
		imageExamples: imageModelExamples[ImageModelDisplayName.DALL_E_3],
	},
	{
		key: ImageModelDisplayName.GPT_Image_1,
		description: 'Great for iterative design workflows—at the cost of higher latency.',
		provider: ImageModelProvider.OpenAI,
		tier: ModelTier.Premium,
		imageExamples: imageModelExamples[ImageModelDisplayName.GPT_Image_1],
	},
	{
		key: ImageModelDisplayName.Imagen_3,
		description: 'Excels at photoreal or stylised art with quick turnaround.',
		provider: ImageModelProvider.Google,
		tier: ModelTier.Premium,
		imageExamples: imageModelExamples[ImageModelDisplayName.Imagen_3],
	},
	{
		key: ImageModelDisplayName.Imagen_4,
		description:
			'2K-resolution stunner with 10x draft speed and razor-sharp detail—ideal for print-ready ads or premium concept art.',
		provider: ImageModelProvider.Google,
		tier: ModelTier.Premium,
		imageExamples: imageModelExamples[ImageModelDisplayName.Imagen_4],
	},
	{
		key: ImageModelDisplayName.Ideogram_V2,
		description:
			'Choose this when your image must contain clean, readable text (logos, posters, memes); look elsewhere if ultimate photographic realism matters more than graphic design.',
		provider: ImageModelProvider.Ideogram,
		tier: ModelTier.Premium,
		imageExamples: imageModelExamples[ImageModelDisplayName.Ideogram_V2],
	},
	{
		key: ImageModelDisplayName.Ideogram_V3,
		description:
			"Adds flagship-level realism to Ideogram's best-in-class typography, great for polished marketing visuals.",
		provider: ImageModelProvider.Ideogram,
		tier: ModelTier.Premium,
		imageExamples: imageModelExamples[ImageModelDisplayName.Ideogram_V3],
	},
	{
		key: ImageModelDisplayName.FLUX_1_1_PRO,
		description:
			'Lightning-fast, photoreal text-to-image that tops quality leaderboards—perfect for product shots and cinematic concept art—yet still trails rivals on fine in-painting or typography work.',
		provider: ImageModelProvider.BlackForestLabs,
		tier: ModelTier.Premium,
		imageExamples: imageModelExamples[ImageModelDisplayName.FLUX_1_1_PRO],
	},
	{
		key: ImageModelDisplayName.FLUX_KONTEXT_PRO,
		description:
			'Handles both text and reference images as inputs, seamlessly enabling targeted, local edits and complex transformations of entire scenes.',
		provider: ImageModelProvider.BlackForestLabs,
		tier: ModelTier.Premium,
		imageExamples: imageModelExamples[ImageModelDisplayName.FLUX_KONTEXT_PRO],
		disabled: true,
	},
];
