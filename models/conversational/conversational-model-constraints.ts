import { ModelDisplayName } from './conversational-models';

export type ReasoningEffort = 'minimal' | 'low' | 'medium' | 'high';

interface NumberConstraint {
	min: number;
	max: number;
	step?: number;
}

interface ModelConstraints {
	temperature?: NumberConstraint;
	topP?: NumberConstraint;
	topK?: NumberConstraint;
	presencePenalty?: NumberConstraint;
	frequencyPenalty?: NumberConstraint;
	reasoningEffort?: ReasoningEffort[];
}

export interface ChatModelParams {
	// system?: string;
	// maxTokens?: number;
	temperature?: number;
	topP?: number;
	topK?: number;
	presencePenalty?: number;
	frequencyPenalty?: number;
	reasoningEffort?: ReasoningEffort;
	// stopSequences?: string[];
	// seed?: number;
}

export const getChatModelDefaultParams = (model: ModelDisplayName): ChatModelParams => {
	switch (model) {
		// Anthropic models
		case ModelDisplayName.Claude_4_Sonnet:
			return {
				temperature: 1,
			};

		// Deepseek models
		case ModelDisplayName.Deepseek_V3:
			return {
				temperature: 0.6,
				topP: 0.95,
				topK: 40,
				presencePenalty: 0,
				frequencyPenalty: 0,
			};
		case ModelDisplayName.Deepseek_R1:
			return {
				temperature: 0.7,
				topP: 0.95,
				topK: 40,
				presencePenalty: 0,
				frequencyPenalty: 0,
			};

		// Google Models
		case ModelDisplayName.Gemini_2_5_Flash:
		case ModelDisplayName.Gemini_2_5_Pro:
			return {
				temperature: 1,
				topP: 0.95,
			};

		// Meta Models
		case ModelDisplayName.Llama_4_Maverick:
			return {
				temperature: 0.7,
				topP: 0.95,
				topK: 40,
				presencePenalty: 0,
				frequencyPenalty: 0,
			};

		// Mistral Models
		case ModelDisplayName.Codestral:
		case ModelDisplayName.Mistral_Nemo:
		case ModelDisplayName.Mistral_Small:
			return {
				temperature: 0.7,
			};

		// OpenAI models
		case ModelDisplayName.o3:
		case ModelDisplayName.o4_mini:
		case ModelDisplayName.GPT_5_mini:
		case ModelDisplayName.GPT_5:
			return {
				temperature: 1,
				topP: 1,
				presencePenalty: 0,
				frequencyPenalty: 0,
			};

		// Grok Models
		case ModelDisplayName.Grok_3_mini:
		case ModelDisplayName.Grok_4:
			return {
				temperature: 1,
				topP: 1,
				// presencePenalty: 0,
				// frequencyPenalty: 0,
			};

		default:
			return {};
	}
};

export const CHAT_MODEL_CONSTRAINTS: Record<ModelDisplayName, ModelConstraints> = {
	// OpenAI models
	[ModelDisplayName.GPT_5_mini]: {
		temperature: { min: 0, max: 2, step: 0.01 },
		topP: { min: 0, max: 1, step: 0.05 },
		presencePenalty: { min: -2, max: 2, step: 0.1 },
		frequencyPenalty: { min: -2, max: 2, step: 0.1 },
		reasoningEffort: ['minimal', 'low', 'medium', 'high'],
	},
	[ModelDisplayName.GPT_5]: {
		temperature: { min: 0, max: 2, step: 0.01 },
		topP: { min: 0, max: 1, step: 0.05 },
		presencePenalty: { min: -2, max: 2, step: 0.1 },
		frequencyPenalty: { min: -2, max: 2, step: 0.1 },
		reasoningEffort: ['minimal', 'low', 'medium', 'high'],
	},
	[ModelDisplayName.o3]: {
		temperature: { min: 0, max: 2, step: 0.01 },
		topP: { min: 0, max: 1, step: 0.05 },
		presencePenalty: { min: -2, max: 2, step: 0.1 },
		frequencyPenalty: { min: -2, max: 2, step: 0.1 },
		reasoningEffort: ['low', 'medium', 'high'],
	},
	[ModelDisplayName.o4_mini]: {
		reasoningEffort: ['low', 'medium', 'high'],
	},
	// Anthropic models
	[ModelDisplayName.Claude_4_Sonnet]: {
		temperature: { min: 0, max: 1, step: 0.01 },
		topP: { min: 0, max: 1, step: 0.05 },
	},

	// Deepseek models
	[ModelDisplayName.Deepseek_V3]: {
		temperature: { min: 0, max: 2, step: 0.01 },
		topP: { min: 0, max: 1, step: 0.05 },
		topK: { min: 1, max: 100, step: 1 },
		presencePenalty: { min: -2, max: 2, step: 0.1 },
		frequencyPenalty: { min: -2, max: 2, step: 0.1 },
	},
	[ModelDisplayName.Deepseek_R1]: {
		temperature: { min: 0, max: 2, step: 0.01 },
		topP: { min: 0, max: 1, step: 0.05 },
		topK: { min: 1, max: 100, step: 1 },
		presencePenalty: { min: -2, max: 2, step: 0.1 },
		frequencyPenalty: { min: -2, max: 2, step: 0.1 },
	},

	// Google Models
	[ModelDisplayName.Gemini_2_5_Flash]: {
		temperature: { min: 0, max: 2, step: 0.01 },
		topP: { min: 0, max: 1, step: 0.05 },
		presencePenalty: { min: -1.99, max: 1.99, step: 0.1 },
		frequencyPenalty: { min: -1.99, max: 1.99, step: 0.1 },
	},
	[ModelDisplayName.Gemini_2_5_Pro]: {
		temperature: { min: 0, max: 2, step: 0.01 },
		topP: { min: 0, max: 1, step: 0.05 },
		presencePenalty: { min: -1.99, max: 1.99, step: 0.1 },
		frequencyPenalty: { min: -1.99, max: 1.99, step: 0.1 },
	},

	// Meta Models
	[ModelDisplayName.Llama_4_Maverick]: {
		temperature: { min: 0, max: 2, step: 0.01 },
		topP: { min: 0, max: 1, step: 0.05 },
		topK: { min: 1, max: 100, step: 1 },
		presencePenalty: { min: -2, max: 2, step: 0.1 },
		frequencyPenalty: { min: -2, max: 2, step: 0.1 },
	},

	// Mistral Models
	[ModelDisplayName.Codestral]: {
		temperature: { min: 0, max: 1.5, step: 0.01 },
		topP: { min: 0, max: 1, step: 0.05 },
		presencePenalty: { min: -2, max: 2, step: 0.1 },
		frequencyPenalty: { min: -2, max: 2, step: 0.1 },
	},
	[ModelDisplayName.Mistral_Nemo]: {
		temperature: { min: 0, max: 1.5, step: 0.01 },
		topP: { min: 0, max: 1, step: 0.05 },
		presencePenalty: { min: -2, max: 2, step: 0.1 },
		frequencyPenalty: { min: -2, max: 2, step: 0.1 },
	},
	[ModelDisplayName.Mistral_Small]: {
		temperature: { min: 0, max: 1.5, step: 0.01 },
		topP: { min: 0, max: 1, step: 0.05 },
		presencePenalty: { min: -2, max: 2, step: 0.1 },
		frequencyPenalty: { min: -2, max: 2, step: 0.1 },
	},

	// XAI Models
	[ModelDisplayName.Grok_3_mini]: {
		temperature: { min: 0, max: 2, step: 0.01 },
		topP: { min: 0, max: 1, step: 0.05 },
		presencePenalty: { min: -2, max: 2, step: 0.1 },
		frequencyPenalty: { min: -2, max: 2, step: 0.1 },
		reasoningEffort: ['low', 'medium', 'high'],
	},
	[ModelDisplayName.Grok_3]: {
		temperature: { min: 0, max: 2, step: 0.01 },
		topP: { min: 0, max: 1, step: 0.05 },
		presencePenalty: { min: -2, max: 2, step: 0.1 },
		frequencyPenalty: { min: -2, max: 2, step: 0.1 },
		reasoningEffort: ['low', 'medium', 'high'],
	},
	[ModelDisplayName.Grok_4]: {
		temperature: { min: 0, max: 2, step: 0.01 },
		topP: { min: 0, max: 1, step: 0.05 },
		presencePenalty: { min: -2, max: 2, step: 0.1 },
		frequencyPenalty: { min: -2, max: 2, step: 0.1 },
		reasoningEffort: ['low', 'medium', 'high'],
	},
} as const;

type SupportedFileType = {
	extension: string;
	description: string;
};

// TODO: File type conversion to URL to increase support for more extensions.
export function getSupportedFileTypes(model: ModelDisplayName): SupportedFileType[] {
	switch (model) {
		case ModelDisplayName.GPT_5_mini:
		case ModelDisplayName.GPT_5:
			return [
				// Text and Documents
				{ extension: '.txt', description: 'Plain Text File' },
				{ extension: '.pdf', description: 'PDF Document' },
				{ extension: '.json', description: 'JSON Data' },
				{ extension: '.md', description: 'Markdown File' },
				{ extension: '.html', description: 'HTML Webpage' },

				// Images
				{ extension: 'image/*', description: 'All Image Types' },
			];
		case ModelDisplayName.o3:
		case ModelDisplayName.o4_mini:
			return [
				// Text-only formats
				{ extension: '.txt', description: 'Plain Text File' },
				{ extension: '.pdf', description: 'PDF Document (Limited)' },
				{ extension: '.json', description: 'JSON Data' },
				{ extension: '.md', description: 'Markdown File' },
				{ extension: '.html', description: 'HTML Webpage' },
			];
		case ModelDisplayName.Gemini_2_5_Flash:
			return [
				// Common Data Formats
				{ extension: '.txt', description: 'Text File' },
				{ extension: '.json', description: 'JSON File' },
				{ extension: '.xml', description: 'XML File' },

				// Document Formats
				{ extension: '.pdf', description: 'PDF Document' },
				{ extension: '.rtf', description: 'Rich Text Format' },

				// Image Formats
				{ extension: 'image/*', description: 'All Image Types' },
			];
		case ModelDisplayName.Gemini_2_5_Pro:
			return [
				// Documents
				{ extension: '.pdf', description: 'PDF Document' },
				{ extension: '.txt', description: 'Text File' },
				{ extension: '.rtf', description: 'Rich Text Format' },
				{ extension: '.odt', description: 'OpenDocument Text' },

				// Images
				{ extension: 'image/*', description: 'All Image Types' },
			];
		case ModelDisplayName.Grok_4:
			return [
				// Images
				{ extension: 'image/*', description: 'All Image Types' },
			];

		// Models that don't support file uploads return empty array
		default:
			return [];
	}
}

export function supportsWebSearch(model: ModelDisplayName): boolean {
	switch (model) {
		// OpenAI
		case ModelDisplayName.GPT_5_mini:
		case ModelDisplayName.GPT_5:
		case ModelDisplayName.o3:
		case ModelDisplayName.o4_mini:

		// XAI
		case ModelDisplayName.Grok_3_mini:
		case ModelDisplayName.Grok_3:
		case ModelDisplayName.Grok_4:

		// Anthropic
		case ModelDisplayName.Claude_4_Sonnet:

		// Google Gemini
		case ModelDisplayName.Gemini_2_5_Flash:
		case ModelDisplayName.Gemini_2_5_Pro:

		// Mistral
		case ModelDisplayName.Mistral_Small:
			return true;
		default:
			return false;
	}
}
