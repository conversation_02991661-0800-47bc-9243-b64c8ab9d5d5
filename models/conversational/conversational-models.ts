import { ModelTier } from '@/models/model-tiers';

export enum Modes {
	GeneralPurpose = 'General Purpose',
	CreativeWriting = 'Creative Writing',
	Research = 'Research',
	SoftwareEngineering = 'Coding',
	Reasoning = 'Reasoning',
}

export enum ModelProvider {
	Anthropic = 'Anthropic',
	Deepseek = 'Deepseek',
	Google = 'Google',
	Meta = 'Meta',
	Mistral = 'Mistral AI',
	OpenAI = 'OpenAI',
	XAI = 'XAI',
}

export enum ModelDisplayName {
	// Anthropic
	Claude_4_Sonnet = 'Claude 4 Sonnet',

	// Deepseek
	Deepseek_V3 = 'Deepseek V3',
	Deepseek_R1 = 'Deepseek R1',

	// Google
	Gemini_2_5_Flash = 'Gemini 2.5 Flash',
	Gemini_2_5_Pro = 'Gemini 2.5 Pro',

	// Meta
	Llama_4_Maverick = 'Llama 4 Maverick',

	// Mistral
	Codestral = 'Codestral',
	Mistral_Nemo = 'Mistral Nemo',
	Mistral_Small = 'Mistral Small',

	// Open AI
	o3 = 'o3',
	o4_mini = 'o4 mini',
	GPT_5_mini = 'GPT 5 mini',
	GPT_5 = 'GPT 5',

	// Grok
	Grok_3_mini = 'Grok 3 mini',
	Grok_3 = 'Grok 3',
	Grok_4 = 'Grok 4',
}

export enum TextEmbeddingModel {
	OpenAI_SmallContext = 'text-embedding-3-small',
	OpenAI_LargeContext = 'text-embedding-3-large',
	Google = 'text-embedding-004',
	Mistral = 'mistral-embed',
}

export const modelDisplayNameToTechnicalNameMap: Record<ModelDisplayName, string> = {
	// Anthropic
	[ModelDisplayName.Claude_4_Sonnet]: 'claude-sonnet-4@20250514',

	// Deepseek
	[ModelDisplayName.Deepseek_R1]: 'deepseek-ai/DeepSeek-R1',
	[ModelDisplayName.Deepseek_V3]: 'deepseek-ai/DeepSeek-V3',

	// Google
	[ModelDisplayName.Gemini_2_5_Flash]: 'gemini-2.5-flash',
	[ModelDisplayName.Gemini_2_5_Pro]: 'gemini-2.5-pro',

	// Meta
	[ModelDisplayName.Llama_4_Maverick]: 'meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8',

	// Mistral
	[ModelDisplayName.Mistral_Small]: 'mistral-small-latest',
	[ModelDisplayName.Codestral]: 'codestral-latest',
	[ModelDisplayName.Mistral_Nemo]: 'open-mistral-nemo',

	// OpenAI
	[ModelDisplayName.o3]: 'o3',
	[ModelDisplayName.o4_mini]: 'o4-mini',
	[ModelDisplayName.GPT_5_mini]: 'gpt-5-mini',
	[ModelDisplayName.GPT_5]: 'gpt-5',

	// Grok
	[ModelDisplayName.Grok_3_mini]: 'grok-3-mini-latest',
	[ModelDisplayName.Grok_3]: 'grok-3-latest',
	[ModelDisplayName.Grok_4]: 'grok-4-latest',
};

export const modelDisplayNameToProviderMap: Record<ModelDisplayName, ModelProvider> = {
	// Anthropic
	[ModelDisplayName.Claude_4_Sonnet]: ModelProvider.Anthropic,

	// Deepseek
	[ModelDisplayName.Deepseek_R1]: ModelProvider.Deepseek,
	[ModelDisplayName.Deepseek_V3]: ModelProvider.Deepseek,

	// Google
	[ModelDisplayName.Gemini_2_5_Flash]: ModelProvider.Google,
	[ModelDisplayName.Gemini_2_5_Pro]: ModelProvider.Google,

	// Meta
	[ModelDisplayName.Llama_4_Maverick]: ModelProvider.Meta,

	// Mistral
	[ModelDisplayName.Mistral_Small]: ModelProvider.Mistral,
	[ModelDisplayName.Codestral]: ModelProvider.Mistral,
	[ModelDisplayName.Mistral_Nemo]: ModelProvider.Mistral,

	// OpenAI
	[ModelDisplayName.o3]: ModelProvider.OpenAI,
	[ModelDisplayName.o4_mini]: ModelProvider.OpenAI,
	[ModelDisplayName.GPT_5_mini]: ModelProvider.OpenAI,
	[ModelDisplayName.GPT_5]: ModelProvider.OpenAI,

	// Grok
	[ModelDisplayName.Grok_3_mini]: ModelProvider.XAI,
	[ModelDisplayName.Grok_3]: ModelProvider.XAI,
	[ModelDisplayName.Grok_4]: ModelProvider.XAI,
};

interface ModelCategoryInfo {
	[Modes.GeneralPurpose]?: string[];
	[Modes.CreativeWriting]?: string[];
	[Modes.Research]?: string[];
	[Modes.SoftwareEngineering]?: string[];
	[Modes.Reasoning]?: string[];
}

interface ModelSWEPerf {
	codeforces?: string;
	sweBenchVerified?: string;
}

export interface ConversationalModel {
	key: ModelDisplayName;
	categories: Modes[];
	tags: ModelCategoryInfo;
	facts: ModelCategoryInfo;
	swePerf?: ModelSWEPerf;
	provider: ModelProvider;
	tier: ModelTier;
	disabled?: boolean;
}

export const ConversationalModelsList: ConversationalModel[] = [
	// --- Standard Tier ---
	// OpenAI
	{
		key: ModelDisplayName.GPT_5_mini,
		categories: [Modes.GeneralPurpose, Modes.Reasoning],
		tags: {
			[Modes.GeneralPurpose]: [
				'✍️ Clear phrasing',
				'🌐 Broad topics',
				'✅ Reliable answers',
				'🚶 Moderate speed',
			],
			[Modes.Reasoning]: [
				'🧠 Basic reasoning',
				'🔍 Step-by-step logic',
				'💡 Problem solving',
				'⚡ Fast thinking',
			],
		},
		facts: {
			[Modes.GeneralPurpose]: [
				'Produces the clearest, most polished daily explanations.',
				'Handles broader knowledge queries than Flash or Mistral Small.',
				'Pick it when wording quality matters more than raw speed.',
			],
			[Modes.Reasoning]: [
				'Solid reasoning capabilities in a lightweight package.',
				'Good for basic math, logic puzzles, and step-by-step problem solving.',
				'Faster reasoning turnaround compared to larger models.',
			],
		},
		provider: ModelProvider.OpenAI,
		tier: ModelTier.Standard,
	},
	// Google
	{
		key: ModelDisplayName.Gemini_2_5_Flash,
		categories: [Modes.GeneralPurpose, Modes.CreativeWriting],
		tags: {
			[Modes.GeneralPurpose]: [
				'⚡ Instant answers',
				'🧠 Long chat memory',
				'📝 Great at summaries',
				'📷 Superior image understanding',
			],
			[Modes.CreativeWriting]: [
				'⚡ Rapid drafts',
				'🕸️ Keeps plot straight',
				'✂️ Quick edits',
				'🎵 Less lyrical',
			],
		},
		facts: {
			[Modes.GeneralPurpose]: [
				'Instant answers — fastest in this list for routine Q&A and summaries.',
				'Superior image understanding in Gemini compared to other models.',
				'Keeps track of long chats without losing context.',
				'Choose over others when speed outweighs depth.',
			],
			[Modes.CreativeWriting]: [
				'Drafts ideas fast—great for rapid story iterations.',
				'Maintains long plot lines better than Nemo.',
				'Pick it when you value speed over elaborate prose.',
			],
		},
		provider: ModelProvider.Google,
		tier: ModelTier.Standard,
	},
	// Mistral
	{
		key: ModelDisplayName.Codestral,
		categories: [Modes.SoftwareEngineering],
		tags: {
			[Modes.SoftwareEngineering]: [
				'✅ Accurate completions',
				'🧪 Writes tests',
				'🌐 80+ programming languages',
			],
		},
		facts: {
			[Modes.SoftwareEngineering]: [
				'Purpose-built coder: completes, refactors, and writes tests accurately.',
				'Recognises 80+ languages; strongest code precision here.',
				'Pick whenever the task is pure programming.',
			],
		},
		provider: ModelProvider.Mistral,
		tier: ModelTier.Standard,
	},
	{
		key: ModelDisplayName.Mistral_Nemo,
		categories: [Modes.CreativeWriting],
		tags: {
			[Modes.CreativeWriting]: [
				'🖌️ Rich detail',
				'🌍 Multilingual',
				'📚 Big context',
				'🐢 Slower pace',
			],
		},
		facts: {
			[Modes.CreativeWriting]: [
				'Fluent storyteller with strong multilingual flair.',
				'Excels at world-building and chapters in one go.',
				'Ideal for rich, extended narratives or non-English pieces.',
			],
		},
		provider: ModelProvider.Mistral,
		tier: ModelTier.Standard,
	},
	{
		key: ModelDisplayName.Mistral_Small,
		categories: [Modes.GeneralPurpose],
		tags: {
			[Modes.GeneralPurpose]: [
				'🔍 Quick lookup',
				'⚡ Snappy response',
				'🎯 Stays on topic',
				'🌊 Shallow depth',
			],
		},
		facts: {
			[Modes.GeneralPurpose]: [
				'All-rounder for quick look-ups and basic tasks.',
				'Good at staying on topic with minimal resources.',
				"Use when you need a snappy reply and don't require rich detail.",
			],
		},
		provider: ModelProvider.Mistral,
		tier: ModelTier.Standard,
	},
	// XAI
	{
		key: ModelDisplayName.Grok_3_mini,
		categories: [Modes.GeneralPurpose, Modes.Reasoning],
		tags: {
			[Modes.GeneralPurpose]: [
				'🪜 Step-by-step logic',
				'🧩 Problem breakdown',
				'🗣️ Reasoned replies',
				'🐢 Slower than Flash',
			],
			[Modes.Reasoning]: [
				'🪜 Transparent steps',
				'💡 Logical clarity',
				'🧠 Lightweight reasoning',
				'🎋 Limited depth',
			],
		},
		facts: {
			[Modes.GeneralPurpose]: [
				'Breaks problems into steps for logic-heavy everyday questions.',
				'Slightly deeper reasoning than Flash or Mistral Small.',
				'Best when you want a short, explained answer rather than just facts.',
			],
			[Modes.Reasoning]: [
				'Provides transparent reasoning chains for tough questions.',
				'Slightly deeper logic than o3, lighter than full Grok 3.',
				'Best when you need clarity on how an answer is reached.',
			],
		},
		provider: ModelProvider.XAI,
		tier: ModelTier.Standard,
	},
	// Deepseek
	{
		key: ModelDisplayName.Deepseek_V3,
		categories: [Modes.CreativeWriting],
		tags: {
			[Modes.CreativeWriting]: [
				'📚 Fact-based fiction',
				'🏗️ Structured narration',
				'🔄 Logical flow',
				'🌿 Subtle style',
			],
		},
		facts: {
			[Modes.CreativeWriting]: [
				'Balances facts with creativity—great for informative fiction.',
				'Keeps plots logically sound compared to flashier models.',
				'Best when accuracy and coherence matter as much as style.',
			],
		},
		provider: ModelProvider.Deepseek,
		tier: ModelTier.Standard,
	},
	// Meta
	{
		key: ModelDisplayName.Llama_4_Maverick,
		categories: [Modes.CreativeWriting, Modes.Research],
		tags: {
			[Modes.CreativeWriting]: [
				'✨ Inventive plots',
				'🎭 Consistent voices',
				'🖼️ Image blending',
				'📜 Long context',
			],
			[Modes.Research]: [
				'📚 Book-length input',
				'✂️ Clear summaries',
				'🗂️ Multi-source compare',
				'🤝 Needs guidance',
			],
		},
		facts: {
			[Modes.CreativeWriting]: [
				'Highly imaginative; strong at consistent character voices.',
				'Multimodal input lets you blend image descriptions into stories.',
				'Choose for complex, experimental fiction.',
			],
			[Modes.Research]: [
				'Swallows book-length input and returns clear multi-point summaries.',
				'Open model for deep dives, summarising, cross-doc links.',
				'Reasoning solid, though not quite top proprietary tier.',
			],
		},
		provider: ModelProvider.Meta,
		tier: ModelTier.Standard,
	},

	// --- Premium Tier ---
	// OpenAI
	{
		key: ModelDisplayName.GPT_5,
		categories: [
			Modes.GeneralPurpose,
			Modes.CreativeWriting,
			Modes.Research,
			Modes.Reasoning,
			Modes.SoftwareEngineering,
		],
		tags: {
			[Modes.GeneralPurpose]: [
				'✍️ Clear phrasing',
				'🌐 Broad topics',
				'✅ Reliable answers',
				'🚀 Fast responses',
			],
			[Modes.CreativeWriting]: [
				'✍️ High-quality drafts',
				'🎨 Follows style cues',
				'🚀 Fast turnaround',
				'💡 Creative ideas',
			],
			[Modes.Research]: [
				'⚖️ Balanced reports',
				'🧠 Nuanced conclusions',
				'🗂️ Large context',
				'🚶 Moderate speed',
			],
			[Modes.Reasoning]: [
				'🧠 Logical thinking',
				'🔍 Step-by-step analysis',
				'💡 Problem solving',
				'🎯 Clear explanations',
			],
			[Modes.SoftwareEngineering]: [
				'⌨️ Production-ready code',
				'🎯 Follows instructions',
				'💬 Explains errors',
				'🌐 Multi-language reliable',
			],
		},
		facts: {
			[Modes.GeneralPurpose]: [
				'Excellent for everyday conversations and general queries.',
				'Handles a wide range of topics with clear, helpful responses.',
				'Perfect for general chat, explanations, and daily assistance.',
			],
			[Modes.CreativeWriting]: [
				'Near-flagship writing quality with excellent performance.',
				'Follows tone instructions well; solid for most creative needs.',
				'Good default for high-quality everyday writing and drafts.',
			],
			[Modes.Research]: [
				'Delivers balanced, well-structured reports on lengthy material.',
				'Strong at drawing nuanced conclusions.',
				'Use when you need dependable depth without extreme context sizes.',
			],
			[Modes.Reasoning]: [
				'Excellent at breaking down complex problems into logical steps.',
				'Provides clear explanations for mathematical and logical reasoning.',
				'Strong performance on reasoning tasks with detailed step-by-step solutions.',
			],
			[Modes.SoftwareEngineering]: [
				'Generates production-ready functions and clear code explanations.',
				'Top performer at guided code reviews and error fixes.',
				'Default pick when you need high reliability across languages.',
			],
		},
		swePerf: {
			sweBenchVerified: '74.9%',
		},
		provider: ModelProvider.OpenAI,
		tier: ModelTier.Premium,
	},
	{
		key: ModelDisplayName.o3,
		categories: [Modes.Research, Modes.SoftwareEngineering, Modes.Reasoning],
		tags: {
			[Modes.Research]: [
				'🪜 Methodical analysis',
				'🛠️ Tool-assisted checks',
				'🎯 High accuracy',
				'⏳ Takes time',
			],
			[Modes.SoftwareEngineering]: [
				'🎓 Senior developer',
				'🧪 Runs code tests',
				'🐞 Handles tricky bugs',
				'✂️ Partial outputs',
			],
			[Modes.Reasoning]: [
				'🧠 Expert-level logic',
				'🛠️ Tool-driven proofs',
				'🎯 High accuracy',
				'⏳ Slower',
			],
		},
		facts: {
			[Modes.Research]: [
				'Reliable summariser for mid-length texts and everyday research.',
				'Follows instructions cleanly, less prone to tangents than older reasoning models.',
				"Best for straightforward analysis where giant context isn't required.",
			],
			[Modes.SoftwareEngineering]: [
				'Works like a senior dev: analyses requirements, plans changes, and double-checks logic instead of rushing code.',
				'Runs its own Python tests to verify fixes and catches edge-case bugs that stump most models; so output code usually works on first try.',
				'Prefers a step-wise approach—may hand you partial code and ask you to run checks, while Gemini 2.5 Pro often dumps the full snippet in one shot.',
				'Responds more slowly than lighter models, but its careful reasoning prevents follow-up debugging.',
			],
			[Modes.Reasoning]: [
				'Excels at multi-step reasoning ~ scores at an expert level on hard math/logic problems by working methodically step-by-step instead of jumping to an answer.',
				'Actively pulls live data or runs code and image analyses to ground answers in fresh evidence.',
				'Delivers the most error-free solutions in this list, sacrificing speed for depth and accuracy.',
			],
		},
		swePerf: {
			sweBenchVerified: '69.1%',
			codeforces: '2706',
		},
		provider: ModelProvider.OpenAI,
		tier: ModelTier.Premium,
	},
	{
		key: ModelDisplayName.o4_mini,
		categories: [Modes.SoftwareEngineering, Modes.Reasoning],
		tags: {
			[Modes.SoftwareEngineering]: [
				'⚡ Fast algorithm help',
				'🪜 Step logic',
				'🐞 Everyday debugging',
				'📏 Short answers',
			],
			[Modes.Reasoning]: [
				'🧠 Quick reasoning',
				'🪜 Step logic',
				'🧩 Good for puzzles',
				'🌊 Moderate depth',
			],
		},
		facts: {
			[Modes.SoftwareEngineering]: [
				'Solves algorithms step-by-step faster than bigger peers.',
				'Sweet spot for everyday coding help without waiting.',
				'Great for debugging or leetCode-style challenges.',
			],
			[Modes.Reasoning]: [
				'Works through puzzles step-by-step with concise logic.',
				'Faster turnaround than larger reasoning models.',
				'Pick for quick yet solid math or logic solutions.',
			],
		},
		swePerf: {
			sweBenchVerified: '68.1%',
			codeforces: '2719',
		},
		provider: ModelProvider.OpenAI,
		tier: ModelTier.Premium,
	},
	// Google
	{
		key: ModelDisplayName.Gemini_2_5_Pro,
		categories: [
			Modes.CreativeWriting,
			Modes.Research,
			Modes.SoftwareEngineering,
			Modes.Reasoning,
		],
		tags: {
			[Modes.CreativeWriting]: [
				'🗺️ Epic world-building',
				'🧩 Logical structure',
				'🏛️ Huge context',
				'⚖️ Measured tone',
			],
			[Modes.Research]: [
				'🌌 Massive window',
				'🔍 Deep cross-analysis',
				'🧐 Careful reasoning',
				'🐢 Deliberate pace',
			],
			[Modes.SoftwareEngineering]: [
				'💡 Architecture insights',
				'🐞 Complex bug hunt',
				'🗂️ Huge code context',
				'🚶 Steady pace',
			],
			[Modes.Reasoning]: [
				'🧩 Systematic thinking',
				'🎬 Scenario analysis',
				'🧠 Long-chain reasoning',
				'🚶 Steady pace',
			],
		},
		facts: {
			[Modes.CreativeWriting]: [
				'Superb at long-form world-building and structural consistency.',
				'Injects analytical depth into creative pieces better than others.',
				'Pick it for epic projects needing both imagination and logic.',
			],
			[Modes.Research]: [
				'Handles the biggest document sets here with careful reasoning.',
				'Excels at cross-document comparisons and deep dives.',
				'Choose for the hardest analytical questions spanning many sources.',
			],
			[Modes.SoftwareEngineering]: [
				'Deep understanding of complex system architectures.',
				'Excels at analysing intertwined components across big codebases.',
				'Choose for tough design reviews or tricky logical bugs.',
			],
			[Modes.Reasoning]: [
				'Systematically tackles multi-layer problems and "what-if" scenarios.',
				'Maintains full rationale on long reasoning chains.',
				'Ideal for strategic planning or complex data questions.',
			],
		},
		swePerf: {
			sweBenchVerified: '63.2%',
		},
		provider: ModelProvider.Google,
		tier: ModelTier.Premium,
	},
	// Anthropic
	{
		key: ModelDisplayName.Claude_4_Sonnet,
		categories: [Modes.SoftwareEngineering],
		tags: {
			[Modes.SoftwareEngineering]: [
				'🔧 End-to-end fixes',
				'💬 Clear explanations',
				'📂 Large code context',
				'⏳ Not fastest',
			],
		},
		facts: {
			[Modes.SoftwareEngineering]: [
				'Excels at end-to-end issue resolution on live GitHub repos.',
				'72% on SWE-bench - scores top on real-world bug-fix benchmarks.',
				'Choose when you want thorough fixes plus clean, human-style explanations.',
			],
		},
		swePerf: {
			sweBenchVerified: '72.7%',
		},
		provider: ModelProvider.Anthropic,
		tier: ModelTier.Premium,
		disabled: true,
	},
	// XAI
	{
		key: ModelDisplayName.Grok_3,
		categories: [Modes.Reasoning],
		tags: {
			[Modes.Reasoning]: [
				'💡 Clever reasoning',
				'🗣️ Transparent chain-of-thought',
				'🧠 Creative flair',
			],
		},
		facts: {
			[Modes.Reasoning]: [
				'Strongest "think aloud" reasoning here; finds clever solutions.',
				'Great for intricate puzzles or strategy tasks.',
				'Choose when you want maximum transparency and depth together.',
			],
		},
		provider: ModelProvider.XAI,
		tier: ModelTier.Premium,
		disabled: true,
	},
	{
		key: ModelDisplayName.Grok_4,
		categories: [Modes.Reasoning],
		// TODO: Update with Grok 4 tags and facts.
		tags: {
			[Modes.Reasoning]: [
				'💡 Clever reasoning',
				'🗣️ Transparent chain-of-thought',
				'🧠 Creative flair',
			],
		},
		facts: {
			[Modes.Reasoning]: [
				'Strongest "think aloud" reasoning here; finds clever solutions.',
				'Great for intricate puzzles or strategy tasks.',
				'Choose when you want maximum transparency and depth together.',
			],
		},
		provider: ModelProvider.XAI,
		tier: ModelTier.Premium,
	},
	// Deepseek
	{
		key: ModelDisplayName.Deepseek_R1,
		categories: [Modes.Reasoning],
		tags: {
			[Modes.Reasoning]: [
				'➕ Math powerhouse',
				'📝 Structured proofs',
				'💡 Logical clarity',
				'🪶 Less polished prose',
			],
		},
		facts: {
			[Modes.Reasoning]: [
				'A powerhouse for math and logic challenges.',
				'Often matches proprietary giants in transparent step-by-step answers.',
				'Best for advanced problem-solving without platform lock-in.',
			],
		},
		provider: ModelProvider.Deepseek,
		tier: ModelTier.Premium,
	},
];
