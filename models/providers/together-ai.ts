import { togetherai } from '@ai-sdk/togetherai';
import {
	wrapLanguageModel,
	extractReasoningMiddleware,
	customProvider,
	convertToCoreMessages,
	streamText,
	Message,
	defaultSettingsMiddleware,
} from 'ai';
import {
	ModelDisplayName,
	modelDisplayNameToTechnicalNameMap,
} from '@/models/conversational/conversational-models';
import { onFinishCallback } from '@/types/ai-sdk';
import {
	getAddResourceTool,
	getInformationTool,
	getWebpageScraperTool,
	getWebSearchTool,
} from '../tools';
import { BASE_GUIDELINE } from '../model-guidelines';
import {
	ChatModelParams,
	getChatModelDefaultParams,
} from '../conversational/conversational-model-constraints';

const wrapTogetherAILanguageModelInMiddleware = (model: ModelDisplayName) =>
	wrapLanguageModel({
		model: togetherai(modelDisplayNameToTechnicalNameMap[model]),
		middleware: [
			defaultSettingsMiddleware({
				settings: getChatModelDefaultParams(model),
			}),
		],
	});

// Special handling for Deepseek R1 with reasoning capabilities
const enhancedDeepseekR1 = wrapLanguageModel({
	model: togetherai(modelDisplayNameToTechnicalNameMap[ModelDisplayName.Deepseek_R1], {
		simulateStreaming: false,
	}),
	middleware: [
		defaultSettingsMiddleware({
			settings: getChatModelDefaultParams(ModelDisplayName.Deepseek_R1),
		}),
		extractReasoningMiddleware({ tagName: 'think' }),
	],
});

export const togetherAI = customProvider({
	languageModels: {
		[ModelDisplayName.Deepseek_R1]: enhancedDeepseekR1,
		[ModelDisplayName.Deepseek_V3]: wrapTogetherAILanguageModelInMiddleware(
			ModelDisplayName.Deepseek_V3
		),
		[ModelDisplayName.Llama_4_Maverick]: wrapTogetherAILanguageModelInMiddleware(
			ModelDisplayName.Llama_4_Maverick
		),
	},
	// TODO: Add Together AI text embedding models, if required.
	textEmbeddingModels: {},
	imageModels: {},
});

export const streamTextResponseFromTogetherAI = (
	userId: string,
	messages: Message[],
	model: ModelDisplayName,
	isWebSearchEnabled = false,
	location: {
		country_code: string;
		city: string;
		languages: string;
	},
	params: ChatModelParams = {},
	onFinish?: onFinishCallback,
	abortSignal?: AbortSignal,
	guideline = BASE_GUIDELINE
) => {
	const languageModel = togetherAI.languageModel(model);

	return streamText({
		system: guideline,
		model: languageModel,
		messages: convertToCoreMessages(messages),
		...params,
		onFinish,
		// Deepseek and Llama do not support tool calling.
		tools: {
			// addResource: getAddResourceTool(userId),
			// getInformation: getInformationTool(userId),
			// ...(isWebSearchEnabled
			// 	? {
			// 			useWebSearch: getWebSearchTool(),
			// 			scrapeWebpages: getWebpageScraperTool(),
			// 		}
			// 	: {}),
		},
		toolCallStreaming: true,
		maxSteps: 5,
		abortSignal,
	});
};
