import { mistral } from '@ai-sdk/mistral';
import {
	customProvider,
	convertToCoreMessages,
	streamText,
	Message,
	wrapLanguageModel,
	defaultSettingsMiddleware,
} from 'ai';
import {
	ModelDisplayName,
	modelDisplayNameToTechnicalNameMap,
	TextEmbeddingModel,
} from '@/models/conversational/conversational-models';
import { onFinishCallback } from '@/types/ai-sdk';
import {
	getAddResourceTool,
	getInformationTool,
	getWebpageScraperTool,
	getWebSearchTool,
} from '../tools';
import { BASE_GUIDELINE } from '../model-guidelines';
import {
	ChatModelParams,
	getChatModelDefaultParams,
} from '../conversational/conversational-model-constraints';

const wrapMistralLanguageModelInMiddleware = (model: ModelDisplayName) =>
	wrapLanguageModel({
		model: mistral(modelDisplayNameToTechnicalNameMap[model], { safePrompt: true }),
		middleware: [
			defaultSettingsMiddleware({
				settings: getChatModelDefaultParams(model),
			}),
		],
	});

export const mistralAI = customProvider({
	languageModels: {
		[ModelDisplayName.Codestral]: wrapMistralLanguageModelInMiddleware(
			ModelDisplayName.Codestral
		),
		[ModelDisplayName.Mistral_Nemo]: wrapMistralLanguageModelInMiddleware(
			ModelDisplayName.Mistral_Nemo
		),
		[ModelDisplayName.Mistral_Small]: wrapMistralLanguageModelInMiddleware(
			ModelDisplayName.Mistral_Small
		),
	},
	textEmbeddingModels: {
		[TextEmbeddingModel.Mistral]: mistral.textEmbeddingModel(TextEmbeddingModel.Mistral),
	},
	imageModels: {},
});

export const streamTextResponseFromMistral = (
	userId: string,
	messages: Message[],
	model: ModelDisplayName,
	isWebSearchEnabled = false,
	location: {
		country_code: string;
		city: string;
		languages: string;
	},
	params: ChatModelParams = {},
	onFinish?: onFinishCallback,
	abortSignal?: AbortSignal,
	guideline = BASE_GUIDELINE
) => {
	const languageModel = mistralAI.languageModel(model);
	// Only Open AI's text embedding model is used for the time being.
	// const textEmbeddingModel = mistralAI.textEmbeddingModel(TextEmbeddingModel.Mistral);

	return streamText({
		system: guideline,
		model: languageModel,
		messages: convertToCoreMessages(messages),
		...params,
		onFinish,
		tools: {
			// addResource: getAddResourceTool(userId),
			// getInformation: getInformationTool(userId),
			...(isWebSearchEnabled
				? {
						useWebSearch: getWebSearchTool(location),
						scrapeWebpages: getWebpageScraperTool(),
					}
				: {}),
		},
		toolCallStreaming: true,
		maxSteps: 5,
		abortSignal,
	});
};
