import { xai } from '@ai-sdk/xai';
import {
	customProvider,
	convertToCoreMessages,
	streamText,
	Message,
	experimental_generateImage as generateImage,
	NoImageGeneratedError,
	defaultSettingsMiddleware,
	wrapLanguageModel,
} from 'ai';
import {
	ModelDisplayName,
	modelDisplayNameToTechnicalNameMap,
} from '@/models/conversational/conversational-models';
import { onFinishCallback } from '@/types/ai-sdk';
import { getWebpageScraperTool, getWebSearchTool } from '../tools';
import { BASE_GUIDELINE } from '../model-guidelines';
import {
	ChatModelParams,
	getChatModelDefaultParams,
} from '../conversational/conversational-model-constraints';

const wrapXAILanguageModelInMiddleware = (model: ModelDisplayName) =>
	wrapLanguageModel({
		model: xai(modelDisplayNameToTechnicalNameMap[model]),
		middleware: [
			defaultSettingsMiddleware({
				settings: getChatModelDefaultParams(model),
			}),
		],
	});

export const xAI = customProvider({
	languageModels: {
		[ModelDisplayName.Grok_3_mini]: wrapXAILanguageModelInMiddleware(
			ModelDisplayName.Grok_3_mini
		),
		[ModelDisplayName.Grok_3]: wrapXAILanguageModelInMiddleware(ModelDisplayName.Grok_3),
		[ModelDisplayName.Grok_4]: wrapXAILanguageModelInMiddleware(ModelDisplayName.Grok_4),
	},
});

export const streamTextResponseFromGrokAI = (
	userId: string,
	messages: Message[],
	model: ModelDisplayName,
	isWebSearchEnabled = false,
	location: {
		country_code: string;
		city: string;
		languages: string;
	},
	params: ChatModelParams = {},
	onFinish?: onFinishCallback,
	abortSignal?: AbortSignal,
	guideline = BASE_GUIDELINE
) => {
	const languageModel = xAI.languageModel(model);

	return streamText({
		system: guideline,
		model: languageModel,
		messages: convertToCoreMessages(messages),
		...params,
		onFinish,
		tools: {
			...(isWebSearchEnabled
				? {
						useWebSearch: getWebSearchTool(location),
						scrapeWebpages: getWebpageScraperTool(),
					}
				: {}),
		},
		toolCallStreaming: true,
		maxSteps: 5,
		abortSignal,
	});
};

export const generateImageResponseFromXAI = async (
	model: string,
	prompt: string,
	params?: any,
	abortSignal?: AbortSignal
) => {
	const imageModel = xAI.imageModel(model);
	const { n, size, aspectRatio, ...modelParams } = params;

	try {
		const { images, warnings } = await generateImage({
			model: imageModel,
			prompt,
			n,
			size,
			aspectRatio,
			providerOptions: {
				xai: { ...modelParams },
			},
			abortSignal,
		});
		if (warnings.length > 0) {
			console.warn(`Provider: XAI\nImage generation warnings: ${warnings}`);
		}
		return images;
	} catch (error) {
		if (NoImageGeneratedError.isInstance(error)) {
			console.error('NoImageGeneratedError');
			console.error('Cause:', error.cause);
			console.error('Responses:', error.responses);
		} else {
			console.error('Unexpected error:', error);
		}
		return [];
	}
};
