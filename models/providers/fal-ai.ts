import {
	customProvider,
	experimental_generateImage as generateImage,
	ImageModel,
	NoImageGeneratedError,
	APICallError,
} from 'ai';
import { fal as fal_ai_sdk } from '@ai-sdk/fal';
import { fal } from '@fal-ai/client';
import {
	getImageModelEndpoint,
	IMAGE_MODEL_ENPOINTS,
	ImageModelDisplayName,
	ImageModelVendor,
} from '@/models/image/image-generation-models';
import { ImageGenerationParams } from '../image/image-generation-constraints';
import { urlToGeneratedImage } from '@/utils/image/local-image-processing';
import { VideoGenerationParams } from '../video/video-generation-constraints';

const FAL_WEBHOOK_URL = 'https://zeco.ai/api/video/fal';

fal.config({ credentials: process.env.FAL_API_KEY });

export const falAI = customProvider({
	imageModels: IMAGE_MODEL_ENPOINTS[ImageModelVendor.FalAI].reduce(
		(falImageModels, endpoint) => {
			falImageModels[endpoint] = fal_ai_sdk.image(endpoint);
			return falImageModels;
		},
		{} as Record<string, ImageModel>
	),
});

export const generateImageResponseFromFalAI = async (
	model: ImageModelDisplayName,
	prompt: string,
	params: ImageGenerationParams,
	imageUrl?: string,
	abortSignal?: AbortSignal
) => {
	const imageModel = falAI.imageModel(getImageModelEndpoint(model, !!imageUrl));

	const { n, size, aspectRatio, ...modelParams } = params;

	try {
		const { images, warnings } = await generateImage({
			model: imageModel,
			prompt,
			n,
			size,
			aspectRatio,
			providerOptions: {
				fal: {
					...modelParams,
					...(!!imageUrl && {
						image_url: imageUrl,
					}),
				},
			},
			abortSignal,
		});
		if (warnings.length > 0) {
			console.warn(`Provider: FalAI\nImage generation warnings: ${warnings}`);
		}
		return images;
	} catch (error) {
		if (NoImageGeneratedError.isInstance(error)) {
			console.error('NoImageGeneratedError');
			console.error('Cause:', error.cause);
			console.error('Responses:', error.responses);
		} else if (
			APICallError.isInstance(error) &&
			(model === ImageModelDisplayName.Ideogram_V2 ||
				model === ImageModelDisplayName.Ideogram_V3) &&
			error.statusCode === 200 &&
			error.responseBody
		) {
			/**
			 * @description
			 * This is a temporary workaround for the issue where the AI SDK throws an error.
			 * The error is thrown when the response JSON does not have height and width properties.
			 * TODO: Use patch-package to fix the issue in the AI SDK. Or, shift to provider API for image generation.
			 */
			try {
				const response = JSON.parse(error.responseBody);
				if (response.images?.length > 0) {
					const imageUrls = response.images.map((img: { url: string }) => img.url);
					const generatedImages = await Promise.all(
						imageUrls.map((url: string) => urlToGeneratedImage(url))
					);
					return generatedImages;
				}
			} catch (parseError) {
				console.error('Error parsing response:', parseError);
			}
		}
		throw error;
	}
};

export const submitVideoGenerationRequestToFalAI = async (
	modelEndpoint: string,
	prompt: string | undefined,
	imageUrl?: string,
	params: VideoGenerationParams = {}
) => {
	const { aspectRatio, duration, resolution, generateAudio } = params;
	try {
		const { request_id, response_url, status_url, cancel_url } = await fal.queue.submit(
			modelEndpoint,
			{
				input: {
					...(!!prompt && { prompt }),
					...(!!imageUrl && { image_url: imageUrl }),
					...(!!aspectRatio && { aspect_ratio: aspectRatio }),
					...(!!duration && { duration }),
					...(!!resolution && { resolution }),
					...(!!generateAudio && { generate_audio: generateAudio }),
				},
				webhookUrl: FAL_WEBHOOK_URL,
			}
		);
		return request_id;
	} catch (error) {
		console.error('Failed to submit video generation request:', error);
		throw error; // Re-throw the error so it can be handled by the API route
	}
};
