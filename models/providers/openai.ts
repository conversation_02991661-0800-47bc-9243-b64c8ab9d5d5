import {
	convertToCoreMessages,
	customProvider,
	defaultSettingsMiddleware,
	Experimental_GeneratedImage as GeneratedImage,
	experimental_generateImage as generateImage,
	ImageModel,
	Message,
	NoImageGeneratedError,
	streamText,
	wrapLanguageModel,
} from 'ai';
import { openai as originalOpenAI } from '@ai-sdk/openai';
import { OpenAI } from 'openai';
import {
	ModelDisplayName,
	modelDisplayNameToTechnicalNameMap,
	TextEmbeddingModel,
} from '@/models/conversational/conversational-models';
import {
	getImageModelEndpoint,
	IMAGE_MODEL_ENPOINTS,
	ImageModelDisplayName,
	ImageModelVendor,
} from '@/models/image/image-generation-models';
import { onFinishCallback } from '@/types/ai-sdk';
import {
	getAddResourceTool,
	getInformationTool,
	getWebpageScraperTool,
	getWebSearchTool,
} from '../tools';
import { BASE_GUIDELINE } from '../model-guidelines';
import {
	ChatModelParams,
	getChatModelDefaultParams,
} from '../conversational/conversational-model-constraints';
import { ImageGenerationParams } from '../image/image-generation-constraints';

const client = new OpenAI();

const wrapOpenAILanguageModelInMiddleware = (model: ModelDisplayName) =>
	wrapLanguageModel({
		model: originalOpenAI(modelDisplayNameToTechnicalNameMap[model]),
		middleware: [
			defaultSettingsMiddleware({
				settings: getChatModelDefaultParams(model),
			}),
		],
	});

export const openai = customProvider({
	languageModels: {
		[ModelDisplayName.GPT_5_mini]: wrapOpenAILanguageModelInMiddleware(
			ModelDisplayName.GPT_5_mini
		),
		[ModelDisplayName.GPT_5]: wrapOpenAILanguageModelInMiddleware(ModelDisplayName.GPT_5),
		[ModelDisplayName.o3]: wrapOpenAILanguageModelInMiddleware(ModelDisplayName.o3),
		[ModelDisplayName.o4_mini]: wrapOpenAILanguageModelInMiddleware(ModelDisplayName.o4_mini),
	},
	textEmbeddingModels: {
		[TextEmbeddingModel.OpenAI_SmallContext]: originalOpenAI.embedding(
			TextEmbeddingModel.OpenAI_SmallContext
		),
		[TextEmbeddingModel.OpenAI_LargeContext]: originalOpenAI.embedding(
			TextEmbeddingModel.OpenAI_LargeContext
		),
	},
	imageModels: IMAGE_MODEL_ENPOINTS[ImageModelVendor.OpenAI].reduce(
		(openAIImageModels, endpoint) => {
			openAIImageModels[endpoint] = originalOpenAI.image(endpoint);
			return openAIImageModels;
		},
		{} as Record<string, ImageModel>
	),
	fallbackProvider: originalOpenAI,
});

export const streamTextResponseFromOpenAI = (
	userId: string,
	messages: Message[],
	model: ModelDisplayName,
	isWebSearchEnabled = false,
	location: {
		country_code: string;
		city: string;
		languages: string;
	},
	params: ChatModelParams = {},
	onFinish?: onFinishCallback,
	abortSignal?: AbortSignal,
	guideline = BASE_GUIDELINE
) => {
	const languageModel = openai.languageModel(model);
	const { reasoningEffort, ...modelParams } = params;

	return streamText({
		system: guideline,
		model: languageModel,
		messages: convertToCoreMessages(messages),
		...modelParams,
		onFinish,
		tools: {
			// addResource: getAddResourceTool(userId),
			// getInformation: getInformationTool(userId),
			useWebSearchViaOpenAI: originalOpenAI.tools.webSearchPreview(),
			...(isWebSearchEnabled
				? {
						useWebSearch: getWebSearchTool(location),
						scrapeWebpages: getWebpageScraperTool(),
					}
				: {}),
		},
		toolCallStreaming: true,
		maxSteps: 5,
		providerOptions: {
			openai: {
				...(reasoningEffort && {
					reasoningEffort,
				}),
				reasoningSummary: 'auto', // 'detailed' for comprehensive summaries.
			},
		},
		abortSignal,
	});
};

export const generateImageResponseFromOpenAI = async (
	model: ImageModelDisplayName,
	prompt: string,
	params: ImageGenerationParams,
	abortSignal?: AbortSignal
) => {
	const imageModel = openai.imageModel(getImageModelEndpoint(model));
	const { n, size, aspectRatio, ...modelParams } = params;

	try {
		const { images, warnings } = await generateImage({
			model: imageModel,
			prompt,
			n,
			size,
			aspectRatio,
			providerOptions: {
				openai: { ...modelParams },
			},
			abortSignal,
		});
		if (warnings.length > 0) {
			console.warn(`Provider: OpenAI\nImage generation warnings: ${warnings}`);
		}
		return images;
	} catch (error) {
		if (NoImageGeneratedError.isInstance(error)) {
			console.error('NoImageGeneratedError');
			console.error('Cause:', error.cause);
			console.error('Responses:', error.responses);
		}
		throw error;
	}
};

export const generateImageEditResponseFromOpenAI = async (
	prompt: string,
	imageUrl: string,
	imageFile: File | null,
	params: ImageGenerationParams,
	abortSignal?: AbortSignal
) => {
	try {
		const { n, size, quality, background } = params;

		// If we have a URL but no file, fetch the file from the URL
		if (!imageFile) {
			try {
				const response = await fetch(imageUrl);
				const blob = await response.blob();
				const filename = imageUrl.split('/').pop() || 'image.png';
				imageFile = new File([blob], filename, { type: blob.type || 'image/png' });
				console.log('Successfully converted image URL to File for OpenAI');
			} catch (fetchError) {
				console.error('Error fetching image from URL for OpenAI:', fetchError);
				return [];
			}
		}

		const { data: images, usage } = await client.images.edit(
			{
				model: 'gpt-image-1',
				prompt,
				image: imageFile,
				n,
				quality: quality as any,
				size: size as any,
				background: background as any,
			},
			{
				signal: abortSignal,
			}
		);

		if (!images) {
			console.error('No images returned from OpenAI');
			return [];
		}

		const processedImages = images.map((image) => {
			// TODO: Confirm what the response returns in b64_json.
			const bytes = Buffer.from(image.b64_json!, 'base64');
			return {
				base64: image.b64_json,
				uint8Array: bytes,
				mimeType: 'image/png',
			} as GeneratedImage;
		});

		return processedImages;
	} catch (error) {
		if (error instanceof Error) {
			console.error('Error generating image edit with OpenAI:', error.message);
		} else {
			console.error('Unexpected error generating image edit with OpenAI:', error);
		}
		throw error;
	}
};
