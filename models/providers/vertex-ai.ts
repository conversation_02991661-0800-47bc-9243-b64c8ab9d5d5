import { GoogleAuth, GoogleAuthOptions } from 'google-auth-library';
import { createVertex, GoogleVertexImageProviderOptions } from '@ai-sdk/google-vertex';
import { createVertexAnthropic } from '@ai-sdk/google-vertex/anthropic';
import {
	customProvider,
	convertToCoreMessages,
	streamText,
	Message,
	streamObject,
	wrapLanguageModel,
	defaultSettingsMiddleware,
	experimental_generateImage as generateImage,
	NoImageGeneratedError,
	ImageModel,
	generateText,
} from 'ai';
import {
	ModelDisplayName,
	modelDisplayNameToTechnicalNameMap,
} from '@/models/conversational/conversational-models';
import { onFinishCallback } from '@/types/ai-sdk';
import { getWebpageScraperTool, getWebSearchTool } from '../tools';
import { BASE_GUIDELINE } from '../model-guidelines';
import {
	ChatModelParams,
	getChatModelDefaultParams,
} from '../conversational/conversational-model-constraints';
import { ImageGenerationParams } from '../image/image-generation-constraints';
import {
	getImageModelEndpoint,
	IMAGE_MODEL_ENPOINTS,
	ImageModelDisplayName,
	ImageModelVendor,
} from '../image/image-generation-models';
import { VideoGenerationParams } from '../video/video-generation-constraints';

let authInstance: GoogleAuth | null = null;
let authOptions: GoogleAuthOptions | null = null;

const getAuth = (options: GoogleAuthOptions) => {
	if (!authInstance || options !== authOptions) {
		authInstance = new GoogleAuth({
			scopes: ['https://www.googleapis.com/auth/cloud-platform'],
			...options,
		});
		authOptions = options;
	}
	return authInstance;
};

export const generateAuthToken = async (options?: GoogleAuthOptions) => {
	const auth = getAuth(options || {});
	const client = await auth.getClient();
	const accessTokenResponse = await client.getAccessToken();
	return accessTokenResponse.token || null;
};
const vertex = createVertex({
	googleAuthOptions: {
		credentials: JSON.parse(process.env.GOOGLE_VERTEX_CREDENTIALS!),
	},
});

const vertexAnthropic = createVertexAnthropic({
	googleAuthOptions: {
		credentials: JSON.parse(process.env.GOOGLE_VERTEX_CREDENTIALS!),
	},
});

const wrapGoogleLanguageModelInMiddleware = (model: ModelDisplayName) =>
	wrapLanguageModel({
		model: vertex(modelDisplayNameToTechnicalNameMap[model], {
			safetySettings: [
				{ category: 'HARM_CATEGORY_UNSPECIFIED', threshold: 'BLOCK_LOW_AND_ABOVE' },
			],
			useSearchGrounding: true,
		}),
		middleware: [
			defaultSettingsMiddleware({
				settings: getChatModelDefaultParams(model),
			}),
		],
	});

const wrapAnthropicLanguageModelInMiddleware = (model: ModelDisplayName) =>
	wrapLanguageModel({
		model: vertexAnthropic(modelDisplayNameToTechnicalNameMap[model]),
		middleware: [
			defaultSettingsMiddleware({
				settings: getChatModelDefaultParams(model),
			}),
		],
	});

export const vertexAI = customProvider({
	languageModels: {
		[ModelDisplayName.Gemini_2_5_Flash]: wrapGoogleLanguageModelInMiddleware(
			ModelDisplayName.Gemini_2_5_Flash
		),
		[ModelDisplayName.Gemini_2_5_Pro]: wrapGoogleLanguageModelInMiddleware(
			ModelDisplayName.Gemini_2_5_Pro
		),
		[ModelDisplayName.Claude_4_Sonnet]: wrapAnthropicLanguageModelInMiddleware(
			ModelDisplayName.Claude_4_Sonnet
		),
	},
	// Google Generative AI offers a text embedding model. Why is it not present for Vertex AI?
	textEmbeddingModels: {},
	imageModels: IMAGE_MODEL_ENPOINTS[ImageModelVendor.GoogleVertexAI].reduce(
		(googleImageModels, endpoint) => {
			googleImageModels[endpoint] = vertex.image(endpoint);
			return googleImageModels;
		},
		{} as Record<string, ImageModel>
	),
});

export const generateTextResponseFromVertexAI = (
	messages: Message[],
	model: ModelDisplayName,
	guideline = BASE_GUIDELINE
) => {
	const languageModel = vertexAI.languageModel(model);

	return generateText({
		system: guideline,
		model: languageModel,
		messages,
	});
};

export const streamTextResponseFromVertexAI = (
	userId: string,
	messages: Message[],
	model: ModelDisplayName,
	isWebSearchEnabled = false,
	location: {
		country_code: string;
		city: string;
		languages: string;
	},
	params: ChatModelParams = {},
	onFinish?: onFinishCallback,
	abortSignal?: AbortSignal,
	guideline = BASE_GUIDELINE
) => {
	const languageModel = vertexAI.languageModel(model);

	return streamText({
		system: guideline,
		model: languageModel,
		messages: convertToCoreMessages(messages),
		...params,
		onFinish,
		tools: {
			// addResource: getAddResourceTool(userId),
			// getInformation: getInformationTool(userId),
			...(isWebSearchEnabled
				? {
						useWebSearch: getWebSearchTool(location),
						scrapeWebpages: getWebpageScraperTool(),
					}
				: {}),
		},
		toolCallStreaming: true,
		maxSteps: 5,
		abortSignal,
	});
};

export const generateObjectResponseFromVertexAI = (
	model: ModelDisplayName,
	schema: any,
	prompt: string,
	guideline = BASE_GUIDELINE
) => {
	const languageModel = vertexAI.languageModel(model);
	return streamObject({
		system: guideline,
		model: languageModel,
		schema,
		prompt,
	});
};

export const generateImageResponseFromVertexAI = async (
	model: ImageModelDisplayName,
	prompt: string,
	params: ImageGenerationParams,
	abortSignal?: AbortSignal
) => {
	const imageModel = vertexAI.imageModel(getImageModelEndpoint(model));
	const { n, aspectRatio } = params;

	try {
		const { images, warnings } = await generateImage({
			model: imageModel,
			prompt,
			n,
			aspectRatio,
			providerOptions: {
				vertex: {
					addWatermark: false,
					personGeneration: 'allow_all',
					// safetySetting: 'block_none',
				} satisfies GoogleVertexImageProviderOptions,
			},
			abortSignal,
		});
		if (warnings.length > 0) {
			console.warn(`Provider: OpenAI\nImage generation warnings: ${warnings}`);
		}
		return images;
	} catch (error) {
		if (NoImageGeneratedError.isInstance(error)) {
			console.error('NoImageGeneratedError');
			console.error('Cause:', error.cause);
			console.error('Responses:', error.responses);
		}
		throw error;
	}
};

export const submitVideoGenerationRequestToVertexAI = async (
	modelEndpoint: string,
	prompt: string | undefined,
	imageUrl?: string,
	params: VideoGenerationParams = {}
): Promise<string> => {
	console.log(`🎬 [Vertex AI] Starting video generation for model: ${modelEndpoint}`);

	const { aspectRatio, duration, resolution, generateAudio } = params;

	// Get Google Cloud project ID from credentials
	const credentials = JSON.parse(process.env.GOOGLE_VERTEX_CREDENTIALS!);
	const projectId = credentials.project_id;

	if (!projectId) {
		console.error('🎬 [Vertex AI] ERROR: Google Cloud project ID not found in credentials');
		throw new Error('Google Cloud project ID not found in credentials');
	}

	// Generate access token
	const accessToken = await generateAuthToken({ credentials });
	if (!accessToken) {
		console.error('🎬 [Vertex AI] ERROR: Failed to generate access token');
		throw new Error('Failed to generate access token for Vertex AI');
	}

	// Prepare request body
	const instances = [];
	const instanceData: any = {};

	if (prompt) {
		instanceData.prompt = prompt;
	}

	if (imageUrl) {
		// For image-to-video, we need to fetch the image and convert to base64
		try {
			const imageResponse = await fetch(imageUrl);
			const imageBuffer = await imageResponse.arrayBuffer();
			const base64Image = Buffer.from(imageBuffer).toString('base64');
			const mimeType = imageResponse.headers.get('content-type') || 'image/jpeg';

			instanceData.image = {
				bytesBase64Encoded: base64Image,
				mimeType: mimeType,
			};
		} catch (error) {
			console.error('Failed to fetch and encode image:', error);
			throw new Error('Failed to process input image for video generation');
		}
	}

	instances.push(instanceData);

	// Prepare parameters
	const requestParams: any = {
		sampleCount: 1, // Always generate 1 video for now
	};

	if (aspectRatio) {
		requestParams.aspectRatio = aspectRatio;
	}

	if (duration) {
		// Convert duration from string to integer (remove 's' suffix if present)
		const durationSeconds = parseInt(duration.replace('s', ''));
		requestParams.durationSeconds = durationSeconds;
	} else {
		// Default duration: Veo 3 = 8s, Veo 2 = 8s (both default to 8)
		requestParams.durationSeconds = 8;
	}

	if (resolution && modelEndpoint.includes('veo-3')) {
		requestParams.resolution = resolution;
	}

	if (generateAudio !== undefined && modelEndpoint.includes('veo-3')) {
		requestParams.generateAudio = generateAudio;
	}

	const requestBody = {
		instances,
		parameters: requestParams,
	};

	// Make request to Vertex AI
	const url = `https://us-central1-aiplatform.googleapis.com/v1/projects/${projectId}/locations/us-central1/publishers/google/models/${modelEndpoint}:predictLongRunning`;

	try {
		const response = await fetch(url, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${accessToken}`,
				'Content-Type': 'application/json',
			},
			body: JSON.stringify(requestBody),
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('🎬 [Vertex AI] API error:', response.status, errorText);
			throw new Error(
				`Vertex AI API request failed: ${response.status} ${response.statusText}`
			);
		}

		const result = await response.json();

		// Extract operation name which serves as our request ID
		const operationName = result.name;
		if (!operationName) {
			console.error('🎬 [Vertex AI] ERROR: No operation name returned from API');
			throw new Error('No operation name returned from Vertex AI');
		}

		// Extract just the operation ID from the full operation name
		const operationId = operationName.split('/').pop();
		console.log(
			`🎬 [Vertex AI] Video generation request submitted successfully. Operation ID: ${operationId}`
		);
		return operationId;
	} catch (error) {
		console.error('🎬 [Vertex AI] ERROR: Failed to submit video generation request:', error);
		throw error;
	}
};
