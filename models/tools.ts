import { tool } from 'ai';
import { z } from 'zod';
import { findRelevantContent, insertEmbeddings } from './embedding';
import { openai } from './providers/openai';
import { TextEmbeddingModel } from './conversational/conversational-models';
import { webSearch, scrapeContent } from './web-search';

export const getAddResourceTool = (
	userId: string,
	model = openai.textEmbeddingModel(TextEmbeddingModel.OpenAI_SmallContext),
	parentDocId?: string
) => {
	return tool({
		description: `Add information to your knowledge base, but only when:
        1. The information is not part of your existing training data
        2. You determine the information would be valuable for future interactions
        3. The information is related to the user's preferences, history, or personal context
        Do not ask for confirmation before saving useful information.`,
		parameters: z.object({
			content: z.string().describe('the content or resource to add to the knowledge base'),
		}),
		execute: async ({ content }, { abortSignal }) =>
			insertEmbeddings(content, userId, model, parentDocId, abortSignal),
	});
};

export const getInformationTool = (
	userId: string,
	model = openai.textEmbeddingModel(TextEmbeddingModel.OpenAI_SmallContext),
	parentDocId?: string
) => {
	return tool({
		description: `Access information from previous interactions and stored content. Only use this tool when: 
        1. The information needed is not available in your training data.
        2. The question specifically relates to user's previous interactions or their stored content.
        3. You need to recall user-specific preferences, history, or saved information`,
		parameters: z.object({
			question: z.string().describe('the users question'),
		}),
		execute: async ({ question }, { abortSignal }) =>
			findRelevantContent(question, userId, model, parentDocId, abortSignal),
	});
};

export const getWebSearchTool = (location: {
	country_code: string;
	city: string;
	languages: string;
}) => {
	return tool({
		description: `Advanced web search tool utilizing SERP API for comprehensive information retrieval.
		Couple with scrapeWebpages tool, ONLY IF REQUIRED, to scrape the websites deemed relevant to provide the best response to the user's prompt.

IMPORTANT USAGE CONSTRAINTS:
1. Use this tool ONLY AFTER thoroughly analyzing previous search results
2. Do not make multiple search queries without fully utilizing the information from the first search
3. Make subsequent searches only if the initial search provided NO relevant information
4. Craft precise queries to maximize relevant results in a single search

CAPABILITIES:
- Complete search results with rich metadata
- Knowledge graph integration
- News and current events (via dedicated news search)
- Product information and pricing
- Academic and technical content
- Local and business data
- Social media insights
- Structured data (specs, prices, dates)

USE WHEN:
1. Required information is not in training data
2. Previous search attempts yielded no relevant results
3. Information needs real-time verification
4. Current data is essential for accurate response
5. Specifically searching for news content (use isNewsSearch parameter)

Returns comprehensive search results as structured JSON for thorough analysis before making additional queries.`,
		parameters: z.object({
			query: z
				.string()
				.describe(
					'carefully crafted search query to maximize relevant results in a single search'
				),
			isNewsSearch: z
				.boolean()
				.optional()
				.describe('Set to true to specifically search Google News results'),
		}),
		execute: async ({ query, isNewsSearch }, { abortSignal }) =>
			webSearch(query, location, isNewsSearch, abortSignal),
	});
};

export const getWebpageScraperTool = () => {
	return tool({
		description: `Deep content extraction tool for website analysis.

WHEN TO USE:
1. DIRECT CASES:
   - User explicitly mentions a website URL
   - User asks to extract information from specific web pages
   - User requests analysis of website content
   - User needs information from documentation sites

2. INDIRECT CASES:
   - User's query implies relevant information exists on specific websites
   - Reference to official documentation or specifications
   - Need to verify information from authoritative sources
   - Questions about product details or technical specifications
   - Information on relevant webpages provided by the web search tool is required for providing the best response to the user's prompt.

CAPABILITIES:
- Process multiple URLs concurrently
- Extract structured data and full content
- Preserve document hierarchy
- Capture metadata and cross-references
- Handle various content types

OUTPUT:
Returns structured JSON with:
- Extracted content
- Page metadata
- Document structure
- Technical specifications
- Cross-referenced information

The tool automatically optimizes extraction based on content type and structure.`,
		parameters: z.object({
			urls: z
				.array(z.string())
				.describe('URLs to analyze - can be explicitly mentioned or derived from context'),
			prompt: z
				.string()
				.describe('The prompt to use for targeted extraction of content from the webpage.'),
		}),
		execute: async ({ urls, prompt }) => scrapeContent(urls, prompt),
	});
};
