import { filterResultsForLLM } from '@/types/web-search-results';
import { scrapePages } from '@/models/web-scrape';

const SERP_API_KEY = process.env.NEXT_PUBLIC_SERP_API_KEY;
const SERP_API_URL = 'https://serpapi.com/search';

export const webSearch = async (
	query: string,
	location: {
		country_code: string;
		city: string;
		languages: string;
	},
	isNewsSearch?: boolean,
	abortSignal?: AbortSignal
) => {
	if (!SERP_API_KEY) {
		console.error('SERP API key is missing.');
		return { error: 'No results found.' };
	}

	const locationParams = new URLSearchParams();

	// Add country code (gl parameter)
	if (location?.country_code) {
		locationParams.append('gl', location.country_code.toLowerCase());
	}

	// Add language code (hl parameter) - use first language from the list
	if (location?.languages) {
		const primaryLanguage = location.languages.split(',')[0].split('-')[0];
		locationParams.append('hl', primaryLanguage.toLowerCase());
	}

	// Add city-level location if available
	if (location?.city) {
		locationParams.append('location', location.city);
	}

	// Add news parameter if needed
	const newsParam = isNewsSearch ? '&tbm=nws' : '';

	const response = await fetch(
		`${SERP_API_URL}?api_key=${SERP_API_KEY}&q=${encodeURIComponent(
			query
		)}&engine=google${newsParam}&${locationParams.toString()}`,
		{
			signal: abortSignal,
		}
	);

	if (!response.ok) {
		console.error(`Search error: ${response.statusText}`);
		return { error: 'Error performing web search.' };
	}

	const rawResults = await response.json();
	return filterResultsForLLM(rawResults);
};

export const scrapeContent = async (urls: string[], prompt: string) => {
	const batchScrapeResult = await scrapePages(urls, {
		jsonOptions: {
			prompt,
		},
	});
	if (batchScrapeResult.success) {
		return batchScrapeResult.data.map((result) => {
			const { url, json, markdown } = result;
			return {
				source: url,
				results: [
					{
						format: 'json',
						result: json,
					},
					{
						format: 'markdown',
						result: markdown,
					},
				],
			};
		});
	}
	return { error: batchScrapeResult.error };
};
