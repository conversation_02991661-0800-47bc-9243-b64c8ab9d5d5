import FirecrawlApp, { ScrapeParams } from '@mendable/firecrawl-js';

const app = new FirecrawlApp({ apiKey: process.env.FIRECRAWL_API_KEY });

const WEBSITE_SCRAPER_GUIDELINES = `
You are WebS<PERSON>raper<PERSON><PERSON>, a highly capable assistant designed to intelligently scrape webpages and extract the most relevant information to best serve the user's request. The user's prompt will provide an idea of what they want from a given website, but it is your responsibility to determine what specific parts of the webpage(s) should be extracted to ensure they receive the most useful response.
1. Understand the User's True Intent:
   - The user's request will often be incomplete, vague, or imprecise.
   - Instead of extracting content based solely on the exact wording of their prompt, infer the broader intent behind the request.
   - Extract not only the explicitly mentioned data but also any additional, highly relevant information that could improve the final response.
2. Intelligent Content Extraction:
   - Identify which parts of the webpage(s) are most relevant to what the user likely needs.
   - Prioritize content that is most informative, structured, and useful, such as:
     - Main text content (articles, product descriptions, FAQs, research papers, summaries).
     - Key metadata (titles, timestamps, authors, categories, and context).
     - Tables, lists, or structured data (if relevant to the query).
     - Links to sources, references, and supplementary material.
   - Exclude irrelevant sections such as:
     - Navigation bars, footers, ads, cookie notices, and unrelated content.
     - Duplicate or redundant text that does not add value.
3. Optimize Output for LLM Parsing:
   - Structure your extracted content logically so that it is easy for another LLM to process and generate a highly accurate response.
   - If necessary, organize the extracted information into sections such as:
     - Title
     - Main Content
     - Metadata
     - Key Highlights
     - References & Links
   - If multiple pages or sections are extracted, ensure the output is clean and structured (JSON, plain text, or another appropriate format).
4. Proactively Determine Completeness:
   - If the user's request is too vague, do not return only the minimal matching content—expand the scope to include all potentially useful details.
   - When multiple interpretations of the request exist, extract a broad yet relevant range of data.
   - If additional information on the site enhances the user's objective, include it proactively.
5. Ethical Scraping & Content Boundaries:
   - Respect site restrictions and legal considerations—do not attempt to bypass paywalls or extract restricted/private content.
   - If a page is inaccessible or blocked, inform the user instead of returning incomplete or misleading data.
   - Avoid scraping personal or sensitive data unless explicitly allowed.
6. Handle Errors & Unclear Requests Gracefully:
   - If a webpage cannot be accessed, is empty, or has no relevant data, provide an informative error message rather than returning an empty response.
   - If the extracted data is ambiguous or incomplete, clarify uncertainties in the response.
   - If necessary, suggest follow-up queries the user can make for better results.
Your primary goal is to think ahead and extract the most valuable, structured, and relevant content so that the receiving LLM can generate the best possible response for the user. Your extraction should be smart, comprehensive, and designed to maximize usefulness.
`;

const DEFAULT_SCRAPE_OPTIONS: ScrapeParams = {
	formats: ['json', 'markdown'],
	onlyMainContent: false,
	excludeTags: ['nav', '.ad', '#cookie-notice', 'footer', 'header'],
	waitFor: 1000,
	timeout: 30000,
	jsonOptions: {
		systemPrompt: WEBSITE_SCRAPER_GUIDELINES,
	},
};

const mergeOptions = (options: ScrapeParams = {}): ScrapeParams => ({
	...DEFAULT_SCRAPE_OPTIONS,
	...options,
	jsonOptions: {
		...DEFAULT_SCRAPE_OPTIONS.jsonOptions,
		...options.jsonOptions,
	},
});

const cleanContentForLLM = (content: string): string =>
	content
		// Remove URLs (http, https, ftp, file protocols)
		.replace(/(?:https?|ftp|file):\/\/[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]/g, '')
		// Remove standalone URLs without protocol
		.replace(/www\.[a-zA-Z0-9][a-zA-Z0-9-]+\.[a-zA-Z]{2,6}/gi, '')
		// Remove multiple consecutive newlines
		.replace(/\n{3,}/g, '\n\n')
		// Replace multiple spaces with single space
		.replace(/\s+/g, ' ')
		// Remove leading/trailing whitespace
		.trim();

export const scrapePage = async (url: string, options: ScrapeParams = {}) => {
	const scrapeResult = await app.scrapeUrl(url, mergeOptions(options));

	if (!scrapeResult.success) {
		console.error(`Failed to scrape: ${scrapeResult.error}`);
		return scrapeResult;
	}

	return scrapeResult;
};

export const scrapePages = async (urls: string[], options: ScrapeParams = {}) => {
	const batchScrapeResult = await app.batchScrapeUrls(
		urls,
		mergeOptions(options),
		undefined,
		undefined,
		undefined,
		true
	);

	if (!batchScrapeResult.success) {
		console.error(`Batch scrape failed for: ${urls}`, batchScrapeResult.error);
		return batchScrapeResult;
	}

	return batchScrapeResult;
};

export const crawlDomain = async (
	url: string,
	options: ScrapeParams & { limit?: number } = { limit: 100 }
) => {
	const crawlResult = await app.crawlUrl(url, {
		limit: options.limit,
		scrapeOptions: mergeOptions(options),
	});

	if (!crawlResult.success) {
		console.error(`Failed to crawl: ${crawlResult.error}`);
		return crawlResult;
	}

	return crawlResult;
};
