import { embed, embedMany, EmbeddingModel } from 'ai';
import { createSupabaseServerClient } from '@/utils/supabase/server';

interface EmbeddingResult {
	name: string;
	similarity: number;
}

const generateChunks = (input: string): string[] => {
	return input
		.trim()
		.split('.')
		.filter((i) => i !== '');
};

/**
 * Generates embedding for a single text input
 * @param value - Text to generate embedding for
 * @param model - Embedding model to use
 */
export const generateEmbedding = async (
	value: string,
	model: EmbeddingModel<string>,
	abortSignal?: AbortSignal
): Promise<number[]> => {
	try {
		const input = value.replaceAll('\\n', ' ');
		const {
			embedding,
			usage: { tokens },
		} = await embed({
			model,
			value: input,
			abortSignal,
		});
		return embedding;
	} catch (error) {
		console.error('Error generating embedding:', error);
		throw new Error('Failed to generate embedding');
	}
};

export const generateEmbeddings = async (
	value: string,
	model: EmbeddingModel<string>,
	abortSignal?: AbortSignal
): Promise<Array<{ embedding: number[]; content: string }>> => {
	const chunks = generateChunks(value);
	const {
		embeddings,
		usage: { tokens },
	} = await embedMany({
		model,
		values: chunks,
		abortSignal,
	});
	return embeddings.map((e, i) => ({ content: chunks[i], embedding: e }));
};

/**
 * Inserts embeddings into the database
 * @param value - Text to generate and store embeddings for
 * @param userId - User ID for the embeddings
 * @param parentDocId - Optional parent document ID
 * @param model - Embedding model to use
 */
export const insertEmbeddings = async (
	value: string,
	userId: string,
	model: EmbeddingModel<string>,
	parentDocId?: string,
	abortSignal?: AbortSignal
): Promise<void> => {
	const chunks = generateChunks(value);
	const supabase = await createSupabaseServerClient();

	try {
		const {
			embeddings,
			usage: { tokens },
		} = await embedMany({
			model,
			values: chunks,
			abortSignal,
		});

		const embeddingsData = chunks.map((chunk, i) => ({
			text_chunk: chunk,
			tokens_used: tokens,
			user_id: userId,
			embedding_vector: embeddings[i],
			...(parentDocId && { parent_doc_id: parentDocId }),
		}));

		const { error } = await supabase.from('embeddings').insert(embeddingsData);

		if (error) {
			throw error;
		}
	} catch (error) {
		console.error('Error inserting embeddings:', error);
		throw new Error('Failed to insert embeddings');
	}
};

/**
 * Finds relevant content based on similarity to query
 * @param userQuery - Query to find similar content for
 * @param userId - User ID to filter embeddings
 * @param parentDocId - Optional parent document ID to further filter results
 * @param model - Embedding model to use
 */
export const findRelevantContent = async (
	userQuery: string,
	userId: string,
	model: EmbeddingModel<string>,
	parentDocId?: string,
	abortSignal?: AbortSignal
): Promise<EmbeddingResult[]> => {
	const supabase = await createSupabaseServerClient();

	try {
		/**
		 * TODO: Store tokens consumed by the embedding generation.
		 * Is Usage Cost = Embedding Cost + Query Cost (includes Tool Call cost)?
		 */
		const userQueryEmbedded = await generateEmbedding(userQuery, model, abortSignal);

		const { data: similarEmbeddings, error } = await supabase.rpc('match_embeddings', {
			query_embedding: userQueryEmbedded,
			match_threshold: 0.5,
			match_count: 4,
			p_user_id: userId,
			p_parent_doc_id: parentDocId,
		});

		if (error) {
			throw error;
		}

		if (!similarEmbeddings?.length) {
			return [];
		}

		return similarEmbeddings.map((item: { text_chunk: string; similarity: number }) => ({
			name: item.text_chunk,
			similarity: item.similarity,
		}));
	} catch (error) {
		console.error('Error finding relevant content:', error);
		return [];
	}
};
