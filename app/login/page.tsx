'use client';

import { Suspense, useMemo } from 'react';
import { Logo } from '@/components/icons';
import { Card } from '@heroui/card';
import Link from 'next/link';
import SparklesCore from '@/components/ui/sparkles';
import { SignInWithSocial } from '@/app/login/actions/SignInWithSocial';
import { motion } from 'framer-motion';

export default function LoginPage() {
	const particleSettings = useMemo(
		() => ({
			id: 'particles-background-login',
			background: 'transparent',
			minSize: 0.6,
			maxSize: 1.4,
			particleColor: '#9455D3',
			particleDensity: 50,
			speed: 1,
			className: 'w-full h-full',
		}),
		[]
	);

	return (
		<Suspense>
			<main className="relative flex items-center justify-center h-screen overflow-hidden">
				{/* Background Elements */}
				<SparklesCore
					{...particleSettings}
					className="absolute inset-0"
				/>
				<div
					className="absolute inset-0 bg-linear-to-tr from-zeco-purple/90 via-purple-600/60 to-transparent opacity-15"
					style={{
						filter: 'blur(120px)',
						animation: 'pulse 8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
					}}
				/>

				{/* Login Card with Halo */}
				<div className="relative p-1">
					<motion.div
						className="absolute inset-0 blur-2xl rounded-2xl bg-linear-to-tr from-fuchsia-500 via-purple-600 to-violet-700"
						initial={{ opacity: 0 }}
						animate={{ opacity: 0.5 }}
						transition={{ duration: 1.2, delay: 0 }}
					/>
					<Card className="relative w-full max-w-sm p-8 bg-black/95 backdrop-blur-xl border border-neutral-800/60">
						<div className="flex flex-col items-center gap-6">
							<Logo className="w-16 h-16 stroke-zeco-purple" />
							<div className="text-center">
								<h1 className="text-2xl font-bold text-[#F8F9FA]">
									Welcome to Zeco AI
								</h1>
								<p className="text-sm text-[#9BA1A6]">
									Sign in to continue your journey
								</p>
							</div>
						</div>

						<div className="flex flex-col gap-3 mt-8">
							<SignInWithSocial provider="google" />
							<SignInWithSocial provider="github" />
						</div>

						<p className="mt-8 text-xs text-center text-[#9BA1A6]">
							By continuing, you agree to our{' '}
							<Link
								href="/terms"
								className="text-[#B49EE8] hover:text-[#CAB6F7] transition-colors"
							>
								Terms of Service
							</Link>{' '}
							and{' '}
							<Link
								href="/privacy"
								className="text-[#B49EE8] hover:text-[#CAB6F7] transition-colors"
							>
								Privacy Policy
							</Link>
							.
						</p>
					</Card>
				</div>

				{/* Decorative Elements - adjusted positioning */}
				<div className="absolute top-0 right-0 w-64 h-64 bg-zeco-purple/15 rounded-full blur-3xl" />
				<div className="absolute bottom-0 left-0 w-64 h-64 bg-purple-600/15 rounded-full blur-3xl" />
			</main>
		</Suspense>
	);
}
