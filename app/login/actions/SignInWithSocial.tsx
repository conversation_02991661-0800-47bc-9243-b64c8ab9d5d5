'use client';

import { useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { GithubColorIcon, GoogleColorIcon } from '@/components/icons';
import { createSupabaseBrowserClient } from '@/utils/supabase/client';
import { Button } from '@heroui/button';

type Provider = 'google' | 'github';

interface SocialButtonProps {
	provider: Provider;
}

export const SignInWithSocial = ({ provider }: SocialButtonProps) => {
	const searchParams = useSearchParams();
	const next = searchParams?.get('next');
	const shareId = searchParams?.get('shareId');

	let postLoginDestination = '';
	if (!!next) {
		postLoginDestination = `?next=${next}`;
		if (!!shareId) {
			postLoginDestination = `?next=${next}&shareId=${shareId}`;
		}
	}

	const supabase = createSupabaseBrowserClient();

	const loginWithProvider = useCallback(async () => {
		await supabase.auth.signInWithOAuth({
			provider,
			options: {
				redirectTo: `${origin}/auth/callback${postLoginDestination}`,
			},
		});
	}, [supabase.auth, provider, postLoginDestination]);

	const getIcon = () => {
		switch (provider) {
			case 'google':
				return <GoogleColorIcon className="h-4 w-4" />;
			case 'github':
				return <GithubColorIcon className="h-4 w-4" />;
		}
	};

	return (
		<Button
			size="md"
			className={`group relative w-full transition-all duration-300 ${
				provider === 'google'
					? 'bg-linear-to-r from-blue-500 via-blue-600 to-blue-700 hover:from-blue-600 hover:to-blue-800'
					: 'bg-linear-to-r from-gray-800 via-gray-900 to-black hover:from-gray-900 hover:to-gray-950'
			} text-white shadow-xl hover:scale-[1.02] hover:shadow-2xl`}
			onPress={loginWithProvider}
		>
			<div className="relative flex items-center justify-center gap-2">
				{getIcon()}
				<span>Continue with {provider.charAt(0).toUpperCase() + provider.slice(1)}</span>
			</div>
		</Button>
	);
};
