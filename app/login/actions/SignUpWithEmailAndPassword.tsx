'use server';

import { createSupabaseServerClient } from '@/utils/supabase/server';
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';

export const SignUpWithEmailAndPassword = async (formData: FormData) => {
	const origin = (await headers()).get('origin');
	const email = formData.get('email') as string;
	const password = formData.get('password') as string;
	const supabase = await createSupabaseServerClient();

	const { error } = await supabase.auth.signUp({
		email,
		password,
		options: {
			emailRedirectTo: `${origin}/auth/callback`,
		},
	});

	if (error) {
		return redirect('/login?message=Could not authenticate user');
	}

	// Email verification disabled
	return redirect('/dashboard');
};
