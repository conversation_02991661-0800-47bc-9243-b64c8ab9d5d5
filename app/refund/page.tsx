import ResizableNavBar from '@/components/landing-page/ResizableNavBar';
import FooterComponent from '@/components/landing-page/FooterComponent';

const RefundPolicy: React.FC = () => {
	return (
		<div className="flex flex-col">
			<ResizableNavBar isFixed />
			<div className="flex justify-center font-semibold text-4xl mt-32 mb-16">
				Refund Policy
			</div>
			<div className="flex flex-col mx-auto max-w-lg md:max-w-(--breakpoint-sm) text-sm mb-10">
				<p>
					<span className="font-bold text-zeco-purple">Last Updated:</span>
					<span className="font-semibold">&nbsp; February 22, 2025</span>
				</p>
				<br />

				{/* Introduction */}
				<p>
					Thank you for choosing <strong>Zeco AI</strong>, a product of&nbsp;
					<strong><PERSON><PERSON><PERSON> (d/b/a Zeco AI)</strong>. We strive to provide
					high-quality products and services for our customers. This policy explains the
					refund process and conditions under which refunds may be considered.
				</p>
				<br />

				{/* No Refunds Section */}
				<p className="font-bold text-lg">NO REFUNDS</p>
				<br />
				<p>
					Generally, all sales are final. Once your purchase is completed, we do not offer
					refunds or exchanges for any subscription fees, credits, or other digital goods
					associated with Zeco AI.
				</p>
				<br />

				{/* Special Cases */}
				<p className="font-bold text-lg">SPECIAL CASES</p>
				<br />
				<p>
					We understand that exceptional circumstances may arise. If you believe you have
					a compelling case for a refund, please reach out to us at&nbsp;
					<strong><EMAIL></strong>. We will review your request and, at our sole
					discretion, determine whether an exception is warranted.
				</p>
				<br />

				{/* Processing of Exception Requests */}
				<p className="font-bold text-lg">PROCESSING OF EXCEPTION REQUESTS</p>
				<br />
				<p>
					1. <strong>Contact Us:</strong>&nbsp;Send an email with the subject line
					&quot;Refund Request&quot; to <strong><EMAIL></strong>, outlining the
					reason for your request.
					<br />
					2. <strong>Review Period:</strong>&nbsp;We will acknowledge receipt of your
					request within a reasonable timeframe. Our team will investigate any relevant
					details related to your order or account.
					<br />
					3. <strong>Decision Notification:</strong>&nbsp;Following our review, we will
					inform you whether a refund will be granted, and if so, any specific terms or
					conditions.
				</p>
				<br />

				{/* No Guarantee */}
				<p className="font-bold text-lg">NO GUARANTEE OF REFUND</p>
				<br />
				<p>
					Submitting a request for a refund does not guarantee that a refund will be
					issued. All decisions are made on a case-by-case basis and are final.
				</p>
				<p>
					if any refund approved, it will be credited to your bank account within 7-10
					business days.
				</p>

				<br />

				{/* Updates to This Policy */}
				<p className="font-bold text-lg">UPDATES TO THIS POLICY</p>
				<br />
				<p>
					We may update this Refund Policy periodically to reflect changes in our
					practices or for other operational, legal, or regulatory reasons. Any updates
					will be posted on this page with a revised &quot;Last Updated&quot; date.
				</p>
				<br />

				{/* Contact Us */}
				<p className="font-bold text-lg">CONTACT US</p>
				<br />
				<p>
					For questions or concerns regarding this Refund Policy, please email us at:
					<br />
					<strong><EMAIL></strong>
				</p>
			</div>
			<footer>
				<FooterComponent />
			</footer>
		</div>
	);
};

export default RefundPolicy;
