import { NextResponse } from 'next/server';
import { getChatMessageById } from '@/service/db/chat-message';
import { getTaskByRequestId } from '@/service/db/tasks';
import type { PollingTaskResponse } from '@/types/chat';
import { createSupabaseServerClient } from '@/utils/supabase/server';

export async function GET(req: Request) {
	const { searchParams } = new URL(req.url);
	const requestId = searchParams.get('requestId');

	if (!requestId) {
		return NextResponse.json(
			{
				error: 'Request ID is required',
			},
			{ status: 400 }
		);
	}

	try {
		const supabase = await createSupabaseServerClient();
		const { data: task, error: taskError } = await getTaskByRequestId(supabase, requestId);

		if (taskError || !task) {
			return NextResponse.json(
				{
					error: 'Task not found',
				},
				{ status: 404 }
			);
		}

		// Prepare base response with task information
		const responseData: PollingTaskResponse = {
			taskStatus: task.status,
		};

		// If task is completed, also fetch the updated message
		if (task.status === 'COMPLETED') {
			const { data: message, error: messageError } = await getChatMessageById(
				supabase,
				task.messageId
			);

			if (messageError || !message) {
				// Task is completed but message not found - this shouldn't happen normally
				// Return task status anyway, client can handle this case
				console.error('Task completed but message not found:', messageError);
			} else {
				// Include the message response in the response
				responseData.videoGenerationResponse = message.response;
			}
		} else if (task.status === 'FAILED') {
			// Include the error message in the response
			let videoGenerationResponseError: string | undefined;
			const falWebhookErrorPayload = task.payload.payload;
			if (falWebhookErrorPayload) {
				const detail = falWebhookErrorPayload.detail;
				if (typeof detail === 'string') {
					videoGenerationResponseError = detail;
				} else {
					videoGenerationResponseError = detail?.[0]?.msg;
				}
			}
			responseData.videoGenerationResponseError = videoGenerationResponseError;
		}

		return NextResponse.json({ data: responseData }, { status: 200 });
	} catch (error) {
		console.error('Error fetching video generation status:', error);
		return NextResponse.json(
			{
				error: 'Internal server error',
			},
			{ status: 500 }
		);
	}
}
