import { NextResponse } from 'next/server';
import {
	VideoModelDisplayName,
	videoModelDisplayNameToTechnicalNameMap,
} from '@/models/video/video-generation-models';
import * as TasksDB from '@/service/db/tasks';
import * as ChatMessageDB from '@/service/db/chat-message';
import * as MediaStorageDB from '@/service/db/media-storage';
import { calculateVideoCreditCost } from '@/service/managers/credit-cost-manager';
import { updateUserUsage } from '@/service/managers/subscription-manager';
import { Response, Task } from '@/types/chat';
import { createSupabaseServerClient } from '@/utils/supabase/server';
import { generateAuthToken } from '@/models/providers/vertex-ai';

interface VertexAIOperationResponse {
	name: string;
	done: boolean;
	response?: {
		'@type': string;
		videos?: Array<{
			gcsUri?: string;
			bytesBase64Encoded?: string;
			mimeType: string;
		}>;
		raiMediaFilteredCount?: number;
		raiMediaFilteredReasons?: string[];
	};
	error?: {
		code: number;
		message: string;
		details?: any[];
	};
}

const updateMessageWithVideoUrl = async (
	supabase: any,
	task: Task,
	videoUrl: string,
	requestId: string
) => {
	try {
		console.log('🔄 [Vertex Polling] Updating message with video URL...');
		// Get the existing message
		const { data: message, error: messageError } = await ChatMessageDB.getChatMessageById(
			supabase,
			task.messageId
		);

		if (messageError || !message) {
			console.error('Failed to get message for video update:', messageError);
			throw new Error('Message not found');
		}

		// Find the response with the matching requestId and update it
		const updatedResponses = (message.response || []).map((resp: Response) => {
			if (resp.requestId === requestId) {
				return {
					...resp,
					text: 'Video generation completed successfully.',
					videos: [
						{
							url: videoUrl,
							type: 'video/mp4',
						},
					],
				};
			}
			return resp;
		});

		// Update the message with the new video URL
		const { error: updateError } = await ChatMessageDB.updateChatMessageResponses(
			supabase,
			task.messageId,
			updatedResponses
		);

		if (updateError) {
			console.error('Failed to update message with video URL:', updateError);
			throw updateError;
		}

		// Update task status to completed
		const { error: taskUpdateError } = await TasksDB.updateTaskStatus(
			supabase,
			requestId,
			'COMPLETED',
			{ videos: [{ url: videoUrl, type: 'video/mp4' }] }
		);

		if (taskUpdateError) {
			console.error('Failed to update task status:', taskUpdateError);
			throw taskUpdateError;
		}
	} catch (error) {
		console.error('Failed to update message with video URL:', error);
		throw error;
	}
};

const updateTaskWithError = async (
	supabase: any,
	task: Task,
	requestId: string,
	errorMessage: string,
	errorPayload?: any
) => {
	try {
		// Revert credits since video generation failed
		if (task.data) {
			const { model, duration, resolution, generateAudio } = task.data;
			try {
				const creditCost = calculateVideoCreditCost(model as VideoModelDisplayName, {
					duration: duration,
					resolution: resolution,
					withAudio: generateAudio === true,
				});

				// Use negative amount to add credits back
				const result = await updateUserUsage(supabase, task.userId, 'credits', -creditCost);
				if (!result.success) {
					console.error(
						'Failed to revert video credits on Vertex failure:',
						result.error
					);
				} else {
					console.log(
						`🔄 [Vertex Polling] Reverted ${creditCost} credits for failed video generation (task: ${requestId})`
					);
				}
			} catch (creditError) {
				console.error(
					'Failed to calculate or revert credits on Vertex failure:',
					creditError
				);
			}
		} else {
			console.warn('🔄 [Vertex Polling] No task data found for credit reversion');
		}

		console.log('🔄 [Vertex Polling] Updating task with error:', errorMessage);

		const { error: taskUpdateError } = await TasksDB.updateTaskStatus(
			supabase,
			requestId,
			'FAILED',
			{ error: errorMessage, payload: errorPayload }
		);

		if (taskUpdateError) {
			console.error('Failed to update task with error:', taskUpdateError);
			throw taskUpdateError;
		}
	} catch (error) {
		console.error('Failed to update task with error:', error);
		throw error;
	}
};

export async function POST(req: Request) {
	const { requestId } = await req.json();

	if (!requestId) {
		console.error('🔄 [Vertex Polling] Missing requestId in Vertex AI polling request');
		return NextResponse.json({ error: 'Missing requestId' }, { status: 400 });
	}

	try {
		const supabase = await createSupabaseServerClient();

		// Authenticate the user first
		const {
			data: { user },
			error: authError,
		} = await supabase.auth.getUser();

		if (authError || !user) {
			console.error('🔄 [Vertex Polling] Authentication failed:', authError);
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Get the task to find the operation details
		const { data: task, error: taskError } = await TasksDB.getTaskByRequestId(
			supabase,
			requestId
		);

		if (taskError || !task) {
			console.error('🔄 [Vertex Polling] Task not found:', requestId);
			return NextResponse.json({ error: 'Task not found' }, { status: 404 });
		}

		// Verify the task belongs to the authenticated user
		if (task.userId !== user.id) {
			console.error('🔄 [Vertex Polling] Unauthorized task access');
			return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
		}

		// Get Google Cloud project ID from credentials
		const credentials = JSON.parse(process.env.GOOGLE_VERTEX_CREDENTIALS!);
		const projectId = credentials.project_id;

		if (!projectId) {
			throw new Error('Google Cloud project ID not found in credentials');
		}

		// Generate access token
		const accessToken = await generateAuthToken({ credentials });
		if (!accessToken) {
			throw new Error('Failed to generate access token for Vertex AI');
		}

		// Get the model endpoint from task data
		const modelEndpoint =
			videoModelDisplayNameToTechnicalNameMap[
				(task.data?.model as VideoModelDisplayName) || VideoModelDisplayName.Veo_2
			];

		// Construct the full operation name
		const operationName = `projects/${projectId}/locations/us-central1/publishers/google/models/${modelEndpoint}/operations/${requestId}`;

		// Poll Vertex AI for operation status
		const pollUrl = `https://us-central1-aiplatform.googleapis.com/v1/projects/${projectId}/locations/us-central1/publishers/google/models/${modelEndpoint}:fetchPredictOperation`;

		const pollResponse = await fetch(pollUrl, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${accessToken}`,
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				operationName: operationName,
			}),
		});

		if (!pollResponse.ok) {
			const errorText = await pollResponse.text();
			console.error('Vertex AI polling error:', errorText);
			await updateTaskWithError(
				supabase,
				task,
				requestId,
				`Vertex AI polling failed: ${pollResponse.status}`,
				errorText
			);
			return NextResponse.json({ error: 'Vertex AI polling failed' }, { status: 500 });
		}

		const operationResult: VertexAIOperationResponse = await pollResponse.json();

		if (operationResult.error) {
			console.error('Vertex AI operation error:', operationResult.error);
			await updateTaskWithError(
				supabase,
				task,
				requestId,
				operationResult.error.message,
				operationResult.error
			);
			return NextResponse.json({ error: 'Video generation failed' }, { status: 500 });
		}

		if (!operationResult.done) {
			// Operation still in progress
			return NextResponse.json({ status: 'IN_PROGRESS' }, { status: 200 });
		}

		console.log('🔄 [Vertex Polling] Video generation completed, processing result...');

		// Operation completed, process the result
		const videos = operationResult.response?.videos;
		if (!videos || videos.length === 0) {
			console.error('🔄 [Vertex Polling] No videos found in completed operation');
			await updateTaskWithError(supabase, task, requestId, 'No videos generated');
			return NextResponse.json({ error: 'No videos generated' }, { status: 500 });
		}

		// Get the first video
		const video = videos[0];
		let videoUrl: string;

		if (video.gcsUri) {
			// Video is stored in Google Cloud Storage, we need to download and store it
			try {
				const gcsResponse = await fetch(video.gcsUri, {
					headers: {
						Authorization: `Bearer ${accessToken}`,
					},
				});

				if (!gcsResponse.ok) {
					throw new Error(`Failed to download video from GCS: ${gcsResponse.status}`);
				}

				const videoBuffer = await gcsResponse.arrayBuffer();
				const videoFile = new File([videoBuffer], 'generated-video.mp4', {
					type: 'video/mp4',
				});

				// Store the video in our media storage
				const { data: storedVideoUrl, error: storageError } =
					await MediaStorageDB.storeGeneratedVideo(supabase, videoFile, task.userId);

				if (storageError || !storedVideoUrl) {
					throw new Error('Failed to store video in media storage');
				}

				videoUrl = storedVideoUrl;
			} catch (error) {
				console.error('Failed to process GCS video:', error);
				await updateTaskWithError(
					supabase,
					task,
					requestId,
					'Failed to process generated video'
				);
				return NextResponse.json(
					{ error: 'Failed to process generated video' },
					{ status: 500 }
				);
			}
		} else if (video.bytesBase64Encoded) {
			// Video is returned as base64, convert and store it
			try {
				const videoBuffer = Buffer.from(video.bytesBase64Encoded, 'base64');
				const videoFile = new File([videoBuffer], 'generated-video.mp4', {
					type: 'video/mp4',
				});

				// Store the video in our media storage
				const { data: storedVideoUrl, error: storageError } =
					await MediaStorageDB.storeGeneratedVideo(supabase, videoFile, task.userId);

				if (storageError || !storedVideoUrl) {
					console.log('Failed to store video in media storage:', storageError);
					throw new Error('Failed to store video in media storage');
				}

				videoUrl = storedVideoUrl;
			} catch (error) {
				console.error('Failed to process base64 video:', error);
				await updateTaskWithError(
					supabase,
					task,
					requestId,
					'Failed to process generated video'
				);
				return NextResponse.json(
					{ error: 'Failed to process generated video' },
					{ status: 500 }
				);
			}
		} else {
			console.error('No video data found in response');
			await updateTaskWithError(supabase, task, requestId, 'No video data in response');
			return NextResponse.json({ error: 'No video data in response' }, { status: 500 });
		}

		// Update the message and task with the video URL
		await updateMessageWithVideoUrl(supabase, task, videoUrl, requestId);

		console.log(
			`🔄 [Vertex Polling] Video generation completed successfully for request: ${requestId}`
		);
		return NextResponse.json({ status: 'COMPLETED', videoUrl }, { status: 200 });
	} catch (error) {
		console.error('🔄 [Vertex Polling] ERROR:', error);
		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}
