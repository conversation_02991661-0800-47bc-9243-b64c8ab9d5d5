import { NextResponse } from 'next/server';
import { VideoModelDisplayName } from '@/models/video/video-generation-models';
import * as TasksDB from '@/service/db/tasks';
import * as ChatMessageDB from '@/service/db/chat-message';
import * as MediaStorageDB from '@/service/db/media-storage';
import { calculateVideoCreditCost } from '@/service/managers/credit-cost-manager';
import { updateUserUsage } from '@/service/managers/subscription-manager';
import { Attachment, Response, Task } from '@/types/chat';
import { createSupabaseServerClient } from '@/utils/supabase/server';
import { verifyFalWebhookSignature } from '@/utils/fal-webhook-utils';

interface FalAIWebhookResponse {
	request_id: string;
	status: 'OK' | 'ERROR';
	payload: any;
	payload_error?: string;
	error?: string;
}

const handleVideoGenerationFailure = async (task: Task, payload: any) => {
	try {
		const supabase = await createSupabaseServerClient();

		// Revert credits since video generation failed
		if (task.data) {
			const { model, duration, resolution, generateAudio } = task.data;
			try {
				const creditCost = calculateVideoCreditCost(model as VideoModelDisplayName, {
					duration: duration,
					resolution: resolution,
					withAudio: generateAudio === true,
				});

				// Use negative amount to add credits back
				const result = await updateUserUsage(supabase, task.userId, 'credits', -creditCost);
				if (!result.success) {
					console.error('Failed to revert video credits on failure:', result.error);
				} else {
					console.log(
						`Reverted ${creditCost} credits for failed video generation (task: ${task.requestId})`
					);
				}
			} catch (creditError) {
				console.error('Failed to calculate or revert credits on failure:', creditError);
			}
		} else {
			console.warn('No video parameters found in task for credit reversion');
		}

		const { error: taskError } = await TasksDB.updateTaskStatus(
			supabase,
			task.requestId,
			'FAILED',
			payload
		);
		if (taskError) {
			console.error('Failed to update task status:', taskError);
			throw new Error('Failed to update task status');
		}

		const { data: message, error: messageError } = await ChatMessageDB.getChatMessageById(
			supabase,
			task.messageId
		);
		if (messageError || !message) {
			throw new Error(`Message not found for task ID: ${task.messageId}`);
		}
		if (!message.response) {
			throw new Error(`No response found for message ID: ${message.id}`);
		}

		// Determine the request type based on the message structure
		// If there's only one response, it's a 'submit' or 'edit'
		// If there are multiple responses and one has this request ID, it's a 'regenerate'
		const responsesWithRequestId = message.response.filter(
			(response: Response) => response.requestId === task.requestId
		);

		if (message.response.length === 1 && responsesWithRequestId.length === 1) {
			await ChatMessageDB.deleteChatMessage(supabase, message.id);
		} else if (responsesWithRequestId.length > 0) {
			// For regenerate, remove the failed response
			const updatedResponses = message.response.filter(
				(response: Response) => response.requestId !== task.requestId
			);
			await ChatMessageDB.updateChatMessageResponses(supabase, message.id, updatedResponses);
		}
	} catch (error) {
		console.error('Failed to handle video generation failure:', error);
		throw error;
	}
};

const handleVideoGenerationSuccess = async (task: Task, payload: any, video: Attachment) => {
	try {
		const supabase = await createSupabaseServerClient(true); // Service role for storage

		const { data: message, error: messageError } = await ChatMessageDB.getChatMessageById(
			supabase,
			task.messageId
		);
		if (messageError || !message) {
			throw new Error(`Failed to fetch message for message id: ${task.messageId}`);
		}
		if (!message.response) {
			throw new Error(`No response found for message id: ${message.id}`);
		}

		const { data: storedVideoUrl, error: storeError } =
			await MediaStorageDB.storeGeneratedVideoUrl(supabase, video.url, task.userId);
		if (storeError || !storedVideoUrl) {
			console.error('Failed to store generated video:', storeError);
			throw new Error('Failed to store generated video');
		}

		const updatedResponses = message.response.map((response: Response) => {
			if (response.requestId === task.requestId) {
				return {
					...response,
					text: undefined,
					videos: [
						{
							...video,
							url: storedVideoUrl,
						},
					],
				};
			}
			return response;
		});

		const { error: updateError } = await ChatMessageDB.updateChatMessageResponses(
			supabase,
			message.id,
			updatedResponses
		);
		if (updateError) {
			console.error('Failed to update message with responses:', updateError);
			throw new Error('Failed to update message with responses');
		}

		const { error: taskError } = await TasksDB.updateTaskStatus(
			supabase,
			task.requestId,
			'COMPLETED',
			payload
		);
		if (taskError) {
			console.error('Failed to update task status:', taskError);
			throw new Error('Failed to update task status');
		}
	} catch (error) {
		console.error('Failed to update message with video URL:', error);
		throw error;
	}
};

export async function POST(req: Request) {
	const requestId = req.headers.get('x-fal-webhook-request-id');
	const userId = req.headers.get('x-fal-webhook-user-id');
	const timestamp = req.headers.get('x-fal-webhook-timestamp');
	const signature = req.headers.get('x-fal-webhook-signature');

	if (!requestId || !userId || !timestamp || !signature) {
		console.error('Missing required webhook headers');
		return NextResponse.json({ error: 'Missing required headers' }, { status: 400 });
	}

	const rawBody = await req.arrayBuffer();
	const bodyBuffer = Buffer.from(rawBody);
	const isValid = await verifyFalWebhookSignature(
		requestId,
		userId,
		timestamp,
		signature,
		bodyBuffer
	);

	if (!isValid) {
		console.error('Invalid webhook signature');
		return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
	}

	const responsePayload = JSON.parse(bodyBuffer.toString()) as FalAIWebhookResponse;
	console.log('Received webhook from fal.ai:', JSON.stringify(responsePayload));

	const { request_id, status, payload, payload_error, error } = responsePayload;

	if (!request_id) {
		console.error('Missing request_id in webhook payload');
		return NextResponse.json({ error: 'Bad Request' }, { status: 400 });
	}

	if (!status) {
		console.error('Missing status in webhook payload');
		return NextResponse.json({ error: 'Bad Request' }, { status: 400 });
	}

	let task: Task | null = null;
	try {
		const supabase = await createSupabaseServerClient();
		const { data, error } = await TasksDB.getTaskByRequestId(supabase, request_id);
		if (error) {
			console.error('Failed to fetch task:', error);
			return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
		}
		task = data;
		if (!task) {
			console.error('Task not found');
			return NextResponse.json({ error: 'Task not found' }, { status: 404 });
		}
	} catch (error) {
		console.error('Failed to fetch task:', error);
		return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
	}

	if (task?.status === 'COMPLETED') {
		console.log('Task already completed, skipping');
		return NextResponse.json({ status: 'OK' }, { status: 200 });
	}

	let videoUrl: string | null = null;
	if (payload && payload.video && payload.video.url) {
		videoUrl = payload.video.url as string;
	}

	if (status !== 'OK' || !videoUrl) {
		if (payload_error) {
			console.log('Error in request payload:', payload_error);
		}
		if (error) {
			console.log('Error in video generation:', error);
		}
		if (payload) {
			console.log('Error in video generation response:', payload);
		}
		try {
			await handleVideoGenerationFailure(task, responsePayload);
			return NextResponse.json({ status: 'OK' }, { status: 200 });
		} catch (error) {
			console.error('Failed to handle video generation failure:', error);
			return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
		}
	}

	try {
		await handleVideoGenerationSuccess(task, responsePayload, {
			type: payload.video.content_type,
			url: videoUrl,
		});
		return NextResponse.json({ status: 'ok' }, { status: 200 });
	} catch (error) {
		console.error('Failed to update message with video URL:', error);
		return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
	}
}
