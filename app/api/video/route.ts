import { NextResponse } from 'next/server';
import * as ChatHistoryDB from '@/service/db/chat-history';
import * as ChatMessageDB from '@/service/db/chat-message';
import * as MediaStorageDB from '@/service/db/media-storage';
import * as TasksDB from '@/service/db/tasks';
import { checkUserModelAccess, updateUserUsage } from '@/service/managers/subscription-manager';
import { createSupabaseServerClient } from '@/utils/supabase/server';
import {
	VideoModelDisplayName,
	VideoModelProvider,
	videoModelDisplayNameToProviderMap,
	videoModelDisplayNameToTechnicalNameMap,
	videoModelDisplayNameToEditEndpointMap,
	VideoModelsList,
} from '@/models/video/video-generation-models';
import { submitVideoGenerationRequestToFalAI } from '@/models/providers/fal-ai';
import { submitVideoGenerationRequestToVertexAI } from '@/models/providers/vertex-ai';
import { Attachment, ChatMessage, TaskData } from '@/types/chat';
import { createVideoErrorResponse } from '@/utils/error-handling';
import { calculateVideoCreditCost } from '@/service/managers/credit-cost-manager';

export async function POST(req: Request) {
	const supabase = await createSupabaseServerClient();
	const {
		data: { user },
		error: authError,
	} = await supabase.auth.getUser();

	if (authError || !user) {
		return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
	}

	const userId = user.id;

	const formData = await req.formData();
	const chatId = formData.get('chatId') as string;
	const messageId = formData.get('messageId') as string;
	const videoModel = formData.get('model') as VideoModelDisplayName;
	const prompt = formData.get('prompt') as string;
	const isNewChat = formData.get('isNewChat') === 'true';
	const isSharedChat = formData.get('isSharedChat') === 'true';
	const sharedMessages = JSON.parse(
		(formData.get('sharedMessages') as string) || '[]'
	) as ChatMessage[];
	const requestType =
		(formData.get('requestType') as 'submit' | 'edit' | 'regenerate') || 'submit';
	const params = JSON.parse((formData.get('params') as string) || '{}');

	// Validate required fields
	if (!chatId || !userId || !prompt || !videoModel) {
		console.error('Missing required fields');
		return NextResponse.json({ error: 'Bad Request' }, { status: 400 });
	}

	// Validate messageId for edit and regenerate
	if ((requestType === 'edit' || requestType === 'regenerate') && !messageId) {
		console.error(`Message ID is required for ${requestType}`);
		return NextResponse.json({ error: 'Bad Request' }, { status: 400 });
	}

	// Get model tier and check access
	const modelInfo = VideoModelsList.find(
		(m: { key: string; tier?: number }) => m.key === videoModel
	);
	if (!modelInfo?.tier) {
		return NextResponse.json({ error: 'Invalid model' }, { status: 400 });
	}

	const accessCheck = await checkUserModelAccess(supabase, userId, modelInfo.tier, 'credit');
	if (!accessCheck.hasAccess) {
		return NextResponse.json({ error: accessCheck.error }, { status: 403 });
	}

	let uploadedAttachments: Attachment[] = [];
	let uploadedImageFiles: File[] = [];
	let uploadedImageUrls: string[] = [];
	if (requestType === 'submit') {
		uploadedImageFiles = formData.getAll('images').map((file) => file as File);
		try {
			const imageUrlResults = await Promise.all(
				uploadedImageFiles.map((file) =>
					MediaStorageDB.storeUploadedImage(supabase, file, userId)
				)
			);
			uploadedImageUrls = imageUrlResults
				.map((result) => result.data)
				.filter(Boolean) as string[];
			uploadedAttachments = uploadedImageFiles.map((file, index) => ({
				type: file.type,
				name: file.name,
				url: uploadedImageUrls[index],
			}));
		} catch (error) {
			console.error('Error storing uploaded image:', error);
			return NextResponse.json(
				{ error: 'Failed to process uploaded image' },
				{ status: 500 }
			);
		}
	} else {
		formData.getAll('images').forEach((file) => {
			const attachment = JSON.parse(file as string) as Attachment;
			uploadedImageUrls.push(attachment.url);
			uploadedAttachments.push(attachment);
		});
	}

	// Currently, a single image upload is supported.
	const uploadedImageUrl = uploadedImageUrls.length > 0 ? uploadedImageUrls[0] : undefined;

	const modelEndpoint = !!uploadedImageUrl
		? videoModelDisplayNameToEditEndpointMap[videoModel]
		: videoModelDisplayNameToTechnicalNameMap[videoModel];
	const modelProvider = videoModelDisplayNameToProviderMap[videoModel];

	// Stable Video Diffusion does not require a prompt.
	let promptToSend: string | undefined = prompt;
	if (videoModel === VideoModelDisplayName.Stable_Video_Diffusion) {
		promptToSend = undefined;
	}

	let videoGenerationRequestId: string;
	try {
		if (modelProvider === VideoModelProvider.Google) {
			videoGenerationRequestId = await submitVideoGenerationRequestToVertexAI(
				modelEndpoint,
				promptToSend,
				uploadedImageUrl,
				params
			);
		} else {
			videoGenerationRequestId = await submitVideoGenerationRequestToFalAI(
				modelEndpoint,
				promptToSend,
				uploadedImageUrl,
				params
			);
		}
	} catch (error: any) {
		console.error('Failed to submit video generation request:', error);
		return NextResponse.json(
			{
				error: createVideoErrorResponse(error, modelProvider, videoModel),
			},
			{ status: 500 }
		);
	}

	try {
		const videoGenerationTaskData: TaskData = {
			model: videoModel,
			duration: params?.duration,
			resolution: params?.resolution,
			generateAudio: params?.generateAudio,
		};

		const { error: taskError } = await TasksDB.createTask(
			supabase,
			videoGenerationRequestId,
			userId,
			chatId,
			messageId,
			videoGenerationTaskData
		);
		if (taskError) {
			throw taskError;
		}
	} catch (error) {
		console.error('Failed to create video generation task in database:', error);
		return NextResponse.json(
			{ error: 'Failed to create video generation task' },
			{ status: 500 }
		);
	}

	switch (requestType) {
		case 'submit':
			if (isNewChat || isSharedChat) {
				try {
					const { error } = await ChatHistoryDB.createChatHistory(
						supabase,
						userId,
						chatId,
						prompt,
						'video'
					);
					if (error) {
						console.error('Failed to create chat history:', error);
						if (error.code !== '23505') {
							throw new Error('Failed to create chat history');
						}
					}
				} catch (error) {
					console.error('Failed to create chat history:', error);
				}
			}

			if (isSharedChat) {
				try {
					const { error } = await ChatMessageDB.createMultipleChatMessages(
						supabase,
						chatId,
						sharedMessages
					);
					if (error) {
						console.error('Failed to save shared chat messages:', error);
						throw new Error('Failed to save shared chat messages');
					}
				} catch (error) {
					console.error('Failed to save shared chat messages:', error);
				}
			}

			const newMessage: ChatMessage = {
				chatId,
				id: messageId,
				prompt: {
					text: prompt,
					attachments: uploadedAttachments,
					params,
				},
				response: [
					{
						text: 'Video generation request has been successfully submitted. It will appear here shortly.',
						videos: [],
						modelUsed: {
							modelName: modelEndpoint,
							modelProvider,
						},
						requestId: videoGenerationRequestId,
						createdAt: new Date().toISOString(),
					},
				],
				createdAt: new Date().toISOString(),
			};

			try {
				const { error } = await ChatMessageDB.createChatMessage(supabase, newMessage);
				if (error) {
					console.error('Failed to save chat message:', error);
					throw new Error('Failed to save chat message');
				}
			} catch (error) {
				console.error('Failed to save chat message in database: ', error);
				return NextResponse.json({ error: 'Failed to save chat message' }, { status: 500 });
			}
			break;

		case 'edit':
			try {
				await ChatMessageDB.deleteChatMessage(supabase, messageId);
			} catch (error) {
				console.error('Failed to delete existing chat message', error);
				return NextResponse.json(
					{ error: 'Failed to delete existing chat message' },
					{ status: 500 }
				);
			}

			const editedMessage: ChatMessage = {
				chatId,
				id: messageId,
				prompt: {
					text: prompt,
					attachments: uploadedAttachments,
					params,
				},
				response: [
					{
						text: 'Video generation request has been successfully submitted. It will appear here shortly.',
						videos: [],
						modelUsed: {
							modelName: modelEndpoint,
							modelProvider,
						},
						requestId: videoGenerationRequestId,
						createdAt: new Date().toISOString(),
					},
				],
				createdAt: new Date().toISOString(),
			};

			try {
				await ChatMessageDB.createChatMessage(supabase, editedMessage);
			} catch (error) {
				console.error('Failed to save new chat message in database: ', error);
				return NextResponse.json(
					{ error: 'Failed to save new chat message' },
					{ status: 500 }
				);
			}
			break;

		case 'regenerate':
			const newResponse = {
				text: 'Video generation request has been successfully submitted. It will appear here shortly.',
				videos: [],
				modelUsed: {
					modelName: modelEndpoint,
					modelProvider,
				},
				requestId: videoGenerationRequestId,
				createdAt: new Date().toISOString(),
			};

			try {
				await ChatMessageDB.appendChatMessageResponse(supabase, messageId, newResponse);
			} catch (error) {
				console.error('Failed to append new response to existing chat message: ', error);
				return NextResponse.json(
					{ error: 'Failed to append new response to existing chat message' },
					{ status: 500 }
				);
			}
			break;
	}

	const creditCost = calculateVideoCreditCost(videoModel as VideoModelDisplayName, {
		duration: params?.duration,
		resolution: params?.resolution,
		withAudio: params?.generateAudio === true,
	});

	const result = await updateUserUsage(supabase, userId, 'credits', creditCost);
	if (!result.success) {
		console.error('Failed to update video usage:', result.error);
		return NextResponse.json(
			{ error: 'Failed to update credits used for video' },
			{ status: 500 }
		);
	}

	try {
		await ChatHistoryDB.updateChatHistoryLastModifiedAt(supabase, chatId);
	} catch (error) {
		console.error('Failed to update chat history last_modified_at:', error);
		return NextResponse.json({ error: 'Failed to update chat history' }, { status: 500 });
	}

	return NextResponse.json({ videoGenerationRequestId, uploadedImageUrls }, { status: 200 });
}
