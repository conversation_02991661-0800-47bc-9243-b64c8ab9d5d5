import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/utils/supabase/server';
import { processSubscriptionWebhook } from '@/service/managers/subscription-webhook-manager';
import type { CreemWebhookPayload } from '@/types/payment';
import { verifyCreemWebhookSignature } from '@/service/helpers/creem-payment-helper';

/**
 * Creem Webhook Handler
 * Handles subscription events from Creem
 */
export async function POST(request: NextRequest) {
	try {
		// Get webhook signature from headers (as per Creem documentation)
		const signature = request.headers.get('creem-signature');

		if (!signature) {
			console.error('Missing required creem-signature header');
			return NextResponse.json(
				{ error: 'Missing required creem-signature header' },
				{ status: 400 }
			);
		}

		// Get raw body for signature verification
		const rawBody = await request.text();

		// Verify webhook signature using Creem's official method
		const isValid = verifyCreemWebhookSignature(rawBody, signature);

		if (!isValid) {
			console.error('Invalid webhook signature');
			return NextResponse.json({ error: 'Invalid webhook signature' }, { status: 401 });
		}

		// Parse webhook payload
		const payload: CreemWebhookPayload = JSON.parse(rawBody);

		// Create Supabase client
		const supabase = await createSupabaseServerClient();

		// Process webhook
		const result = await processSubscriptionWebhook(supabase, payload);

		if (result.success) {
			console.log('Webhook processed successfully:', result.message);
			return NextResponse.json({ message: result.message }, { status: 200 });
		} else {
			console.error('Webhook processing failed:', result.error);
			return NextResponse.json({ error: result.error || result.message }, { status: 400 });
		}
	} catch (error) {
		console.error('Webhook handler error:', error);
		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}
