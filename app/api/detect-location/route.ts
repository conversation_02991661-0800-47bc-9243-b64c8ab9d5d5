import { NextResponse } from 'next/server';

export async function GET(request: Request) {
	const headers = new Headers(request.headers);
	const cfCountry = headers.get('CF-IPCountry');
	const acceptLanguage = headers.get('Accept-Language');

	// Try Cloudflare header first
	if (cfCountry) {
		return NextResponse.json({ country: cfCountry });
	}

	// Fallback to Accept-Language header parsing
	const language = acceptLanguage?.split(',')[0] || 'en-US';
	const country = language.split('-')[1] || 'US';

	return NextResponse.json({ country });
}
