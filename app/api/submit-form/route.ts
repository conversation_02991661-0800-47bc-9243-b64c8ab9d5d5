import { generateEmailTemplate } from '@/utils/emailTemplate';
import { sendEmail } from '@/utils/emailConfig';
import { NextResponse } from 'next/server';

const googleSheetUrl = process.env.GOOGLE_FORM_URL || '';

export async function POST(request: Request) {
	try {
		const formData = await request.formData();
		const email = formData.get('Email');

		if (!email) {
			return new Response('Email is required', { status: 400 });
		}

		try {
			await Promise.all([
				// Submit to Google Sheet
				fetch(googleSheetUrl, {
					method: 'POST',
					body: formData,
				}),
				// Send welcome email
				sendEmail({
					to: email.toString(),
					subject: 'You are in! Thanks for signing up for ZECO AI Beta.',
					html: generateEmailTemplate(),
					personalizations: {},
				}),
			]);

			return NextResponse.json({ message: 'Success' }, { status: 200 });
		} catch (error) {
			console.error('Operation failed:', error);
			return NextResponse.json({ error: 'Error Registering Your Interest' }, { status: 500 });
		}
	} catch (error) {
		console.error('Form submission error:', error);
		return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
	}
}
