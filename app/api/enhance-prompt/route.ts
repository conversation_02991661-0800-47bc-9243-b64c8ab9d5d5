import { NextRequest, NextResponse } from 'next/server';
import { generateTextResponseFromVertexAI } from '@/models/providers/vertex-ai';
import { getPromptEnhancementGuidelines } from '@/models/model-guidelines';
import { ModelDisplayName } from '@/models/conversational/conversational-models';
import { nanoid } from '@/lib';

export async function POST(request: NextRequest) {
	try {
		const { prompt, model } = await request.json();

		if (!prompt || !model) {
			return NextResponse.json({ error: 'Prompt and model are required' }, { status: 400 });
		}

		const guideline = getPromptEnhancementGuidelines(model);
		const { text: enhancedPrompt } = await generateTextResponseFromVertexAI(
			[
				{
					id: nanoid(),
					role: 'user',
					content: prompt,
				},
			],
			ModelDisplayName.Gemini_2_5_Flash,
			guideline
		);

		return NextResponse.json({ enhancedPrompt });
	} catch (error: any) {
		console.error('Error enhancing prompt:', error);
		return NextResponse.json(
			{
				error: 'Failed to enhance prompt. Please try again or proceed with your original prompt.',
			},
			{ status: 500 }
		);
	}
}
