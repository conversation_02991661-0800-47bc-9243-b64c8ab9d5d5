import { NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/utils/supabase/server';
import { getCurrentSubscriptionCycleForPlan } from '@/service/db/subscription-cycle';

export async function GET() {
	try {
		const supabase = await createSupabaseServerClient();
		const {
			data: { user },
			error: authError,
		} = await supabase.auth.getUser();

		if (authError || !user) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Determine user's plan from metadata
		const isPro = user.user_metadata?.is_pro_user || false;
		const planId = isPro ? 'zeco-pro-monthly-plan' : 'zeco-free-plan';

		// Get current subscription cycle for the user's plan
		const { data: cycle, error: cycleError } = await getCurrentSubscriptionCycleForPlan(
			supabase,
			user.id,
			planId
		);

		if (cycleError || !cycle) {
			// If no cycle found, user might not have a subscription yet
			return NextResponse.json({
				credits: 0,
				creditsUsed: 0,
				creditsGranted: 0,
				tier1TokensUsed: 0,
				tier1TokensGranted: 0,
				tier2TokensUsed: 0,
				tier2TokensGranted: 0,
			});
		}

		const remainingCredits = cycle.credits_granted - cycle.credits_used;

		return NextResponse.json({
			credits: Math.max(0, remainingCredits),
			creditsUsed: cycle.credits_used,
			creditsGranted: cycle.credits_granted,
			tier1TokensUsed: cycle.tier1_tokens_used,
			tier1TokensGranted: cycle.tier1_tokens_granted,
			tier2TokensUsed: cycle.tier2_tokens_used,
			tier2TokensGranted: cycle.tier2_tokens_granted,
		});
	} catch (error) {
		console.error('Error fetching user credits:', error);
		return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
	}
}
