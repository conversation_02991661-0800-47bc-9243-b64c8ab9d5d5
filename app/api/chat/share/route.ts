import { NextRequest, NextResponse } from 'next/server';
import { getPublicAccessTimeForChat } from '@/service/managers/chat-manager';
import { createSupabaseServerClient } from '@/utils/supabase/server';

export async function GET(request: NextRequest) {
	const searchParams = request.nextUrl.searchParams;
	const chatId = searchParams.get('chatId');

	if (!chatId) {
		return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 });
	}

	try {
		const supabase = await createSupabaseServerClient();
		const lastSharedAt = await getPublicAccessTimeForChat(supabase, chatId);

		return NextResponse.json({ lastSharedAt });
	} catch (error) {
		console.error('Error fetching public access time for chat:', error);
		return NextResponse.json({ error: 'Failed to fetch chat share status' }, { status: 500 });
	}
}
