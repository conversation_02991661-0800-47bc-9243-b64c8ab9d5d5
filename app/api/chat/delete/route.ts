import { revalidatePath } from 'next/cache';
import { NextRequest, NextResponse } from 'next/server';
import * as ChatMessageDB from '@/service/db/chat-message';
import { createSupabaseServerClient } from '@/utils/supabase/server';


export async function DELETE(request: NextRequest) {
	const searchParams = request.nextUrl.searchParams;
	const chatId = searchParams.get('id');
	const chatType = searchParams.get('type');

	const supabase = await createSupabaseServerClient();

	if (!chatId || chatId === '') {
		return NextResponse.json({ error: 'Chat ID is required' }, { status: 400 });
	}

	const { error } = await ChatMessageDB.deleteChatMessagesByChatId(supabase, chatId);

	if (error) {
		return NextResponse.json({ error: 'Failed to delete chat history.' }, { status: 555 });
	}

	if (chatType) {
		revalidatePath(`/${chatType}/${chatId}`, 'page');
	} else {
		console.warn('Chat type was not found. Revalidating entire post-auth (chat) segment.');
		revalidatePath('/(post-auth)', 'layout');
	}

	return NextResponse.json({ message: 'Chat history deleted successfully.' }, { status: 200 });
}
