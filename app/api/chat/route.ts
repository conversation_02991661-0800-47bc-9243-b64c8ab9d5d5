import { Message, appendResponseMessages } from 'ai';
import { NextResponse } from 'next/server';
import * as ChatHistoryDB from '@/service/db/chat-history';
import * as ChatMessageDB from '@/service/db/chat-message';
import { checkUserModelAccess, updateUserUsage } from '@/service/managers/subscription-manager';
import { createSupabaseServerClient } from '@/utils/supabase/server';
import {
	ModelDisplayName,
	modelDisplayNameToProviderMap,
	modelDisplayNameToTechnicalNameMap,
	ModelProvider,
	ConversationalModelsList,
} from '@/models/conversational/conversational-models';
import { ChatModelParams } from '@/models/conversational/conversational-model-constraints';
import { streamTextResponseFromMistral } from '@/models/providers/mistral-ai';
import { streamTextResponseFromOpenAI } from '@/models/providers/openai';
import { streamTextResponseFromTogetherAI } from '@/models/providers/together-ai';
import { streamTextResponseFromVertexAI } from '@/models/providers/vertex-ai';
import { streamTextResponseFromGrokAI } from '@/models/providers/xai';
import { onFinishCallback } from '@/types/ai-sdk';
import { convertFromSDKMessage, convertFromSDKResponse } from '@/utils/chat/chat-helpers';

// Allow streaming responses up to 30 seconds
export const maxDuration = 60;

// Called by useChat (AI SDK) internally.
// This function is responsible for handling the incoming messages and returning the AI response.
export async function POST(req: Request) {
	const supabase = await createSupabaseServerClient();
	const {
		data: { user },
		error: authError,
	} = await supabase.auth.getUser();

	if (authError || !user) {
		return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
	}

	const userId = user.id;

	const {
		id: chatId,
		isNewChat,
		isSharedChat,
		messages,
		sharedMessages,
		model,
		requestType,
		data,
		location,
	} = await req.json();

	// Get model tier and check access
	const modelInfo = ConversationalModelsList.find(
		(m: { key: string; tier?: number }) => m.key === model
	);
	if (!modelInfo?.tier) {
		return NextResponse.json({ error: 'Invalid model' }, { status: 400 });
	}

	const accessCheck = await checkUserModelAccess(supabase, userId, modelInfo.tier, 'token');
	if (!accessCheck.hasAccess) {
		return NextResponse.json({ error: accessCheck.error }, { status: 403 });
	}

	const chatModel: ModelDisplayName = model;
	const modelProvider = modelDisplayNameToProviderMap[chatModel];
	const isWebSearchEnabled: boolean = data?.isWebSearchEnabled;
	const modelParams: ChatModelParams = {
		temperature: data?.temperature,
		topP: data?.topP,
		topK: data?.topK,
		presencePenalty: data?.presencePenalty,
		frequencyPenalty: data?.frequencyPenalty,
		reasoningEffort: data?.reasoningEffort,
	};

	const onFinish: onFinishCallback = async ({ response, reasoning, usage }) => {
		// Update token usage based on model tier
		const usageType = modelInfo.tier === 1 ? 'tier1_tokens' : 'tier2_tokens';
		const result = await updateUserUsage(supabase, userId, usageType, usage.totalTokens);

		if (!result.success) {
			console.error('Failed to update token usage:', result.error);
		}

		const updatedMessageList = appendResponseMessages({
			messages,
			responseMessages: response.messages,
		});

		const prompt = updatedMessageList[updatedMessageList.length - 2];
		const responseMessage = updatedMessageList[updatedMessageList.length - 1];
		const isMessageEmpty = responseMessage.content === '';
		if (isMessageEmpty) {
			throw new Error('Empty message is an invalid response.');
		}

		switch (requestType) {
			case 'submit':
				if (!!isNewChat || !!isSharedChat) {
					try {
						const { error } = await ChatHistoryDB.createChatHistory(
							supabase,
							userId,
							chatId,
							(messages as Message[])[0].content,
							'chat'
						);
						if (error) {
							console.error('Failed to create chat history:', error);
							if (error.code !== '23505') {
								throw new Error('Failed to create chat history');
							}
						}
					} catch (error) {
						console.error('Failed to create chat history:', error);
					}
				}

				if (!!isSharedChat) {
					try {
						const { error } = await ChatMessageDB.createMultipleChatMessages(
							supabase,
							chatId,
							sharedMessages
						);
						if (error) {
							console.error('Failed to save shared chat messages:', error);
							throw new Error('Failed to save shared chat messages');
						}
					} catch (error) {
						console.error('Failed to save shared chat messages:', error);
					}
				}

				try {
					const chatMessage = convertFromSDKMessage(
						prompt,
						responseMessage,
						reasoning,
						isWebSearchEnabled,
						modelParams,
						chatId,
						{
							modelName: modelDisplayNameToTechnicalNameMap[chatModel],
							modelProvider,
						},
						usage.totalTokens
					);
					const { error } = await ChatMessageDB.createChatMessage(supabase, chatMessage);
					if (error) {
						console.error('Failed to save chat message:', error);
						throw new Error('Failed to save chat message');
					}
				} catch (error) {
					console.error('Failed to save chat message:', error);
				}
				break;

			case 'edit':
				try {
					const { error: deleteError } = await ChatMessageDB.deleteChatMessage(
						supabase,
						prompt.id
					);
					if (deleteError) {
						console.error('Failed to delete chat message:', deleteError);
						throw new Error('Failed to delete chat message');
					}

					const chatMessage = convertFromSDKMessage(
						prompt,
						responseMessage,
						reasoning,
						isWebSearchEnabled,
						modelParams,
						chatId,
						{
							modelName: modelDisplayNameToTechnicalNameMap[chatModel],
							modelProvider,
						},
						usage.totalTokens
					);
					const { error: saveError } = await ChatMessageDB.createChatMessage(
						supabase,
						chatMessage
					);
					if (saveError) {
						console.error('Failed to save edited chat message:', saveError);
						throw new Error('Failed to save edited chat message');
					}
				} catch (error) {
					console.error('Failed to edit chat message:', error);
				}
				break;

			case 'regenerate':
				try {
					const newResponse = convertFromSDKResponse(
						responseMessage,
						reasoning,
						{
							modelName: modelDisplayNameToTechnicalNameMap[chatModel],
							modelProvider,
						},
						usage.totalTokens
					);
					const { error } = await ChatMessageDB.appendChatMessageResponse(
						supabase,
						prompt.id,
						newResponse
					);
					if (error) {
						console.error('Failed to regenerate response:', error);
						throw new Error('Failed to regenerate response');
					}
				} catch (error) {
					console.error('Failed to regenerate response:', error);
				}
				break;
		}

		try {
			const { error } = await ChatHistoryDB.updateChatHistoryLastModifiedAt(supabase, chatId);
			if (error) {
				console.error('Failed to update chat history last_modified_at:', error);
				throw new Error('Failed to update chat history last_modified_at');
			}
		} catch (error) {
			console.error('Failed to update chat history last_modified_at:', error);
		}
	};

	switch (modelProvider) {
		case ModelProvider.Anthropic:
		case ModelProvider.Google:
			return streamTextResponseFromVertexAI(
				userId,
				messages,
				chatModel,
				isWebSearchEnabled,
				location,
				modelParams,
				onFinish,
				req.signal
			).toDataStreamResponse({
				sendReasoning: true,
				getErrorMessage(error: any) {
					console.error(
						`${chatModel} failed to generate response.\nError Details: ${error.message || 'Unknown error'}`
					);
					return JSON.stringify({
						message: `${chatModel} failed to generate a response. Please try again.`,
						errorDetails: error.message || 'Unknown error',
					});
				},
			});

		case ModelProvider.Deepseek:
		case ModelProvider.Meta:
			return streamTextResponseFromTogetherAI(
				userId,
				messages,
				chatModel,
				isWebSearchEnabled,
				location,
				modelParams,
				onFinish,
				req.signal
			).toDataStreamResponse({
				sendReasoning: true,
				getErrorMessage(error: any) {
					console.error(
						`${chatModel} failed to generate response.\nError Details: ${error.message || 'Unknown error'}`
					);
					return JSON.stringify({
						message: `${chatModel} failed to generate a response. Please try again.`,
						errorDetails: error.message || 'Unknown error',
					});
				},
			});

		case ModelProvider.Mistral:
			return streamTextResponseFromMistral(
				userId,
				messages,
				chatModel,
				isWebSearchEnabled,
				location,
				modelParams,
				onFinish,
				req.signal
			).toDataStreamResponse({
				sendReasoning: true,
				getErrorMessage(error: any) {
					console.error(
						`${chatModel} failed to generate response.\nError Details: ${error.message || 'Unknown error'}`
					);
					return JSON.stringify({
						message: `${chatModel} failed to generate a response. Please try again.`,
						errorDetails: error.message || 'Unknown error',
					});
				},
			});

		case ModelProvider.OpenAI:
			return streamTextResponseFromOpenAI(
				userId,
				messages,
				chatModel,
				isWebSearchEnabled,
				location,
				modelParams,
				onFinish,
				req.signal
			).toDataStreamResponse({
				sendReasoning: true,
				getErrorMessage(error: any) {
					console.error(
						`${chatModel} failed to generate response.\nError Details: ${error.message || 'Unknown error'}`
					);
					return JSON.stringify({
						message: `${chatModel} failed to generate a response. Please try again.`,
						errorDetails: error.message || 'Unknown error',
					});
				},
			});

		case ModelProvider.XAI:
			return streamTextResponseFromGrokAI(
				userId,
				messages,
				chatModel,
				isWebSearchEnabled,
				location,
				modelParams,
				onFinish,
				req.signal
			).toDataStreamResponse({
				sendReasoning: true,
				getErrorMessage(error: any) {
					console.error(
						`${chatModel} failed to generate response.\nError Details: ${error.message || 'Unknown error'}`
					);
					return JSON.stringify({
						message: `${chatModel} failed to generate a response. Please try again.`,
						errorDetails: error.message || 'Unknown error',
					});
				},
			});
	}

	/**
	 * TODO: Consume stream in case of disconnects.
	 * @see https://sdk.vercel.ai/docs/ai-sdk-ui/chatbot-message-persistence#handling-client-disconnects
	 */
}
