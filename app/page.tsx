import HeroHeaderComponent from '@/components/landing-page/HeroHeaderComponent';
import TrustedByComponent from '@/components/landing-page/TrustedByComponent';
import FeaturesComponent from '@/components/landing-page/FeaturesComponent';
import CTAComponent from '@/components/landing-page/CTAComponent';
import FooterComponent from '@/components/landing-page/FooterComponent';
import ResizableNavBar from '@/components/landing-page/ResizableNavBar';
import PricingSection from '@/components/landing-page/PricingSection';
import FAQComponent from '@/components/landing-page/FaqComponent';

export default function Home() {
	return (
		<div>
			<ResizableNavBar />
			<section>
				<HeroHeaderComponent />
			</section>
			<section>
				<TrustedByComponent />
			</section>
			<section>
				<FeaturesComponent />
			</section>
			<section>
				<PricingSection />
			</section>
			<section>
				<FAQComponent />
			</section>
			<section>
				<CTAComponent />
			</section>
			<footer>
				<FooterComponent />
			</footer>
		</div>
	);
}
