'use client';

import { But<PERSON> } from '@heroui/button';
import { Card } from '@heroui/card';
import Link from 'next/link';
import { IconAlertTriangle } from '@tabler/icons-react';

export default function AuthCodeErrorPage() {
	return (
		<div className="flex min-h-screen items-center justify-center bg-dot-thick-neutral-900">
			{/* Background Gradient */}
			<div
				className="absolute inset-0 bg-linear-to-tr from-red-500/50 via-purple-600/30 to-transparent opacity-15"
				style={{
					filter: 'blur(120px)',
				}}
			/>

			<Card className="w-full max-w-md border border-red-500/20 bg-[#000000]/95 p-8 backdrop-blur-xl">
				<div className="flex flex-col items-center space-y-6 text-center">
					{/* Error Icon */}
					<div className="flex h-16 w-16 items-center justify-center rounded-full bg-red-500/10">
						<IconAlertTriangle
							size={32}
							className="text-red-500"
						/>
					</div>

					{/* Error Message */}
					<div className="space-y-3">
						<h1 className="text-2xl font-bold text-[#F8F9FA]">Authentication Error</h1>
						<p className="text-sm text-[#9BA1A6]">
							We encountered an issue while trying to sign you in. This might be due
							to an expired or invalid authentication token.
						</p>
					</div>

					{/* Action Buttons */}
					<div className="flex w-full flex-col space-y-3">
						<Link
							href="/login"
							className="w-full"
						>
							<Button className="w-full bg-linear-to-r from-purple-600 to-purple-700 text-white">
								Try Again
							</Button>
						</Link>
						<Link
							href="/"
							className="w-full"
						>
							<Button
								variant="ghost"
								className="w-full text-[#9BA1A6]"
							>
								Return to Home
							</Button>
						</Link>
					</div>
				</div>
			</Card>
		</div>
	);
}
