'use client';

import { But<PERSON> } from '@heroui/button';
import { Card } from '@heroui/card';
import Link from 'next/link';
import { IconAlertTriangle } from '@tabler/icons-react';

export default function AuthCodeErrorPage() {
	return (
		<div className="min-h-screen flex items-center justify-center bg-dot-thick-neutral-900">
			{/* Background Gradient */}
			<div
				className="absolute inset-0 opacity-15 bg-gradient-to-tr from-red-500/50 via-purple-600/30 to-transparent"
				style={{
					filter: 'blur(120px)',
				}}
			/>

			<Card className="w-full max-w-md p-8 bg-[#000000]/95 backdrop-blur-xl border border-red-500/20">
				<div className="flex flex-col items-center text-center space-y-6">
					{/* Error Icon */}
					<div className="w-16 h-16 rounded-full bg-red-500/10 flex items-center justify-center">
						<IconAlertTriangle
							size={32}
							className="text-red-500"
						/>
					</div>

					{/* Error Message */}
					<div className="space-y-3">
						<h1 className="text-2xl font-bold text-[#F8F9FA]">Authentication Error</h1>
						<p className="text-[#9BA1A6] text-sm">
							We encountered an issue while trying to sign you in. This might be due
							to an expired or invalid authentication token.
						</p>
					</div>

					{/* Action Buttons */}
					<div className="flex flex-col w-full space-y-3">
						<Link
							href="/login"
							className="w-full"
						>
							<Button className="w-full bg-gradient-to-r from-purple-600 to-purple-700 text-white">
								Try Again
							</Button>
						</Link>
						<Link
							href="/"
							className="w-full"
						>
							<Button
								variant="ghost"
								className="w-full text-[#9BA1A6]"
							>
								Return to Home
							</Button>
						</Link>
					</div>
				</div>
			</Card>
		</div>
	);
}
