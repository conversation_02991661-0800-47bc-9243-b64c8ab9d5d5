import { NextResponse } from 'next/server';
// The client you created from the Server-Side Auth instructions
import { createSupabaseServerClient } from '@/utils/supabase/server';

export async function GET(request: Request) {
	const { searchParams, origin } = new URL(request.url);
	const code = searchParams.get('code');
	const next = searchParams.get('next') ?? '/chat';
	const shareId = searchParams.get('shareId');

	let postLoginDestination = next;
	if (!!shareId) {
		postLoginDestination = `${next}?shareId=${shareId}`;
	}

	if (code) {
		const supabase = await createSupabaseServerClient();
		const { error: signOutError } = await supabase.auth.signOut({
			scope: 'others',
		});
		if (!signOutError) {
			const { error } = await supabase.auth.exchangeCodeForSession(code);
			if (!error) {
				const forwardedHost = request.headers.get('x-forwarded-host'); // original origin before load balancer
				const isLocalEnv = process.env.NODE_ENV === 'development';
				if (isLocalEnv) {
					// we can be sure that there is no load balancer in between, so no need to watch for X-Forwarded-Host
					return NextResponse.redirect(`${origin}${postLoginDestination}`);
				} else if (forwardedHost) {
					return NextResponse.redirect(`https://${forwardedHost}${postLoginDestination}`);
				} else {
					return NextResponse.redirect(`${origin}${postLoginDestination}`);
				}
			}
		}
	}

	// return the user to an error page with instructions
	return NextResponse.redirect(`${origin}/auth/auth-code-error`);
}
