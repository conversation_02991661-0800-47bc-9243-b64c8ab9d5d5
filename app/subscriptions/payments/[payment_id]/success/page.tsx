import { Suspense } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@heroui/button';
import { CheckIcon, CrossIcon } from '@/components/icons';
import { verifySubscriptionPaymentAction } from '@/app/subscriptions/actions';
import { type CreemReturnParams } from '@/service/helpers/creem-payment-helper';

interface SubscriptionPaymentSuccessContentProps {
	paymentId: string;
	searchParams: { [key: string]: string | string[] | undefined };
}

async function SubscriptionPaymentSuccessContent({
	paymentId,
	searchParams,
}: SubscriptionPaymentSuccessContentProps) {
	const verificationData: CreemReturnParams & { signature?: string } = {
		request_id: (searchParams.request_id as string) ?? null,
		checkout_id: (searchParams.checkout_id as string) ?? null,
		order_id: (searchParams.order_id as string) ?? null,
		customer_id: (searchParams.customer_id as string) ?? null,
		subscription_id: (searchParams.subscription_id as string) ?? null,
		product_id: (searchParams.product_id as string) ?? null,
		signature: (searchParams.signature as string) ?? undefined,
	};

	const result = await verifySubscriptionPaymentAction(paymentId, verificationData);

	const isSuccess = result.success;
	const message = isSuccess
		? 'Payment verified successfully! We are now processing your subscription.'
		: result.error.message;

	const Icon = isSuccess ? CheckIcon : CrossIcon;
	const iconColor = isSuccess ? 'text-green-500' : 'text-red-500';
	const bgColor = isSuccess ? 'bg-green-500/20' : 'bg-red-500/20';

	return (
		<div className="flex min-h-screen w-full flex-col items-center justify-center space-y-6 bg-black">
			<div className={`h-16 w-16 rounded-full ${bgColor} flex items-center justify-center`}>
				<Icon className={`h-8 w-8 ${iconColor}`} />
			</div>

			<span className="max-w-md text-center text-lg font-medium text-white">{message}</span>

			<span className="text-center text-sm text-gray-400">
				{isSuccess
					? 'You can now safely close this page or continue to the chat.'
					: 'Please contact support if the problem persists.'}
			</span>
			<Link href="/chat">
				<Button
					color="secondary"
					size="md"
					className="mt-4"
				>
					Continue to Chat
				</Button>
			</Link>
		</div>
	);
}

type PageProps = {
	params: Promise<{ payment_id: string }>;
	searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

export default async function SubscriptionPaymentSuccessPage({ params, searchParams }: PageProps) {
	const resolvedParams = await params;
	const resolvedSearchParams = await searchParams;

	return (
		<Suspense
			fallback={
				<div className="flex min-h-screen flex-col items-center justify-center space-y-4 bg-black">
					<span className="text-lg font-medium text-white">Verifying payment...</span>
					<span className="text-sm text-gray-400">This may take a few moments</span>
				</div>
			}
		>
			<SubscriptionPaymentSuccessContent
				paymentId={resolvedParams.payment_id}
				searchParams={resolvedSearchParams}
			/>
		</Suspense>
	);
}
