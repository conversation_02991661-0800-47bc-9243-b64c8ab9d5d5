'use server';

import { createSupabaseServerClient } from '@/utils/supabase/server';
import {
	verifyCreemReturnSignature,
	type CreemReturnParams,
	generateCustomerPortalLink,
} from '@/service/helpers/creem-payment-helper';
import * as PaymentManager from '@/service/managers/payment-manager';
import { getExistingCustomerId } from '@/service/db/subscription-payment';
import {
	hasActiveSubscription,
	createUserSubscription,
	updateUserSubscription,
} from '@/service/db/user-subscription';
import * as SubscriptionManager from '@/service/managers/subscription-manager';
import { generateUserSubscriptionId, generateSubscriptionCycleId } from '@/types/payment';
import { createSubscriptionCycle } from '@/service/db/subscription-cycle';
import { getSubscriptionPlan } from '@/service/db/subscription-plan';

// Shared types for consistency
type ActionError = {
	code: 'UNAUTHORIZED' | 'INVALID_INPUT' | 'NOT_FOUND' | 'FORBIDDEN' | 'INTERNAL_ERROR';
	message: string;
};

// Type-safe result types using discriminated unions
export type InitiatePaymentResult =
	| { success: true; paymentLink: string }
	| { success: false; error: ActionError; isProcessing?: boolean };

export type VerifyPaymentResult =
	| { success: true; paymentId: string }
	| { success: false; error: ActionError };

export type CustomerPortalUrlResult =
	| { success: true; portalUrl: string }
	| { success: false; error: ActionError };

export type SetupFreeSubscriptionResult =
	| { success: true; updatedUser: any }
	| { success: false; error: ActionError };

export async function initiateSubscriptionPaymentAction(
	planId: string
): Promise<InitiatePaymentResult> {
	try {
		// Input validation
		if (!planId || typeof planId !== 'string') {
			return {
				success: false,
				error: {
					code: 'INVALID_INPUT',
					message: 'Invalid plan ID provided',
				},
			};
		}

		// Authentication check
		const supabase = await createSupabaseServerClient();
		const {
			data: { user },
			error: authError,
		} = await supabase.auth.getUser();

		if (authError || !user) {
			return {
				success: false,
				error: {
					code: 'UNAUTHORIZED',
					message: 'Please log in to continue',
				},
			};
		}

		// Check if user already has processing payments or is Pro
		const { data: statusData, error: statusError } =
			await SubscriptionManager.getUserSubscriptionStatus(supabase, user.id);

		if (statusError) {
			console.error('Failed to check subscription status:', statusError);
			// Continue anyway - don't block payment initiation
		} else if (statusData) {
			// User is already Pro
			if (statusData.isPro) {
				return {
					success: false,
					error: {
						code: 'FORBIDDEN',
						message: 'You already have an active Pro subscription',
					},
				};
			}

			// User needs support for stuck payment
			if (statusData.status === 'need_support') {
				return {
					success: false,
					error: {
						code: 'FORBIDDEN',
						message:
							'Your payment is taking longer than expected. Please reach <NAME_EMAIL> for help!',
					},
					isProcessing: true,
				};
			}

			// User has processing payment
			if (statusData.status === 'processing') {
				return {
					success: false,
					error: {
						code: 'FORBIDDEN',
						message: 'Payment is already being processed. Please wait.',
					},
					isProcessing: true,
				};
			}
		}

		// Initiate payment
		const result = await SubscriptionManager.initiateSubscriptionPayment(
			supabase,
			user.id,
			user.email!,
			user.user_metadata?.full_name || 'Customer',
			planId
		);

		if (!result.success) {
			console.error('Payment initiation failed:', result.error);
			return {
				success: false,
				error: {
					code: 'INTERNAL_ERROR',
					message: result.error || 'Failed to create payment session',
				},
			};
		}

		// Log successful initiation for monitoring
		console.log('Payment initiated successfully', {
			userId: user.id,
			planId,
			timestamp: new Date().toISOString(),
		});

		return {
			success: true,
			paymentLink: result.payment_link!,
		};
	} catch (error) {
		console.error('Unexpected error in payment initiation:', error);
		return {
			success: false,
			error: {
				code: 'INTERNAL_ERROR',
				message: 'An unexpected error occurred. Please try again.',
			},
		};
	}
}

export async function verifySubscriptionPaymentAction(
	paymentId: string,
	verificationData: CreemReturnParams & { signature?: string }
): Promise<VerifyPaymentResult> {
	try {
		// Comprehensive input validation
		const missingParams = [];
		if (!verificationData.checkout_id) missingParams.push('checkout_id');
		if (!verificationData.signature) missingParams.push('signature');
		if (!verificationData.subscription_id) missingParams.push('subscription_id');

		if (missingParams.length > 0) {
			return {
				success: false,
				error: {
					code: 'INVALID_INPUT',
					message: `Missing required parameters: ${missingParams.join(', ')}`,
				},
			};
		}

		// Verify signature
		if (!verifyCreemReturnSignature(verificationData)) {
			console.warn('Invalid signature attempt:', {
				checkoutId: verificationData.checkout_id,
				timestamp: new Date().toISOString(),
			});
			return {
				success: false,
				error: {
					code: 'FORBIDDEN',
					message: 'Invalid verification signature',
				},
			};
		}

		// Authentication
		const supabase = await createSupabaseServerClient();
		const {
			data: { user },
			error: authError,
		} = await supabase.auth.getUser();

		if (authError || !user) {
			return {
				success: false,
				error: {
					code: 'UNAUTHORIZED',
					message: 'Please log in to verify your payment',
				},
			};
		}

		// Get payment record
		const { data: payment, error: paymentError } = await PaymentManager.getPaymentById(
			supabase,
			paymentId
		);

		if (paymentError || !payment) {
			console.error('Payment not found:', {
				paymentId,
				userId: user.id,
				error: paymentError,
			});
			return {
				success: false,
				error: {
					code: 'NOT_FOUND',
					message: 'Payment record not found',
				},
			};
		}

		// Verify ownership
		if (payment.user_id !== user.id) {
			console.warn('Unauthorized payment access attempt:', {
				paymentId: payment.id,
				attemptedBy: user.id,
				owner: payment.user_id,
			});
			return {
				success: false,
				error: {
					code: 'FORBIDDEN',
					message: 'You do not have access to this payment',
				},
			};
		}

		// Check if already verified (idempotency)
		if (payment.status === 'success') {
			console.log('Payment already verified:', payment.id);
			return {
				success: true,
				paymentId: payment.id,
			};
		}

		// Update payment status
		const { data: updatedPayment, error: updateError } =
			await PaymentManager.markPaymentAsProcessing(supabase, payment.id, {
				subscription_id: verificationData.subscription_id!,
				customer_id: verificationData.customer_id!,
				product_id: payment.provider_metadata?.product_id!,
				payment_link: payment.provider_metadata?.payment_link!,
				provider: 'creem',
			});

		if (updateError || !updatedPayment) {
			return {
				success: false,
				error: {
					code: 'INTERNAL_ERROR',
					message: 'Failed to update payment status',
				},
			};
		}

		console.log('Payment verified successfully:', {
			paymentId: payment.id,
			userId: user.id,
			timestamp: new Date().toISOString(),
		});

		return {
			success: true,
			paymentId: payment.id,
		};
	} catch (error) {
		console.error('Unexpected error in payment verification:', error);
		return {
			success: false,
			error: {
				code: 'INTERNAL_ERROR',
				message: 'Failed to verify payment. Please contact support.',
			},
		};
	}
}

export async function generateCustomerPortalUrl(): Promise<CustomerPortalUrlResult> {
	try {
		const supabase = await createSupabaseServerClient();
		const {
			data: { user },
			error: authError,
		} = await supabase.auth.getUser();

		if (authError || !user) {
			return {
				success: false,
				error: {
					code: 'UNAUTHORIZED',
					message: 'Please log in.',
				},
			};
		}

		const customerId = await getExistingCustomerId(supabase, user.id, 'creem');

		if (!customerId) {
			return {
				success: false,
				error: { code: 'NOT_FOUND', message: 'No active subscription found.' },
			};
		}

		const portalUrl = await generateCustomerPortalLink(customerId);

		return { success: true, portalUrl: portalUrl };
	} catch (err) {
		console.error('Customer portal server action error:', err);
		return {
			success: false,
			error: { code: 'INTERNAL_ERROR', message: 'Internal server error.' },
		};
	}
}

export async function setupFreeSubscriptionAction(): Promise<SetupFreeSubscriptionResult> {
	try {
		const supabase = await createSupabaseServerClient();
		const {
			data: { user },
			error: authError,
		} = await supabase.auth.getUser();

		if (authError || !user) {
			return {
				success: false,
				error: {
					code: 'UNAUTHORIZED',
					message: 'Please log in to continue',
				},
			};
		}

		// Check if user already has a free subscription (idempotency)
		const hasFreeSub = await hasActiveSubscription(supabase, user.id, 'zeco-free-plan');
		if (hasFreeSub) {
			// User already has free subscription - ensure metadata is correct and return current user
			const { data: updatedUserData } = await supabase.auth.updateUser({
				data: {
					has_free_subscription: true,
					is_pro_user: false,
					subscription_plan_id: 'zeco-free-plan',
					subscription_status: 'active',
					last_subscription_update: new Date().toISOString(),
				},
			});

			return {
				success: true,
				updatedUser: updatedUserData.user,
			};
		}

		// Get the free subscription plan
		const { data: freePlan, error: planError } = getSubscriptionPlan('zeco-free-plan');
		if (planError || !freePlan) {
			console.error('Error fetching free plan:', planError);
			return {
				success: false,
				error: {
					code: 'INTERNAL_ERROR',
					message: 'Failed to get free subscription plan',
				},
			};
		}

		// Create user subscription
		const subscriptionId = generateUserSubscriptionId();
		const now = new Date().toISOString();
		const nextMonth = new Date();
		nextMonth.setMonth(nextMonth.getMonth() + 1);

		const { data: newSubscription, error: subscriptionError } = await createUserSubscription(
			supabase,
			{
				id: subscriptionId,
				user_id: user.id,
				subscription_plan_id: freePlan.id,
				status: 'active',
				start_at: now,
				end_at: nextMonth.toISOString(),
				next_billing_at: nextMonth.toISOString(),
				created_at: now,
				updated_at: now,
			}
		);

		if (subscriptionError || !newSubscription) {
			console.error('Error creating user subscription:', subscriptionError);
			return {
				success: false,
				error: {
					code: 'INTERNAL_ERROR',
					message: 'Failed to create user subscription',
				},
			};
		}

		// Create initial subscription cycle
		const cycleId = generateSubscriptionCycleId();
		const { error: cycleError } = await createSubscriptionCycle(supabase, {
			id: cycleId,
			user_subscription_id: subscriptionId,
			user_id: user.id,
			subscription_payment_id: undefined, // No payment for free subscription
			cycle_index: 0,
			cycle_start_at: now,
			cycle_end_at: nextMonth.toISOString(),
			tier1_tokens_granted: freePlan.tier1_token_limit,
			tier2_tokens_granted: freePlan.tier2_token_limit,
			credits_granted: freePlan.cycle_credits,
			tier1_tokens_used: 0,
			tier2_tokens_used: 0,
			credits_used: 0,
			is_current: true,
			created_at: now,
			updated_at: now,
		});

		if (cycleError) {
			console.error('Error creating subscription cycle:', cycleError);
			return {
				success: false,
				error: {
					code: 'INTERNAL_ERROR',
					message: 'Failed to create subscription cycle',
				},
			};
		}

		// Update subscription with cycle reference
		const { error: updateError } = await updateUserSubscription(supabase, subscriptionId, {
			current_subscription_cycle_id: cycleId,
			updated_at: now,
		});

		if (updateError) {
			console.error('Error updating subscription with cycle reference:', updateError);
			return {
				success: false,
				error: {
					code: 'INTERNAL_ERROR',
					message: 'Failed to update subscription with cycle reference',
				},
			};
		}

		// Update user metadata for free subscription and get updated user
		const { data: updatedUserData, error: userUpdateError } = await supabase.auth.updateUser({
			data: {
				has_free_subscription: true,
				is_pro_user: false,
				free_subscription_started_at: now,
				subscription_plan_id: 'zeco-free-plan',
				subscription_status: 'active',
				last_subscription_update: now,
			},
		});

		if (userUpdateError || !updatedUserData.user) {
			console.error('Error updating user metadata:', userUpdateError);
			return {
				success: false,
				error: {
					code: 'INTERNAL_ERROR',
					message: 'Failed to update user metadata',
				},
			};
		}

		console.log('Free subscription setup successful:', {
			userId: user.id,
			subscriptionId,
			cycleId,
			timestamp: now,
		});

		return {
			success: true,
			updatedUser: updatedUserData.user,
		};
	} catch (error) {
		console.error('Unexpected error in free subscription setup:', error);
		return {
			success: false,
			error: {
				code: 'INTERNAL_ERROR',
				message: 'Failed to setup free subscription. Please contact support.',
			},
		};
	}
}
