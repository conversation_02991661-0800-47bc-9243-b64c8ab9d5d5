'use client';
import FormComponent from '@/components/landing-page/FormComponent';
import { useMemo } from 'react';
import SparklesCore from '@/components/ui/sparkles';

export default function BetaAccessPage() {
	const particleSettings = useMemo(
		() => ({
			id: 'particles-background-login',
			background: 'transparent',
			minSize: 0.6,
			maxSize: 1.4,
			particleColor: '#9455D3',
			particleDensity: 50,
			speed: 1,
			className: 'w-full h-full',
		}),
		[]
	);
	return (
		<main className="relative flex items-center justify-center h-screen overflow-hidden">
			{/* Background Elements */}
			<SparklesCore
				{...particleSettings}
				className="absolute inset-0"
			/>
			<div
				className="absolute inset-0 bg-linear-to-tr from-zeco-purple/90 via-purple-600/60 to-transparent opacity-15"
				style={{
					filter: 'blur(120px)',
					animation: 'pulse 8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
				}}
			/>
			<FormComponent
				isOpen={true}
				noAccess={true}
				onClose={() => {}}
			/>

			{/* Decorative Elements - adjusted positioning */}
			<div className="absolute top-0 right-0 w-64 h-64 bg-zeco-purple/15 rounded-full blur-3xl" />
			<div className="absolute bottom-0 left-0 w-64 h-64 bg-purple-600/15 rounded-full blur-3xl" />
		</main>
	);
}
