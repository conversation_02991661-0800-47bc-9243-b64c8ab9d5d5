import '@/styles/globals.css';
import { Metadata, Viewport } from 'next';
import { siteConfig } from '@/config/site';
import { Providers } from './providers';
import { SpeedInsights } from '@vercel/speed-insights/next';
import { Analytics } from '@vercel/analytics/next';
import CookieConsentBanner from '@/components/cookie/CookieConsentBanner';

const ogImage = `${siteConfig.url}/preview.png`;

export const metadata: Metadata = {
	metadataBase: new URL(siteConfig.url),
	title: {
		default: siteConfig.name,
		template: `%s - ${siteConfig.name}`,
	},
	description: siteConfig.description,
	manifest: '/manifest.json',
	appleWebApp: {
		capable: true,
		statusBarStyle: 'black-translucent',
		title: 'ZECO AI',
	},
	formatDetection: {
		telephone: false,
	},
	openGraph: {
		type: 'website',
		url: siteConfig.url,
		title: siteConfig.name,
		description: siteConfig.description,
		images: [
			{
				url: ogImage,
				width: 2184,
				height: 1404,
				alt: siteConfig.name,
			},
		],
		siteName: siteConfig.name,
	},
	twitter: {
		card: 'summary_large_image',
		title: siteConfig.name,
		description: siteConfig.description,
		images: [ogImage],
		creator: '@zeco_ai',
	},
	icons: {
		icon: '/favicon.ico',
		shortcut: '/favicon-16x16.png',
		apple: '/apple-touch-icon.png',
	},
};

export const viewport: Viewport = {
	width: 'device-width',
	initialScale: 1,
	maximumScale: 1,
	userScalable: false,
	themeColor: '#000000',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
	return (
		<html
			lang="en"
			suppressHydrationWarning
		>
			<head>
				<meta
					name="description"
					content="One subscription for everything AI. Access models, assistants, playgrounds, tools, and more with ZECO AI."
				/>
				<meta
					name="keywords"
					content="AI, AI Agent, Subscription, Models, Assistants, Claude, Gemini, GPT, Research, Image, Video, Text, Creative, Tools"
				/>
				<meta
					name="apple-mobile-web-app-capable"
					content="yes"
				/>
				<meta
					name="apple-mobile-web-app-status-bar-style"
					content="black-translucent"
				/>
				<meta
					name="apple-mobile-web-app-title"
					content="ZECO"
				/>
				<link
					rel="apple-touch-icon"
					href="/apple-touch-icon.png"
				/>
			</head>
			<body className="font-inter antialiased overscroll-none">
				<Providers themeProps={{ attribute: 'class', defaultTheme: 'dark' }}>
					<div className="min-h-screen">
						<main className="flex-1 grow">{children}</main>
						<CookieConsentBanner />
					</div>
				</Providers>
				<SpeedInsights />
				<Analytics />
			</body>
		</html>
	);
}
