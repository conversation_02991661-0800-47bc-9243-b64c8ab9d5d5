import { redirect } from 'next/navigation';
import { AppProvider } from '@/app/(post-auth)/providers';
import { createSupabaseServerClient } from '@/utils/supabase/server';
import Sidebar from '@/components/sidebar/Sidebar';
import { getChatHistoriesForUser } from '@/service/db/chat-history';
import { setupFreeSubscriptionAction } from '@/app/subscriptions/actions';
import { addToast } from '@heroui/toast';

export default async function AppLayout({ children }: { children: React.ReactNode }) {
	const supabase = await createSupabaseServerClient();
	const {
		data: { user },
	} = await supabase.auth.getUser();

	if (!user) {
		redirect('/login');
	}

	let updatedUser = user;

	// Set up free subscription if user doesn't have one
	if (!user.user_metadata?.has_free_subscription) {
		const result = await setupFreeSubscriptionAction();

		if (result.success) {
			// Use the updated user returned from the action
			updatedUser = result.updatedUser;
		} else {
			console.error('Failed to setup free subscription:', result.error);
			addToast({
				title: 'Failed to setup free subscription',
				description: 'Please refresh the page and try again!',
				color: 'danger',
			});
		}
	}

	const initialChatHistoriesPromise = getChatHistoriesForUser(supabase, updatedUser.id);

	return (
		<div className="flex h-screen overflow-hidden">
			<AppProvider
				user={updatedUser}
				initialChatHistoriesPromise={initialChatHistoriesPromise}
			>
				<div className="flex h-full w-full flex-row">
					<Sidebar />
					{children}
				</div>
			</AppProvider>
		</div>
	);
}
