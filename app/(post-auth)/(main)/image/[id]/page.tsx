import ImageGenerationLayout from '@/components/chat/ImageGenerationLayout';
import { getChatMessagesFromChatId } from '@/service/managers/chat-manager';

interface ImageGenerationPageProps {
	params: Promise<{
		id: string;
	}>;
}

const ImageGenerationPage = async (props: ImageGenerationPageProps) => {
	const params = await props.params;
	const initialMessages = await getChatMessagesFromChatId('image', params.id);

	return (
		<ImageGenerationLayout
			id={params.id}
			messages={initialMessages}
		/>
	);
};

export default ImageGenerationPage;
