import { nanoid } from '@/lib';
import { ChatMessage } from '@/types/chat';
import ImageGenerationLayout from '@/components/chat/ImageGenerationLayout';
import { getChatMessagesFromChatId } from '@/service/managers/chat-manager';

interface NewImageGenerationPageProps {
	searchParams: Promise<{ [key: string]: string | undefined }>;
}

const NewImageGenerationPage = async ({ searchParams }: NewImageGenerationPageProps) => {
	const { shareId } = await searchParams;

	let initialMessages: ChatMessage[] = [];
	if (!!shareId) {
		initialMessages = await getChatMessagesFromChatId('image', shareId, true);
	}

	return (
		<ImageGenerationLayout
			id={nanoid()}
			messages={initialMessages}
			isShared={!!shareId && initialMessages.length > 0}
		/>
	);
};

export default NewImageGenerationPage;
