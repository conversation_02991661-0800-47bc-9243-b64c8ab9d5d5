import ChatLayout from '@/components/chat/ChatLayout';
import { getChatMessagesFromChatId } from '@/service/managers/chat-manager';

interface ChatPageProps {
	params: Promise<{
		id: string;
	}>;
}

const ChatPage = async (props: ChatPageProps) => {
	const params = await props.params;
	const initialMessages = await getChatMessagesFromChatId('chat', params.id);

	return (
		<ChatLayout
			id={params.id}
			messages={initialMessages}
		/>
	);
};

export default ChatPage;
