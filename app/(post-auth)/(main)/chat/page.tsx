import { nanoid } from '@/lib';
import { ChatMessage } from '@/types/chat';
import ChatLayout from '@/components/chat/ChatLayout';
import { getChatMessagesFromChatId } from '@/service/managers/chat-manager';

interface NewChatPageProps {
	searchParams: Promise<{ [key: string]: string | undefined }>;
}

const NewChatPage = async ({ searchParams }: NewChatPageProps) => {
	const { shareId } = await searchParams;

	let initialMessages: ChatMessage[] = [];
	if (!!shareId) {
		initialMessages = await getChatMessagesFromChatId('chat', shareId, true);
	}

	return (
		<ChatLayout
			id={nanoid()}
			messages={initialMessages}
			isShared={!!shareId && initialMessages.length > 0}
		/>
	);
};

export default NewChatPage;
