import { nanoid } from '@/lib';
import { ChatMessage } from '@/types/chat';
import VideoGenerationLayout from '@/components/chat/VideoGenerationLayout';
import { getChatMessagesFromChatId } from '@/service/managers/chat-manager';

interface NewVideoGenerationPageProps {
	searchParams: Promise<{ [key: string]: string | undefined }>;
}

const NewVideoGenerationPage = async ({ searchParams }: NewVideoGenerationPageProps) => {
	const { shareId } = await searchParams;

	let initialMessages: ChatMessage[] = [];
	if (!!shareId) {
		initialMessages = await getChatMessagesFromChatId('video', shareId, true);
	}

	return (
		<VideoGenerationLayout
			id={nanoid()}
			messages={initialMessages}
			isShared={!!shareId && initialMessages.length > 0}
		/>
	);
};

export default NewVideoGenerationPage;
