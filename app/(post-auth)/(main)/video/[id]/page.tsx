import VideoGenerationLayout from '@/components/chat/VideoGenerationLayout';
import { getChatMessagesFromChatId } from '@/service/managers/chat-manager';

interface VideoGenerationPageProps {
	params: Promise<{
		id: string;
	}>;
}

const VideoGenerationPage = async (props: VideoGenerationPageProps) => {
	const params = await props.params;
	const initialMessages = await getChatMessagesFromChatId('video', params.id);

	return (
		<VideoGenerationLayout
			id={params.id}
			messages={initialMessages}
		/>
	);
};

export default VideoGenerationPage;
