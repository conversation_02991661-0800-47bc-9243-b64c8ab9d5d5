import ChatHeader from '@/components/chat/ChatHeader';
import { MediaGenerationModelProvider, SharedChatProvider } from '../providers';

export default function MainLayout({ children }: { children: React.ReactNode }) {
	return (
		<div className="flex flex-col flex-1 h-full">
			<SharedChatProvider>
				<MediaGenerationModelProvider>
					<ChatHeader />
					<div className="flex flex-1 justify-center overflow-auto">{children}</div>
				</MediaGenerationModelProvider>
			</SharedChatProvider>
		</div>
	);
}
