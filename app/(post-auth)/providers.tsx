'use client';

import { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';
import { usePathname } from 'next/navigation';
import { User } from '@supabase/auth-js';
import { ModelDisplayName } from '@/models/conversational/conversational-models';
import { ImageModelDisplayName } from '@/models/image/image-generation-models';
import { ChatHistory, ChatType } from '@/types/chat';
import { ImageGenerationParams } from '@/models/image/image-generation-constraints';
import { VideoModelDisplayName } from '@/models/video/video-generation-models';
import { useSubscriptionListener } from '@/lib/hooks/use-subscription-listener';

export type ModelType = ModelDisplayName | ImageModelDisplayName | VideoModelDisplayName;
export type MediaGenerationMode = 'CREATE' | 'EDIT' | 'NONE';

// TODO: Add video generation params
export interface SelectedModelPromptDetailsContextType {
	prompt: string;
	params: ImageGenerationParams | undefined;
}

interface SharedChatContextType {
	type?: ChatType;
	chatId?: string;
	isShareable: boolean;
}

const UserContext = createContext<User | undefined>(undefined);

const ModelContext = createContext<ModelType | undefined>(undefined);
const ModelUpdateContext = createContext<((model: ModelType) => void) | undefined>(undefined);

const ChatHistoryContext = createContext<ChatHistory[] | null | undefined>(undefined);
const ChatHistoryUpdateContext = createContext<
	React.Dispatch<React.SetStateAction<ChatHistory[] | null>> | undefined
>(undefined);

const SharedChatContext = createContext<SharedChatContextType | undefined>(undefined);
const SharedChatUpdateContext = createContext<
	((sharedChatDetails: SharedChatContextType) => void) | undefined
>(undefined);

const SidebarVisibilityContext = createContext<boolean>(false);
const SidebarVisibilityUpdateContext = createContext<((isVisible: boolean) => void) | undefined>(
	undefined
);

const SelectedModelPromptDetailsContext = createContext<
	SelectedModelPromptDetailsContextType | undefined
>(undefined);

const SelectedModelPromptDetailsUpdateContext = createContext<
	((details: SelectedModelPromptDetailsContextType) => void) | undefined
>(undefined);

const MediaGenerationModeContext = createContext<MediaGenerationMode | undefined>(undefined);
const MediaGenerationModeUpdateContext = createContext<
	((mediaGenerationMode: MediaGenerationMode) => void) | undefined
>(undefined);

export function useUserContext() {
	const context = useContext(UserContext);
	if (context === undefined) {
		throw new Error('useUser must be used within a UserProvider');
	}
	return context;
}

export function useModelContext() {
	const context = useContext(ModelContext);
	if (context === undefined) {
		throw new Error('useModel must be used within a ModelProvider');
	}
	return context;
}

export function useModelUpdateContext() {
	const context = useContext(ModelUpdateContext);
	if (context === undefined) {
		throw new Error('useModelUpdate must be used within a ModelProvider');
	}
	return context;
}

export function useChatHistoryContext() {
	const context = useContext(ChatHistoryContext);
	if (context === undefined) {
		throw new Error('useChatHistory must be used within a ChatHistoryProvider');
	}
	return context;
}

export function useChatHistoryUpdateContext() {
	const context = useContext(ChatHistoryUpdateContext);
	if (context === undefined) {
		throw new Error('useChatHistoryUpdate must be used within a ChatHistoryProvider');
	}
	return context;
}

export function useSharedChatContext() {
	const context = useContext(SharedChatContext);
	if (context === undefined) {
		throw new Error('useSharedChatContext must be used within a SharedChatProvider');
	}
	return context;
}

export function useSharedChatUpdateContext() {
	const context = useContext(SharedChatUpdateContext);
	if (context === undefined) {
		throw new Error('useSharedChatUpdateContext must be used within a SharedChatProvider');
	}
	return context;
}

export function useSidebarVisibility() {
	const context = useContext(SidebarVisibilityContext);
	if (context === undefined) {
		throw new Error('useSidebarVisibility must be used within a SidebarVisibilityProvider');
	}
	return context;
}

export function useSidebarVisibilityUpdate() {
	const context = useContext(SidebarVisibilityUpdateContext);
	if (context === undefined) {
		throw new Error(
			'useSidebarVisibilityUpdate must be used within a SidebarVisibilityProvider'
		);
	}
	return context;
}

export function useSelectedModelPromptDetailsContext() {
	const context = useContext(SelectedModelPromptDetailsContext);
	if (context === undefined) {
		throw new Error(
			'useSelectedModelPromptDetailsContext must be used within a SelectedModelImageProvider'
		);
	}
	return context;
}

export function useSelectedModelPromptDetailsUpdateContext() {
	const context = useContext(SelectedModelPromptDetailsUpdateContext);
	if (context === undefined) {
		throw new Error(
			'useSelectedModelImageDetailsUpdate must be used within a SelectedModelImageProvider'
		);
	}
	return context;
}

export function useMediaGenerationModeContext() {
	const context = useContext(MediaGenerationModeContext);
	if (context === undefined) {
		throw new Error(
			'useMediaGenerationModeContext must be used within a SelectedModelImageProvider'
		);
	}
	return context;
}

export function useMediaGenerationModeUpdateContext() {
	const context = useContext(MediaGenerationModeUpdateContext);
	if (context === undefined) {
		throw new Error(
			'useMediaGenerationModeUpdateContext must be used within a SelectedModelImageProvider'
		);
	}
	return context;
}

export function UserProvider({ children, user }: { children: ReactNode; user: User }) {
	return <UserContext.Provider value={user}>{children}</UserContext.Provider>;
}

export function ModelProvider({ children }: { children: ReactNode }) {
	const pathname = usePathname();
	let type: ChatType = 'chat';
	if (pathname?.startsWith('/image')) {
		type = 'image';
	} else if (pathname?.startsWith('/video')) {
		type = 'video';
	}

	const getDefaultModelForType = (type: ChatType): ModelType => {
		switch (type) {
			case 'image':
				return ImageModelDisplayName.Stable_Diffusion_XL;
			case 'video':
				return VideoModelDisplayName.Luma_Ray_2_Flash;
			case 'chat':
			default:
				return ModelDisplayName.GPT_5_mini;
		}
	};

	const [selectedModel, setSelectedModel] = useState<ModelType>(() =>
		getDefaultModelForType(type)
	);

	const setModel = useCallback((model: ModelType) => {
		setSelectedModel(model);
	}, []);

	useEffect(() => {
		setSelectedModel(getDefaultModelForType(type));
	}, [type]);

	return (
		<ModelContext.Provider value={selectedModel}>
			<ModelUpdateContext.Provider value={setModel}>{children}</ModelUpdateContext.Provider>
		</ModelContext.Provider>
	);
}

export function ChatHistoryProvider({
	children,
	initialChatHistoriesPromise,
}: {
	children: ReactNode;
	initialChatHistoriesPromise: Promise<{ data: ChatHistory[] | null; error: any }>;
}) {
	const [chatHistory, setChatHistory] = useState<ChatHistory[] | null>(null);

	useEffect(() => {
		initialChatHistoriesPromise.then(({ data: initialChatHistories, error }) => {
			if (initialChatHistories === null) {
				// console.error('Failed to fetch chat histories:', error);
				setChatHistory([]);
			} else {
				setChatHistory(initialChatHistories);
			}
		});
	}, [initialChatHistoriesPromise]);

	return (
		<ChatHistoryContext.Provider value={chatHistory}>
			<ChatHistoryUpdateContext.Provider value={setChatHistory}>
				{children}
			</ChatHistoryUpdateContext.Provider>
		</ChatHistoryContext.Provider>
	);
}

export function SharedChatProvider({ children }: { children: ReactNode }) {
	const [sharedChatDetails, setSharedChatDetails] = useState<SharedChatContextType>({
		isShareable: false,
	});
	const updateSharedChatDetails = useCallback((sharedChatDetails: SharedChatContextType) => {
		setSharedChatDetails(sharedChatDetails);
	}, []);

	return (
		<SharedChatContext.Provider value={sharedChatDetails}>
			<SharedChatUpdateContext.Provider value={updateSharedChatDetails}>
				{children}
			</SharedChatUpdateContext.Provider>
		</SharedChatContext.Provider>
	);
}

export function SidebarVisibilityProvider({ children }: { children: ReactNode }) {
	const [isVisible, setIsVisible] = useState<boolean>(false);

	return (
		<SidebarVisibilityContext.Provider value={isVisible}>
			<SidebarVisibilityUpdateContext.Provider value={setIsVisible}>
				{children}
			</SidebarVisibilityUpdateContext.Provider>
		</SidebarVisibilityContext.Provider>
	);
}

export function SelectedModelImageDetailsProvider({ children }: { children: ReactNode }) {
	const pathname = usePathname();

	const [selectedModelImageDetails, setSelectedModelImageDetails] =
		useState<SelectedModelPromptDetailsContextType>({
			prompt: '',
			params: undefined,
		});

	const updateSelectedModelImageDetails = useCallback(
		(details: SelectedModelPromptDetailsContextType) => {
			setSelectedModelImageDetails(details);
		},
		[]
	);

	useEffect(() => {
		setSelectedModelImageDetails({
			prompt: '',
			params: undefined,
		});
	}, [pathname]);

	return (
		<SelectedModelPromptDetailsContext.Provider value={selectedModelImageDetails}>
			<SelectedModelPromptDetailsUpdateContext.Provider
				value={updateSelectedModelImageDetails}
			>
				{children}
			</SelectedModelPromptDetailsUpdateContext.Provider>
		</SelectedModelPromptDetailsContext.Provider>
	);
}

export function MediaGenerationModelProvider({ children }: { children: ReactNode }) {
	const pathname = usePathname();

	let type: ChatType = 'chat';
	if (pathname?.startsWith('/image')) {
		type = 'image';
	} else if (pathname?.startsWith('/video')) {
		type = 'video';
	}

	const [mediaGenerationMode, setMediaGenerationMode] = useState<MediaGenerationMode>('NONE');
	const updateMediaGenerationMode = useCallback((mediaGenerationMode: MediaGenerationMode) => {
		setMediaGenerationMode(mediaGenerationMode);
	}, []);

	useEffect(() => {
		setMediaGenerationMode(type === 'image' ? 'CREATE' : 'NONE');
	}, [type]);

	return (
		<MediaGenerationModeContext.Provider value={mediaGenerationMode}>
			<MediaGenerationModeUpdateContext.Provider value={updateMediaGenerationMode}>
				{children}
			</MediaGenerationModeUpdateContext.Provider>
		</MediaGenerationModeContext.Provider>
	);
}

function SubscriptionManager() {
	useSubscriptionListener();
	return null; // This component does not render anything
}

export function AppProvider({
	children,
	user,
	initialChatHistoriesPromise,
}: {
	children: ReactNode;
	user: User;
	initialChatHistoriesPromise: Promise<{ data: ChatHistory[] | null; error: any }>;
}) {
	return (
		<UserProvider user={user}>
			<ModelProvider>
				<ChatHistoryProvider initialChatHistoriesPromise={initialChatHistoriesPromise}>
					<SidebarVisibilityProvider>
						<SelectedModelImageDetailsProvider>
							<SubscriptionManager />
							{children}
						</SelectedModelImageDetailsProvider>
					</SidebarVisibilityProvider>
				</ChatHistoryProvider>
			</ModelProvider>
		</UserProvider>
	);
}
