'use server';

import {
	givePublicAccessToChat as givePublicAccessToChatQuery,
	revokePublicAccessToChat as revokePublicAccessToChatQuery,
} from '@/service/db/chat-history';
import { createSupabaseServerClient } from '@/utils/supabase/server';

export async function givePublicAccessToChat(chatId: string): Promise<void> {
	const supabase = await createSupabaseServerClient();
	const { error } = await givePublicAccessToChatQuery(supabase, chatId);
	if (error) {
		console.error('Error sharing chat: ', error);
		throw new Error('Failed to share chat');
	}
}

export async function revokePublicAccessToChat(chatId: string): Promise<void> {
	const supabase = await createSupabaseServerClient();
	const { error } = await revokePublicAccessToChatQuery(supabase, chatId);
	if (error) {
		console.error('Error removing share from chat:', error);
		throw new Error('Failed to remove share from chat');
	}
}

export async function submitFeedback(feedbackText: string): Promise<void> {
	const trimmedText = feedbackText.trim();
	if (trimmedText.length < 10) {
		throw new Error('Feedback must be at least 10 characters long');
	}
	if (trimmedText.length > 1000) {
		throw new Error('Feedback cannot exceed 1000 characters');
	}

	const supabase = await createSupabaseServerClient();

	const {
		data: { user },
		error: authError,
	} = await supabase.auth.getUser();

	if (authError || !user) {
		console.error('Authentication error:', authError);
		throw new Error('User not authenticated');
	}

	// TODO: Create a feedback manager, if required later.
	const { error } = await supabase.from('feedback').insert({
		user_id: user.id,
		user_name: user.user_metadata?.name || user.email || null,
		feedback_text: trimmedText,
		created_at: new Date().toISOString(),
	});

	if (error) {
		console.error('Error submitting feedback:', error);
		throw new Error('Failed to submit feedback');
	}
}
