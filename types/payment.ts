/* ────────────────────────────────────────────────────────────────────
   PAYMENT ENUMS & SHARED TYPES
   ───────────────────────────────────────────────────────────────── */

export type PaymentStatus =
	| 'pending'
	| 'processing'
	| 'success'
	| 'failed'
	| 'cancelled'
	| 'refunded'
	| 'expired';

export type Provider = 'creem';
export type CurrencyCode = 'INR' | 'USD';
export type BillingPeriod = 'monthly' | 'weekly' | 'lifetime';

import { nanoid } from '@/lib';

export interface Address {
	city: string;
	state: string;
	country: string;
	street: string;
	zipcode: string;
}

/* ────────────────────────────────────────────────────────────────────
   CATALOGUE OBJECTS
   ───────────────────────────────────────────────────────────────── */

export interface SubscriptionPlan {
	id: string;
	name: string;
	tier1_token_limit: number;
	tier2_token_limit: number;
	cycle_credits: number;
	price_minor: number;
	currency: CurrencyCode;
	billing_period: BillingPeriod;
	description?: string;
	active: boolean;
	created_at: string;
	updated_at: string;
}

export interface CreditPackPlan {
	id: string;
	name: string;
	credits: number;
	price_minor: number;
	currency: CurrencyCode;
	description?: string;
	active: boolean;
	created_at: string;
	updated_at: string;
}

/* ────────────────────────────────────────────────────────────────────
   USER-LEVEL ENTITLEMENTS
   ───────────────────────────────────────────────────────────────── */

export interface UserSubscription {
	id: string;
	user_id: string;
	subscription_plan_id: string; // FK → SubscriptionPlan
	status: 'active' | 'past_due' | 'cancelled' | 'expired';
	start_at: string; // initial activation
	end_at: string; // absolute end (updated on renewal)
	next_billing_at?: string;
	current_subscription_cycle_id?: string; // FK → SubscriptionCycle
	last_subscription_payment_id?: string; // FK → SubscriptionPayment
	provider_subscription_id?: string; // PG only
	created_by_webhook_event_id?: string; // FK → WebhookEvent (creation audit trail)
	last_modified_by_webhook_event_id?: string; // FK → WebhookEvent (latest modification)
	created_at: string;
	updated_at: string;
}

// ID generation function for UserSubscription
export function generateUserSubscriptionId(): string {
	return `sub_${nanoid()}`;
}

/**
 * One row per billing cycle, gives automatic history.
 * Token/credit usage is incremented in-place; bonus grants copy into *_granted.
 */
export interface SubscriptionCycle {
	id: string;
	user_subscription_id: string; // FK → UserSubscription
	user_id: string; // Direct reference for easier queries
	subscription_payment_id?: string; // FK → SubscriptionPayment (null for free cycles)
	cycle_index: number; // 0,1,2…
	cycle_start_at: string;
	cycle_end_at: string;

	tier1_tokens_granted: number;
	tier2_tokens_granted: number;
	credits_granted: number;

	tier1_tokens_used: number;
	tier2_tokens_used: number;
	credits_used: number;

	is_current: boolean; // true for active cycle
	created_at: string;
	updated_at: string;
}

// ID generation function for SubscriptionCycle
export function generateSubscriptionCycleId(): string {
	return `cycle_${nanoid()}`;
}

/**
 * Extra tokens or credits granted mid-cycle
 * (promo, compensation, paid top-up…).
 */
export interface CycleGrant {
	id: string;
	subscription_cycle_id: string; // FK → SubscriptionCycle
	grant_type: 'token' | 'credit';
	tier?: 1 | 2; // required if grant_type == 'token'
	amount: number; // tokens or credits added
	reason: 'promo' | 'compensation' | 'purchase' | 'manual';
	subscription_payment_id?: string; // FK → SubscriptionPayment (if paid)
	granted_at: string;
}

/**
 * Every credit-pack purchase (or free grant) lives here.
 * Multiple rows per user are fine; debit FIFO or by any rule you like.
 */
export interface UserCreditPack {
	id: string;
	user_id: string;
	credit_pack_plan_id: string; // FK → CreditPackPlan
	credit_pack_payment_id?: string; // FK → CreditPackPayment (null ⇒ free)
	credits_granted: number; // snapshot of plan.credits
	credits_used: number; // increment per spend
	expiry_at?: string; // null ⇒ never expires
	status: 'active' | 'exhausted' | 'expired';
	created_at: string;
	updated_at: string;
}

/* ────────────────────────────────────────────────────────────────────
   USAGE LOG (credit spends – tokens tracked in SubscriptionCycle)
   ───────────────────────────────────────────────────────────────── */

export interface CreditUsageLog {
	id: string;
	user_id: string;
	subscription_cycle_id?: string; // present if credits from subscription
	user_credit_pack_id?: string; // present if credits from a pack
	credits_consumed: number;
	feature: string; // 'image_generation', 'video', …
	metadata?: Record<string, unknown>;
	created_at: string;
}

/* ────────────────────────────────────────────────────────────────────
   PAYMENTS
   ───────────────────────────────────────────────────────────────── */

export interface PaymentBase {
	id: string; // Primary key (txn_${nanoid()} for transactions, sub_${nanoid()} for subscriptions, etc.)
	user_id: string;

	amount_minor: number;
	currency: CurrencyCode;
	status: PaymentStatus;
	payment_method?: string;

	provider: Provider;
	provider_metadata?: ProviderMetadata; // JSONB field containing all provider-specific data (optional during migration)

	webhook_event_id?: string; // FK → WebhookEvent (for webhook-initiated payments)
	error?: string;
	initiated_at: string;
	completed_at?: string;
	created_at: string;
	updated_at: string;
}

export interface SubscriptionPayment extends PaymentBase {
	subscription_plan_id: string; // FK → SubscriptionPlan
}

// ID generation function for SubscriptionPayment
export function generateSubscriptionPaymentId(): string {
	return `txn_${nanoid()}`;
}

export interface CreditPackPayment extends PaymentBase {
	credit_pack_plan_id: string; // FK → CreditPackPlan
}

/* ────────────────────────────────────────────────────────────────────
   CHECKOUT SESSION METADATA
   ───────────────────────────────────────────────────────────────── */

/**
 * Strongly typed metadata that gets passed to Creem during checkout session creation
 * and is later received back in webhook events for processing.
 */
export interface CreemCheckoutSessionMetadata {
	user_email: string;
	user_name: string;
	user_id: string;
	subscription_plan_id: string;
	subscription_payment_id: string;
}

/* ────────────────────────────────────────────────────────────────────
   PROVIDER-SPECIFIC METADATA (plug into provider_metadata field)
   ───────────────────────────────────────────────────────────────── */

export interface CreemProviderMetadata {
	provider: 'creem';
	customer_id?: string;
	subscription_id?: string;
	product_id: string;
	transaction_id?: string;
	payment_link?: string;
}

export type ProviderMetadata = CreemProviderMetadata;

/* ────────────────────────────────────────────────────────────────────
   API REQUEST/RESPONSE TYPES
   ───────────────────────────────────────────────────────────────── */

export interface SubscriptionPaymentInitiateRequest {
	planId: string;
}

export interface SubscriptionPaymentInitiateResponse {
	success: boolean;
	transaction_id: string;
	payment_link?: string;
	error?: string;
}

export interface CustomerData {
	email: string;
	name: string;
	phone_number?: string;
}

/* ────────────────────────────────────────────────────────────────────
   CREEM SDK TYPES -> UNUSED
   ───────────────────────────────────────────────────────────────── */
import {
	CheckoutEntity,
	CustomerEntity,
	OrderEntity,
	ProductEntity,
	SubscriptionEntity,
} from 'creem/models/components';

export type CreemCustomer = CustomerEntity;
export type CreemProduct = ProductEntity;
export type CreemSubscription = SubscriptionEntity;
export type CreemOrder = OrderEntity;
export type CreemCheckout = CheckoutEntity;

/* ────────────────────────────────────────────────────────────────────
   WEBHOOK EVENTS TABLE
   ───────────────────────────────────────────────────────────────── */

export type WebhookEventStatus = 'pending' | 'processed' | 'failed';
export type WebhookProvider = 'creem';

export interface WebhookEvent {
	id: string;
	webhook_id: string; // Provider webhook ID (unique per provider)
	event_type: string; // 'subscription.paid', 'subscription.canceled', etc.
	provider: WebhookProvider;
	status: WebhookEventStatus;
	payload: Record<string, any>; // Full webhook payload for debugging
	processed_at?: string;
	error_message?: string;
	retry_count: number;
	created_at: string;
	updated_at: string;
}

// ID generation function for WebhookEvent
export function generateWebhookEventId(): string {
	return `wh_evt_${nanoid()}`;
}

/* ────────────────────────────────────────────────────────────────────
   CREEM WEBHOOK TYPES
   ───────────────────────────────────────────────────────────────── */

// Base interfaces for common objects
export interface CreemWebhookCustomer {
	id: string;
	object: 'customer';
	email: string;
	name: string;
	country: string;
	created_at: string;
	updated_at: string;
	mode: string;
}

export interface CreemWebhookProduct {
	id: string;
	name: string;
	description: string;
	image_url: string | null;
	price: number;
	currency: string;
	billing_type: 'recurring' | 'one-time';
	billing_period: 'every-month' | 'every-year' | string;
	status: 'active' | 'inactive';
	tax_mode: 'exclusive' | 'inclusive';
	tax_category: string;
	default_success_url: string;
	created_at: string;
	updated_at: string;
	mode: string;
}

export interface CreemWebhookOrder {
	id: string;
	customer: string;
	product: string;
	amount: number;
	currency: string;
	status: 'paid' | 'pending' | 'failed';
	type: 'recurring' | 'one-time';
	created_at: string;
	updated_at: string;
	mode: string;
}

export interface CreemWebhookSubscription {
	id: string;
	object: 'subscription';
	product: CreemWebhookProduct;
	customer: CreemWebhookCustomer;
	collection_method: 'charge_automatically' | 'send_invoice';
	status: 'active' | 'canceled' | 'expired' | 'past_due';
	last_transaction_id: string;
	last_transaction_date: string;
	next_transaction_date?: string;
	current_period_start_date: string;
	current_period_end_date: string;
	canceled_at: string | null;
	created_at: string;
	updated_at: string;
	metadata: CreemCheckoutSessionMetadata;
	mode: string;
}

export interface CreemWebhookCheckout {
	id: string;
	object: 'checkout';
	request_id: string;
	order: CreemWebhookOrder;
	product: CreemWebhookProduct;
	customer: CreemWebhookCustomer;
	subscription: {
		id: string;
		object: 'subscription';
		product: string;
		customer: string;
		collection_method: 'charge_automatically' | 'send_invoice';
		status: 'active' | 'canceled' | 'expired' | 'past_due';
		canceled_at: string | null;
		created_at: string;
		updated_at: string;
		metadata: CreemCheckoutSessionMetadata;
		mode: string;
	};
	custom_fields: any[];
	status: 'completed' | 'pending' | 'failed';
	metadata: CreemCheckoutSessionMetadata;
	mode: string;
}

// Discriminated union type for different webhook events
export type CreemWebhookPayload =
	| {
			id: string;
			eventType: 'subscription.paid';
			created_at: number;
			object: CreemWebhookSubscription;
	  }
	| {
			id: string;
			eventType: 'subscription.canceled';
			created_at: number;
			object: CreemWebhookSubscription;
	  }
	| {
			id: string;
			eventType: 'subscription.expired';
			created_at: number;
			object: CreemWebhookSubscription;
	  }
	| {
			id: string;
			eventType: 'checkout.completed';
			created_at: number;
			object: CreemWebhookCheckout;
	  }
	| {
			id: string;
			eventType: 'subscription.active';
			created_at: number;
			object: CreemWebhookSubscription;
	  };

// Helper type for extracting specific event payloads
export type CreemWebhookEventPayload<T extends CreemWebhookPayload['eventType']> = Extract<
	CreemWebhookPayload,
	{ eventType: T }
>;
