interface RichSnippetDetectedExtensions {
	price?: string;
	currency?: string;
	rating?: number;
	reviews?: number;
	reviews_link?: string;
	price_from?: string;
	price_to?: string;
	address?: string;
	top_answer?: string;
	vote_count?: string;
	link?: string;
	answer_count?: string;
	[key: string]: string | number | undefined; // For month/week as key
}

interface RichSnippetSection {
	extensions?: string[];
	detected_extensions?: RichSnippetDetectedExtensions;
}

interface Sitelink {
	title: string;
	link: string;
	snippet?: string;
}

interface KeyMoment {
	text: string;
	time: string;
	link: string;
	thumbnail: string;
}

interface CarouselItem {
	title: string;
	link: string;
	thumbnail: string;
	snippet: string;
	source: string;
	author?: string;
	duration?: string;
	date?: string;
	extensions?: string[];
	rating?: string;
	extracted_rating?: number;
	reviews?: string;
	extracted_reviews?: number;
}

interface AboutThisResult {
	source: {
		description: string;
		source_info_link: string;
		security: string;
		icon: string;
	};
	keywords?: string[];
	languages?: string[];
	regions?: string[];
}

interface LatestNewsItem {
	title: string;
	link: string;
	thumbnail: string;
	source: string;
	source_logo: string;
	date: string;
}

interface Answer {
	link: string;
	answer: string;
	top_answer: boolean;
	votes: number;
}

interface RelatedQuestion {
	question: string;
	snippet: string;
}

/**
 * Google search main results are called organic results. Some results are simple and straightforward.
 * Others include rich data like reviews, thumbnails, and other special snippets. SerpApi is able to scrape, extract, and make sense of both simple and more complex organic results. When SerpApi encounters organic results, we add them to our JSON output as the array organic_results.
 * For each organic result, we are able to extract its position, title, link, redirect_link, source, displayed_link, thumbnail, favicon, date, author, cited_by, extracted_cited_by, snippet, cached_page_link, about_page_link, related_pages_link, sitelinks:inline, sitelinks:expanded, rich_snippet:top, rich_snippet:bottom, rich_snippet_table, extensions, reviews, ratings, answers, related_questions, carousel and more.
 */
export interface OrganicResult {
	position: number;
	title: string;
	link: string;
	redirect_link?: string;
	displayed_link?: string;
	thumbnail?: string;
	date?: string;
	author?: string;
	cited_by?: string;
	extracted_cited_by?: number;
	favicon?: string;
	snippet?: string;
	snippet_highlighted_words?: string[];
	duration?: string;
	key_moments?: KeyMoment[];
	video_link?: string;
	sitelinks_search_box?: boolean;
	sitelinks?: {
		inline?: Sitelink[];
		expanded?: Sitelink[];
		list?: Sitelink[];
	};
	rich_snippet?: {
		top?: RichSnippetSection;
		bottom?: RichSnippetSection;
	};
	carousel?: CarouselItem[];
	about_this_result?: AboutThisResult;
	about_page_link?: string;
	about_page_serpapi_link?: string;
	cached_page_link?: string;
	related_pages_link?: string;
	source?: string;
	latest_news?: LatestNewsItem[];
	answers?: Answer[];
	related_questions?: RelatedQuestion[];
}

/**
 * The Google Available On Results API allows a user to scrape the Available On results of a Google search.
 * These results are usually seen for media, such as: movies, music, books, etc. SerpApi is able to scrape, extract, and make sense of this information.
 * When SerpApi encounters "Available On" results, we add them to our JSON output. We are able to extract: name, link, price and thumbnail.
 */
export interface AvailableOn {
	name: string;
	link: string;
	price: string;
	thumbnail: string;
}

/**
 * The Discussions and Forums Results API allows a user to scrape Discussions and Forums results of a Google search.
 * We are able to extract the: title, link, date and source of each listed item.
 */
export interface DiscussionsAndForums {
	title: string;
	link: string;
	date: string;
	extensions: string[];
	source: string;
}

/**
 * The Google events results API allows a user to scrape events results from a regular Google Search page.
 * SerpApi is able to make sense of this information and extract title, date, address, link, thumbnail and more.
 * To get started, you can try these searches: "events near me" or "family friendly events" (you have to set a location parameter first).
 * You can also add a location to your search. For example, "concerts in Chicago" (you do not have to set a location parameter).
 */
export interface Events {
	title: string;
	date: {
		start_date: string;
		when: string;
	};
	address: string[];
	link: string;
	thumbnail: string;
}

/**
 * For some products (depending on your location), Google search will include the "Immersive Products" block. When this block is clicked, Google will open a pop-up with complete product details.
 * SerpApi is able to extract and make sense of this information.
 */
export interface ImmersiveProducts {
	thumbnail: string;
	source_logo: string;
	source: string;
	title: string;
	snippets: {
		text: string;
		link: string;
		source: string;
	}[];
	rating: number;
	reviews: number;
	price: string;
	extracted_price: number;
	original_price: string;
	extracted_original_price: number;
	extensions: string[];
	immersive_product_page_token: string;
	serpapi_link: string;
	product_id: string;
	serpapi_product_api: string;
}

/**
 * For some searches, Google search includes a inline images. SerpApi is able to scrape, extract, and make sense of this information.
 */
export interface InlineImages {
	source: string;
	thumbnail: string;
	original: string;
	title: string;
}

export interface InlineVideos {
	position: number;
	title: string;
	link: string;
	thumbnail: string;
	channel: string;
	duration: string;
	platform: string;
	date: string;
}

/**
 * For some searches, Google organic results include inline shopping products. SerpApi is able to extract and make sense of this information.
 * Inline shopping can contain block_position, title, price, link, source, rating, reviews, thumbnail, extensions, second_hand_condition, old_price, extracted_old_price, shipping and more.
 */
export interface ShoppingResults {
	position: number;
	title: string;
	link: string;
	source: string;
	price: string;
	extracted_price: number;
	old_price: string;
	extracted_old_price: number;
	second_hand_condition: string;
	shipping: string;
	rating: number;
	reviews: number;
	reviews_original: string;
	extensions: string[];
	thumbnail: string;
	product_id: string;
	serpapi_product_api: string;
}

/**
 * Certain Google searches return a Latest Posts section. These latest posts are parsed and exist within the latest_posts array in the JSON output.
 * Latest posts can contain title, link, thumbnail, source, source_icon, channel, and more.
 */
export interface LatestPosts {
	title: string;
	link: string;
	thumbnail: string;
	source: string;
	source_icon: string;
	channel: string;
	date: string;
	views: string;
	extracted_views: number;
}

/**
 * For some searches, Google search includes the "Local news" block. SerpApi is able to scrape, extract, and make sense of this information.
 */
export interface LocalNews {
	source: string;
	thumbnail: string;
	title: string;
	link: string;
	date: string;
}

/**
 * When SerpApi encounters a local map and/or local results, we add them to our JSON output.
 * From the local_map, we are able to extract link, gps_coordinates, image and streetview information.
 * From the local_results, we are able to extract position title, reviews, price, type, address, description, hours, extensions, thumbnail, gps_coordinates, place_id (ludocid and lsig parameters) and more.
 */
export interface LocalResults {
	more_locations_link: string;
	places: {
		position: number;
		title: string;
		place_id: string;
		lsig: string;
		place_id_search: string;
		rating: number;
		reviews: number;
		price: string;
		type: string;
		address: string;
		hours: string;
		thumbnail: string;
		service_options: {
			dine_in: boolean;
			takeout: boolean;
			delivery?: boolean;
		};
		gps_coordinates: {
			latitude: number;
			longitude: number;
		};
	}[];
}

/**
 * Our Google News API allows you to scrape results from the Google News page.
 * To scrape Google News results with SerpApi, create a search with tbm parameter set to nws. (I.e., tbm=nws).
 */
export interface NewsResults {
	position: number;
	title: string;
	link: string;
	date: string;
	source: string;
	snippet: string;
	thumbnail: string;
}

/**
 * For some searches, Google search results includes a Scholarly Articles block. SerpApi is able to scrape, extract and make sense of this information.
 */
export interface ScholarlyArticles {
	title: string;
	link: string;
	serpapi_link: string;
	articles: {
		title: string;
		link: string;
		author: string;
		cited_by: string;
		highlighted_words: string[];
	}[];
}

export interface SerpApiResponse {
	available_on?: AvailableOn[];
	discussions_and_forums?: DiscussionsAndForums[];
	events_results?: Events[];
	immersive_products?: ImmersiveProducts[];
	inline_images?: InlineImages[];
	inline_videos?: InlineVideos[];
	organic_results?: OrganicResult[];
	// knowledge_graph?: any;
	shopping_results?: ShoppingResults[];
	latest_posts?: LatestPosts[];
	local_news?: LocalNews[];
	local_results?: LocalResults;
	news_results?: NewsResults[];
	scholarly_articles?: ScholarlyArticles;
	// sports_results?: any;
	// top_stories?: any;
	// twitter_results?: any;
}

export const filterResultsForLLM = (rawResults: SerpApiResponse) => {
	const results: any = {};

	if (rawResults.available_on) {
		results.available_on = rawResults.available_on.map((item) => ({
			name: item.name,
			link: item.link,
			price: item.price,
		}));
	}

	if (rawResults.discussions_and_forums) {
		results.discussions_and_forums = rawResults.discussions_and_forums.map((result) => ({
			title: result.title,
			link: result.link,
			date: result.date,
			source: result.source,
			extensions: result.extensions || [],
		}));
	}

	if (rawResults.events_results) {
		results.events_results = rawResults.events_results.map((event) => ({
			title: event.title,
			date: {
				start_date: event.date.start_date,
				when: event.date.when,
			},
			address: event.address,
			link: event.link,
			thumbnail: event.thumbnail,
		}));
	}

	if (rawResults.immersive_products) {
		results.immersive_products = rawResults.immersive_products.map((product) => ({
			title: product.title,
			source: product.source,
			rating: product.rating,
			reviews: product.reviews,
			price: product.price,
			extracted_price: product.extracted_price,
			original_price: product.original_price,
			extracted_original_price: product.extracted_original_price,
			extensions: product.extensions || [],
		}));
	}

	if (rawResults.inline_images) {
		results.inline_images = rawResults.inline_images.map((image) => ({
			source: image.source,
			thumbnail: image.thumbnail,
			original: image.original,
			title: image.title,
		}));
	}

	if (rawResults.inline_videos) {
		results.inline_videos = rawResults.inline_videos.map((video) => ({
			title: video.title,
			link: video.link,
			thumbnail: video.thumbnail,
			channel: video.channel,
			duration: video.duration,
			platform: video.platform,
			date: video.date,
		}));
	}

	if (rawResults.organic_results) {
		results.organic_results = rawResults.organic_results.map((result) => ({
			title: result.title,
			link: result.link,
			displayed_link: result.displayed_link,
			snippet: result.snippet,
			source: result.source,
			date: result.date,
			author: result.author,
			rich_snippet: result.rich_snippet?.top?.detected_extensions
				? {
						top: {
							detected_extensions: {
								price: result.rich_snippet.top.detected_extensions.price,
								rating: result.rich_snippet.top.detected_extensions.rating,
								reviews: result.rich_snippet.top.detected_extensions.reviews,
								address: result.rich_snippet.top.detected_extensions.address,
							},
						},
					}
				: undefined,
		}));
	}

	if (rawResults.shopping_results) {
		results.shopping_results = rawResults.shopping_results.map((product) => ({
			position: product.position,
			title: product.title,
			link: product.link,
			source: product.source,
			price: product.price,
			extracted_price: product.extracted_price,
			old_price: product.old_price,
			extracted_old_price: product.extracted_old_price,
			rating: product.rating,
			reviews: product.reviews,
			reviews_original: product.reviews_original,
			shipping: product.shipping,
			extensions: product.extensions || [],
			thumbnail: product.thumbnail,
		}));
	}

	if (rawResults.latest_posts) {
		results.latest_posts = rawResults.latest_posts.map((post) => ({
			title: post.title,
			link: post.link,
			source: post.source,
			channel: post.channel,
			date: post.date,
			views: post.views,
			extracted_views: post.extracted_views,
		}));
	}

	if (rawResults.local_news) {
		results.local_news = rawResults.local_news.map((news) => ({
			source: news.source,
			title: news.title,
			link: news.link,
			date: news.date,
			thumbnail: news.thumbnail,
		}));
	}

	if (rawResults.local_results) {
		results.local_results = {
			more_locations_link: rawResults.local_results.more_locations_link,
			places: rawResults.local_results.places.map((place) => ({
				title: place.title,
				rating: place.rating,
				reviews: place.reviews,
				price: place.price,
				type: place.type,
				address: place.address,
				hours: place.hours,
				thumbnail: place.thumbnail,
				service_options: place.service_options,
				gps_coordinates: place.gps_coordinates,
				place_id_search: place.place_id_search,
			})),
		};
	}

	if (rawResults.news_results) {
		results.news_results = rawResults.news_results.map((result) => ({
			position: result.position,
			title: result.title,
			link: result.link,
			date: result.date,
			source: result.source,
			snippet: result.snippet,
			thumbnail: result.thumbnail,
		}));
	}

	if (rawResults.scholarly_articles) {
		results.scholarly_articles = {
			title: rawResults.scholarly_articles.title,
			link: rawResults.scholarly_articles.link,
			articles: rawResults.scholarly_articles.articles.map((article) => ({
				title: article.title,
				link: article.link,
				author: article.author,
				cited_by: article.cited_by,
				highlighted_words: article.highlighted_words,
			})),
		};
	}

	return results;
};
