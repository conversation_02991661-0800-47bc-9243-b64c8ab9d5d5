import { ModelProvider } from '@/models/conversational/conversational-models';
import { ChatModelParams } from '@/models/conversational/conversational-model-constraints';
import { ImageModelProvider } from '@/models/image/image-generation-models';
import { ImageGenerationParams } from '@/models/image/image-generation-constraints';
import { VideoModelProvider } from '@/models/video/video-generation-models';
import { VideoGenerationParams } from '@/models/video/video-generation-constraints';

export type ChatType = 'chat' | 'image' | 'video';
export type TaskStatus = 'PENDING' | 'COMPLETED' | 'FAILED';

export interface ChatHistory {
	chatId: string;
	chatType: ChatType;
	title: string;
	userId: string;
	historyContext: string;
	createdAt: string;
	lastModifiedAt: string;
}

export interface Model {
	modelName: string;
	modelProvider: ModelProvider | ImageModelProvider | VideoModelProvider;
}

export interface ChatMessage {
	chatId: string;
	id: string;
	prompt: Prompt;
	response: Response[] | null;
	createdAt: string;
}

export interface Prompt {
	text: string;
	attachments?: Attachment[];
	webSearchEnabled?: boolean;
	params?: ChatModelParams | ImageGenerationParams | VideoGenerationParams;
}

export interface Response {
	text?: string;
	reasoning?: string;
	images?: Attachment[];
	videos?: Attachment[];
	requestId?: string;
	tokensUsed?: number;
	toolResults?: ToolResult[];
	createdAt: string;
	modelUsed: Model;
	isActive?: boolean;
}

export interface ToolResult {
	toolName: string;
	toolCallId: string;
	args: any;
	result: any;
}

export interface Attachment {
	type?: string;
	name?: string;
	url: string;
	formData?: FormData; //used to send the file to the server
}

export interface TaskData extends VideoGenerationParams {
	model: string;
}

export interface Task {
	requestId: string;
	messageId: string;
	chatId: string;
	userId: string;
	status: TaskStatus;
	data: TaskData | null;
	payload: any;
	createdAt: string;
	updatedAt?: string;
}

export interface PollingTaskResponse {
	taskStatus: TaskStatus;
	videoGenerationResponse?: Response[] | null;
	videoGenerationResponseError?: string;
}
