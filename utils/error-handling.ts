import { ImageModelProvider } from '@/models/image/image-generation-models';
import { VideoModelProvider } from '@/models/video/video-generation-models';

interface FalAIError {
	loc: string[];
	msg: string;
	type: string;
	url: string;
	ctx?: Record<string, any>;
	input?: any;
}

interface FalAIErrorResponse {
	detail: FalAIError[];
}

export interface ParsedError {
	message: string;
	errorDetails: string;
}

type MediaType = 'image' | 'video';
type ErrorHandler = (error: any) => ParsedError;

// A mapping of error types to user-friendly messages.
const FalErrorMessageMap: Record<string, (error: FalAIError) => ParsedError> = {
	content_policy_violation: (err) => ({
		message: 'Your prompt violates our content policy. Please try a different prompt.',
		errorDetails: err.msg,
	}),
	face_detection_error: (err) => ({
		message:
			'Unable to detect a face in the image. Please use an image with a clear, visible face.',
		errorDetails: err.msg,
	}),
	image_load_error: (err) => ({
		message: 'Failed to load the provided image. Please check the image and try again.',
		errorDetails: err.msg,
	}),
	unsupported_image_format: (err) => {
		const supportedFormats = err.ctx?.supported_formats?.join(', ') || 'JPG, PNG, WebP';
		return {
			message: `Unsupported image format. Please use one of: ${supportedFormats}.`,
			errorDetails: err.msg,
		};
	},
	unsupported_video_format: (err) => {
		const supportedFormats = err.ctx?.supported_formats?.join(', ') || 'MP4, MOV, WebM';
		return {
			message: `Unsupported video format. Please use one of: ${supportedFormats}.`,
			errorDetails: err.msg,
		};
	},
	image_too_large: (err) => {
		const maxDims = err.ctx ? `${err.ctx.max_width}x${err.ctx.max_height}` : 'the maximum';
		return {
			message: `Image dimensions are too large. Maximum allowed is ${maxDims}.`,
			errorDetails: err.msg,
		};
	},
	image_too_small: (err) => {
		const minDims = err.ctx ? `${err.ctx.min_width}x${err.ctx.min_height}` : 'the minimum';
		return {
			message: `Image dimensions are too small. Minimum required is ${minDims}.`,
			errorDetails: err.msg,
		};
	},
	generation_timeout: (err) => ({
		message: 'The request timed out. Please try again with a simpler prompt.',
		errorDetails: err.msg,
	}),
	downstream_service_error: (err) => ({
		message: 'The AI service is temporarily unavailable. Please try again in a few moments.',
		errorDetails: err.msg,
	}),
	internal_server_error: (err) => ({
		message: 'An internal server error occurred. Please try again later.',
		errorDetails: err.msg,
	}),
};

export function parseFalAIError(error: any): ParsedError {
	// Check for the structured 422 error from fal.ai first.
	if (error?.statusCode === 422 && error?.responseBody) {
		try {
			const errorResponse: FalAIErrorResponse = JSON.parse(error.responseBody);
			const firstError = errorResponse.detail?.[0];

			if (firstError) {
				// Use the map to get a specific message, or fall back to the message from the API.
				const handler = FalErrorMessageMap[firstError.type];
				if (handler) {
					return handler(firstError);
				}
				return {
					message: firstError.msg || 'An unknown validation error occurred.',
					errorDetails: firstError.msg,
				};
			}
		} catch (parseError) {
			console.error('Failed to parse fal.ai error response:', parseError);
			// Fall through to the generic error handling below.
		}
	}

	// Generic error handling based on message content.
	const errorMessage = error?.message || 'Unknown error';
	if (/content policy|safety/i.test(errorMessage)) {
		return {
			message: 'Your prompt violates our content policy. Please try a different prompt.',
			errorDetails: errorMessage,
		};
	}

	// Default fallback error.
	return {
		message: 'An unexpected error occurred. Please try again.',
		errorDetails: errorMessage,
	};
}

// Error handlers for different providers
const handleFalAIError: ErrorHandler = (error: any) => {
	return parseFalAIError(error);
};

// TODO: Implement Vertex AI error handling
const handleVertexAIError: ErrorHandler = (error: any) => {
	// Can be extended with Vertex AI specific error handling
	return handleDefaultError(error);
};

const handleDefaultError: ErrorHandler = (error: any) => {
	const errorMessage = error?.message || 'Unknown error';
	if (/content policy|safety/i.test(errorMessage)) {
		return {
			message: 'Your prompt violates our content policy. Please try a different prompt.',
			errorDetails: errorMessage,
		};
	}
	return {
		message: 'An unexpected error occurred. Please try again.',
		errorDetails: errorMessage,
	};
};

// Provider to error handler mapping for images
const imageProviderErrorHandlers: Record<ImageModelProvider, ErrorHandler> = {
	[ImageModelProvider.Ideogram]: handleFalAIError,
	[ImageModelProvider.StabilityAI]: handleFalAIError,
	[ImageModelProvider.BlackForestLabs]: handleFalAIError,
	[ImageModelProvider.OpenAI]: handleDefaultError,
	[ImageModelProvider.Google]: handleVertexAIError,
	[ImageModelProvider.XAI]: handleDefaultError,
};

// Provider to error handler mapping for videos
const videoProviderErrorHandlers: Record<VideoModelProvider, ErrorHandler> = {
	[VideoModelProvider.KlingAI]: handleFalAIError,
	[VideoModelProvider.Luma]: handleFalAIError,
	[VideoModelProvider.MiniMax]: handleFalAIError,
	[VideoModelProvider.StabilityAI]: handleFalAIError,
	[VideoModelProvider.Google]: handleFalAIError, //handleVertexAIError,
};

export function createImageErrorResponse(
	error: any,
	provider?: ImageModelProvider,
	model?: string
): string {
	return _createErrorResponse(error, provider, model, 'image');
}

export function createVideoErrorResponse(
	error: any,
	provider?: VideoModelProvider,
	model?: string
): string {
	return _createErrorResponse(error, provider, model, 'video');
}

function _createErrorResponse(
	error: any,
	provider?: ImageModelProvider | VideoModelProvider,
	model?: string,
	type: MediaType = 'image'
): string {
	let parsedError: ParsedError;

	if (type === 'video') {
		const errorHandler = provider
			? videoProviderErrorHandlers[provider as VideoModelProvider]
			: handleDefaultError;
		parsedError = errorHandler(error);
	} else {
		const errorHandler = provider
			? imageProviderErrorHandlers[provider as ImageModelProvider]
			: handleDefaultError;
		parsedError = errorHandler(error);
	}

	return JSON.stringify({
		...parsedError,
		...(model && { model }),
		...(provider && { provider }),
		...(type && { type }),
	});
}
