import crypto from 'crypto';
import sodium from 'libsodium-wrappers';

const JWKS_URL = 'https://rest.alpha.fal.ai/.well-known/jwks.json';
const JWKS_CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

interface JWK {
	x: string;
	[key: string]: any;
}

interface JWKSResponse {
	keys: JWK[];
}

let jwksCache: JWK[] | null = null;
let jwksCacheTime = 0;

async function fetchJwks(): Promise<JWK[]> {
	const currentTime = Date.now();
	if (!jwksCache || currentTime - jwksCacheTime > JWKS_CACHE_DURATION) {
		const response = await fetch(JWKS_URL);
		if (!response.ok) {
			throw new Error(`JWKS fetch failed: ${response.status}`);
		}
		const data = (await response.json()) as JWKSResponse;
		jwksCache = data.keys || [];
		jwksCacheTime = currentTime;
	}
	return jwksCache || [];
}

export async function verifyFalWebhookSignature(
	requestId: string,
	userId: string,
	timestamp: string,
	signatureHex: string,
	body: Buffer
): Promise<boolean> {
	await sodium.ready;

	// Validate timestamp (within ±5 minutes)
	try {
		const timestampInt = parseInt(timestamp, 10);
		const currentTime = Math.floor(Date.now() / 1000);
		if (Math.abs(currentTime - timestampInt) > 300) {
			console.error('Timestamp is too old or in the future.');
			return false;
		}
	} catch (e) {
		console.error('Invalid timestamp format:', e);
		return false;
	}

	// Construct the message to verify
	try {
		const messageParts = [
			requestId,
			userId,
			timestamp,
			crypto.createHash('sha256').update(body).digest('hex'),
		];

		if (messageParts.some((part) => part == null)) {
			console.error('Missing required header value.');
			return false;
		}

		const messageToVerify = messageParts.join('\n');
		const messageBytes = Buffer.from(messageToVerify, 'utf-8');

		// Decode signature
		let signatureBytes;
		try {
			signatureBytes = Buffer.from(signatureHex, 'hex');
		} catch (e) {
			console.error('Invalid signature format (not hexadecimal).');
			return false;
		}

		// Fetch public keys
		const publicKeysInfo = await fetchJwks();
		if (!publicKeysInfo.length) {
			console.error('No public keys found in JWKS.');
			return false;
		}

		// Verify signature with each public key
		for (const keyInfo of publicKeysInfo) {
			try {
				const publicKeyB64Url = keyInfo.x;
				if (typeof publicKeyB64Url !== 'string') {
					continue;
				}

				const publicKeyBytes = Buffer.from(publicKeyB64Url, 'base64url');
				const isValid = sodium.crypto_sign_verify_detached(
					signatureBytes,
					messageBytes,
					publicKeyBytes
				);

				if (isValid) {
					return true;
				}
			} catch (e) {
				console.error('Verification failed with a key:', e);
				continue;
			}
		}

		console.error('Signature verification failed with all keys.');
		return false;
	} catch (e) {
		console.error('Error constructing message:', e);
		return false;
	}
}
