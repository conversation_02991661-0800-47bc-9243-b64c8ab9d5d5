/**
 * TODO:
 * Remove unnecessary helpers after schema changes in database.
 */

import { Message } from 'ai';
import { ChatMessage, Model, Response, Attachment } from '@/types/chat';
import { ChatModelParams } from '@/models/conversational/conversational-model-constraints';

export const createResponseId = (promptId: string, index: number): string => {
	return `${promptId}:response:${index}`;
};

export const convertPromptAttachments = (attachments: Attachment[] = []) =>
	attachments.map((attachment) => ({
		url: attachment.url,
		name: attachment.name,
		contentType: attachment.type,
	}));

export const convertResponseToolResults = (response: Response) =>
	response.toolResults?.map((result) => ({
		state: 'result' as const,
		toolName: result.toolName,
		toolCallId: result.toolCallId,
		args: result.args,
		result: result.result,
	}));

export const extractImageAttachments = (message: Message) =>
	message.experimental_attachments
		?.filter((att) => att.contentType?.startsWith('image/'))
		.map(({ name, contentType, url }) => ({
			name,
			type: contentType,
			url,
		}));

export const extractToolResults = (message: Message) =>
	message.toolInvocations
		?.filter(
			(
				inv
			): inv is {
				state: 'result';
				toolName: string;
				toolCallId: string;
				args: any;
				result: any;
			} => inv.state === 'result'
		)
		.map(({ toolName, toolCallId, args, result }) => ({
			toolName,
			toolCallId,
			args,
			result,
		}));

export const createAssistantMessage = (
	msg: ChatMessage,
	response: Response,
	index: number
): Message => ({
	id: createResponseId(msg.id, index),
	role: 'assistant',
	content: response.text || '',
	reasoning: response.reasoning,
	createdAt: new Date(response.createdAt),
	data: response.modelUsed
		? {
				modelName: response.modelUsed.modelName,
				modelProvider: response.modelUsed.modelProvider,
			}
		: undefined,
	experimental_attachments: response.images?.length
		? response.images.map(({ url, type, name }) => ({
				name,
				contentType: type,
				url,
			}))
		: undefined,
	toolInvocations: convertResponseToolResults(response),
});

export const convertToSDKMessages = (chatMessages: ChatMessage[]): Message[] => {
	return chatMessages.flatMap((msg) => {
		const userMessage = {
			id: msg.id,
			role: 'user' as const,
			content: msg.prompt.text,
			experimental_attachments:
				msg.prompt.attachments && msg.prompt.attachments.length > 0
					? convertPromptAttachments(msg.prompt.attachments)
					: undefined,
		};

		if (!msg.response?.length) {
			return [userMessage];
		}

		const lastResponse = msg.response[msg.response.length - 1];
		return [userMessage, createAssistantMessage(msg, lastResponse, msg.response.length - 1)];
	});
};

export const convertFromSDKMessage = (
	prompt: Message,
	response: Message,
	reasoning: string | undefined,
	isWebSearchEnabled: boolean,
	modelParams: ChatModelParams,
	chatId: string,
	model: Model,
	usage: number
): ChatMessage => ({
	id: prompt.id,
	chatId,
	createdAt: response.createdAt?.toISOString() || new Date().toISOString(),
	prompt: {
		text: prompt.content,
		attachments: (prompt.experimental_attachments || []).map((att) => ({
			type: att.contentType,
			name: att.name,
			url: att.url,
		})),
		webSearchEnabled: isWebSearchEnabled,
		params: modelParams,
	},
	response: [
		{
			text: response.content,
			reasoning: response.reasoning || reasoning,
			tokensUsed: usage,
			images: extractImageAttachments(response),
			toolResults: extractToolResults(response),
			createdAt: response.createdAt?.toISOString() || new Date().toISOString(),
			modelUsed: model,
		},
	],
});

export const convertFromSDKResponse = (
	message: Message,
	reasoning: string | undefined,
	model: Model,
	usage: number
): Response => ({
	text: message.content,
	reasoning: message.reasoning || reasoning,
	tokensUsed: usage,
	images: extractImageAttachments(message),
	toolResults: extractToolResults(message),
	createdAt: message.createdAt?.toISOString() || new Date().toISOString(),
	modelUsed: model,
});

// Token limit for conversation context (128K tokens)
export const MAX_CONVERSATION_TOKENS = 128000;

/**
 * Calculate total tokens used in a conversation
 * @param chatMessages - Array of chat messages
 * @param earlyExit - If true, stops counting when MAX_CONVERSATION_TOKENS is exceeded
 */
export function calculateTotalTokensUsed(chatMessages: ChatMessage[], earlyExit = false): number {
	let totalTokens = 0;
	for (const message of chatMessages) {
		if (message.response) {
			for (const response of message.response) {
				if (response.tokensUsed) {
					totalTokens += response.tokensUsed;
					if (earlyExit && totalTokens >= MAX_CONVERSATION_TOKENS) {
						return totalTokens;
					}
				}
			}
		}
	}
	return totalTokens;
}
