import nodemailer from 'nodemailer';

export interface EmailData {
	to: string;
	subject: string;
	html: string;
	personalizations?: Record<string, string>;
}

// Create reusable transporter object using SMTP transport
export const transporter = nodemailer.createTransport({
	host: process.env.SMTP_HOST,
	port: Number(process.env.SMTP_PORT) || 587,
	secure: process.env.SMTP_SECURE === 'true',
	auth: {
		user: process.env.SMTP_USER,
		pass: process.env.SMTP_PASS,
	},
});

// Send email with error handling
export async function sendEmail(data: EmailData): Promise<void> {
	try {
		await transporter.sendMail({
			from: process.env.SMTP_FROM,
			to: data.to,
			subject: data.subject,
			html: data.html,
		});
	} catch (error) {
		console.error('Error sending email:', error);
		throw error;
	}
}
