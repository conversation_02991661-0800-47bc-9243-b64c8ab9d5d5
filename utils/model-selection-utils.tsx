import { JSX } from 'react';
import {
	<PERSON><PERSON><PERSON>,
	<PERSON>,
	DeepSeek,
	Flux,
	Gemini,
	Google,
	Grok,
	Ideogram,
	Meta,
	Mistral,
	OpenAI,
	Stability,
	XAI,
	Kling,
	Luma,
	Minimax,
	DeepMind,
} from '@lobehub/icons';
import {
	IconYinYangFilled,
	IconBulb,
	IconTelescope,
	IconCode,
	IconBrain,
} from '@tabler/icons-react';
import {
	ModelDisplayName,
	modelDisplayNameToProviderMap,
	modelDisplayNameToTechnicalNameMap,
	ModelProvider,
	Modes,
} from '@/models/conversational/conversational-models';
import {
	getImageModelDisplayName,
	ImageModelDisplayName,
	imageModelDisplayNameToProviderMap,
	ImageModelProvider,
} from '@/models/image/image-generation-models';
import {
	VideoModelDisplayName,
	videoModelDisplayNameToEditEndpointMap,
	videoModelDisplayNameToProviderMap,
	videoModelDisplayNameToTechnicalNameMap,
	VideoModelProvider,
} from '@/models/video/video-generation-models';
import { ChatType } from '@/types/chat';

export function getTechnicalNameMetadata(
	type: ChatType,
	technicalName?: string
): {
	modelDisplayName?: ModelDisplayName | ImageModelDisplayName | VideoModelDisplayName;
	modelProvider?: ModelProvider | ImageModelProvider | VideoModelProvider;
} {
	if (!technicalName) {
		return { modelDisplayName: undefined, modelProvider: undefined };
	}

	if (type === 'chat') {
		// Find the display name by matching technical name for chat models
		const modelDisplayName = Object.entries(modelDisplayNameToTechnicalNameMap).find(
			([_, techName]) => techName === technicalName
		)?.[0] as ModelDisplayName | undefined;

		// Get the provider if we found a display name
		const modelProvider = modelDisplayName
			? modelDisplayNameToProviderMap[modelDisplayName]
			: undefined;

		return { modelDisplayName, modelProvider };
	} else if (type === 'image') {
		// Find the display name by matching technical name for image models
		const modelDisplayName = getImageModelDisplayName(technicalName);

		// Get the provider if we found a display name
		const modelProvider = modelDisplayName
			? imageModelDisplayNameToProviderMap[modelDisplayName]
			: undefined;

		return { modelDisplayName, modelProvider };
	} else {
		// Find the display name by matching technical name for video models
		let modelDisplayName = Object.entries(videoModelDisplayNameToTechnicalNameMap).find(
			([_, techName]) => techName === technicalName
		)?.[0] as VideoModelDisplayName | undefined;

		if (!modelDisplayName) {
			// Find the display name by matching edit endpoint for video models
			modelDisplayName = Object.entries(videoModelDisplayNameToEditEndpointMap).find(
				([_, techName]) => techName === technicalName
			)?.[0] as VideoModelDisplayName | undefined;
		}

		// Get the provider if we found a display name
		const modelProvider = modelDisplayName
			? videoModelDisplayNameToProviderMap[modelDisplayName]
			: undefined;

		return { modelDisplayName, modelProvider };
	}
}

export const getProviderLogo = (provider: string, type: ChatType): JSX.Element => {
	// Image Models
	if (type === 'image') {
		switch (provider) {
			case ImageModelProvider.BlackForestLabs:
				return <Flux.Combine />;
			case ImageModelProvider.Google:
				return <Google.BrandColor size={20} />;
			case ImageModelProvider.Ideogram:
				return <Ideogram.Combine />;
			case ImageModelProvider.OpenAI:
				return <OpenAI.Combine />;
			case ImageModelProvider.StabilityAI:
				return <Stability.BrandColor />;
			case ImageModelProvider.XAI:
				return <XAI.Combine />;
			default:
				return <OpenAI.Combine />;
		}
	}

	// Video Models
	if (type === 'video') {
		switch (provider) {
			case VideoModelProvider.Luma:
				return <Luma.Combine />;
			case VideoModelProvider.MiniMax:
				return <Minimax.Combine />;
			case VideoModelProvider.StabilityAI:
				return <Stability.BrandColor />;
			case VideoModelProvider.KlingAI:
				return <Kling.Combine />;
			case VideoModelProvider.Google:
				return <DeepMind.Combine />;
			default:
				return <Kling.Combine />;
		}
	}

	// Chat Models
	switch (provider) {
		case ModelProvider.Anthropic:
			return <Anthropic.Text />;
		case ModelProvider.Deepseek:
			return <DeepSeek.Combine type="color" />;
		case ModelProvider.Google:
			return <Google.BrandColor size={20} />;
		case ModelProvider.Meta:
			return <Meta.BrandColor />;
		case ModelProvider.Mistral:
			return <Mistral.Combine type="color" />;
		case ModelProvider.OpenAI:
			return <OpenAI.Combine />;
		case ModelProvider.XAI:
			return <XAI.Combine />;
		default:
			return <OpenAI.Combine />;
	}
};

export const getProviderIcon = (
	provider: string,
	type: ChatType,
	size: number = 36
): JSX.Element => {
	// Image Models
	if (type === 'image') {
		switch (provider) {
			case ImageModelProvider.BlackForestLabs:
				return <Flux size={size} />;
			case ImageModelProvider.Google:
				return <Gemini.Color size={size} />;
			case ImageModelProvider.Ideogram:
				return <Ideogram size={size} />;
			case ImageModelProvider.OpenAI:
				return <OpenAI size={size} />;
			case ImageModelProvider.StabilityAI:
				return <Stability.Color size={size} />;
			case ImageModelProvider.XAI:
				return <XAI size={size} />;
			default:
				return <OpenAI size={size} />;
		}
	}

	// Video Models
	if (type === 'video') {
		switch (provider) {
			case VideoModelProvider.Luma:
				return <Luma size={size} />;
			case VideoModelProvider.MiniMax:
				return <Minimax size={size} />;
			case VideoModelProvider.StabilityAI:
				return <Stability.Color size={size} />;
			case VideoModelProvider.KlingAI:
				return <Kling size={size} />;
			case VideoModelProvider.Google:
				return <DeepMind.Color size={size} />;
			default:
				return <Kling size={size} />;
		}
	}

	// Chat Models
	switch (provider) {
		case ModelProvider.Anthropic:
			return <Claude.Color size={size} />;
		case ModelProvider.Deepseek:
			return <DeepSeek.Color size={size} />;
		case ModelProvider.Google:
			return <Gemini.Color size={size} />;
		case ModelProvider.Meta:
			return <Meta.Color size={size} />;
		case ModelProvider.Mistral:
			return <Mistral.Color size={size} />;
		case ModelProvider.OpenAI:
			return <OpenAI size={size} />;
		case ModelProvider.XAI:
			return <Grok size={size} />;
		default:
			return <OpenAI size={size} />;
	}
};

export const getCategoryIcon = (category: Modes, isSelected: boolean = false) => {
	const color = isSelected ? '#9455D3' : undefined;

	switch (category) {
		case Modes.GeneralPurpose:
			return (
				<IconYinYangFilled
					color={color}
					size={20}
				/>
			);
		case Modes.CreativeWriting:
			return (
				<IconBulb
					color={color}
					size={20}
				/>
			);
		case Modes.Research:
			return (
				<IconTelescope
					color={color}
					size={20}
				/>
			);
		case Modes.SoftwareEngineering:
			return (
				<IconCode
					color={color}
					size={20}
				/>
			);
		case Modes.Reasoning:
			return (
				<IconBrain
					color={color}
					size={20}
				/>
			);
	}
};
