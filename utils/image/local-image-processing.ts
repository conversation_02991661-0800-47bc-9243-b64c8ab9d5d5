/**
 * Helper for testing the Image Generation Experience.
 * Use the following code in route.ts in 'api/image' to mock the generated image by the AI.
 * const filePath = "/Users/<USER>/Downloads/pexels-tatyana-doloman-728740365-30538754.jpg"; // Replace with the actual file path
 * generatedImage = processLocalImage(filePath);
 */

import * as fs from 'fs';
import { Experimental_GeneratedImage as GeneratedImage } from 'ai';

/**
 * Converts a URL to a GeneratedImage object by downloading and processing the image.
 * @param url - The URL of the image to process.
 * @returns A Promise that resolves to a GeneratedImage object.
 */
export async function urlToGeneratedImage(url: string): Promise<GeneratedImage> {
	const response = await fetch(url);
	const arrayBuffer = await response.arrayBuffer();
	const uint8Array = new Uint8Array(arrayBuffer);
	const base64 = Buffer.from(arrayBuffer).toString('base64');

	return {
		base64,
		uint8Array,
		mimeType: 'image/png', // Ideogram mimeType is 'image/png'
	};
}

/**
 * Reads an image file and converts it to Base64.
 * @param filePath - The path to the image file.
 * @returns A Base64 encoded string.
 */
function imageFileToBase64(filePath: string): string {
	const imageBuffer = fs.readFileSync(filePath);
	return imageBuffer.toString('base64');
}

/**
 * Converts a Base64 string to a Uint8Array.
 * @param base64 - The Base64 encoded string.
 * @returns A Uint8Array representation of the image.
 */
export function base64ToUint8Array(base64: string): Uint8Array {
	const binaryString = Buffer.from(base64, 'base64');
	return new Uint8Array(binaryString);
}

/**
 * Converts a Uint8Array back to a Base64 string.
 * @param uint8Array - The Uint8Array representation of the image.
 * @returns A Base64 encoded string.
 */
function uint8ArrayToBase64(uint8Array: Uint8Array): string {
	return Buffer.from(uint8Array).toString('base64');
}

/**
 * Processes a local image file and returns a GeneratedImage object.
 * @param filePath - The path to the image file.
 * @returns A GeneratedImage object.
 */
export function processLocalImage(filePath: string): GeneratedImage {
	const base64 = imageFileToBase64(filePath);
	const uint8Array = base64ToUint8Array(base64);

	return {
		base64,
		uint8Array,
		mimeType: 'image/png',
	};
}
