type LogLevel = 'info' | 'error' | 'warn' | 'debug';

interface LogEntry {
	timestamp: string;
	level: LogLevel;
	message: string;
	data?: any;
	error?: Error;
}

export const logger = {
	info: (message: string, data?: any) => {
		log('info', message, data);
	},

	error: (message: string, error?: Error, data?: any) => {
		log('error', message, data, error);
	},

	warn: (message: string, data?: any) => {
		log('warn', message, data);
	},

	debug: (message: string, data?: any) => {
		if (process.env.NODE_ENV === 'development') {
			log('debug', message, data);
		}
	},
};

function log(level: LogLevel, message: string, data?: any, error?: Error) {
	const entry: LogEntry = {
		timestamp: new Date().toISOString(),
		level,
		message,
		...(data && { data }),
		...(error && {
			error: {
				message: error.message,
				stack: error.stack,
				name: error.name,
			},
		}),
	};

	// In development, log to console
	if (process.env.NODE_ENV === 'development') {
		const color = {
			info: '\x1b[36m', // Cyan
			error: '\x1b[31m', // Red
			warn: '\x1b[33m', // Yellow
			debug: '\x1b[35m', // Magenta
		}[level];

		console.log(`${color}[${level.toUpperCase()}]\x1b[0m ${message}`);
		if (data) console.log(data);
		if (error) console.error(error);
	}

	// In production, you might want to send to a logging service
	// For now, just use console.log
	if (process.env.NODE_ENV === 'production') {
		console.log(JSON.stringify(entry));
	}
}

// Payment specific logging
export const paymentLogger = {
	initializePayment: (userId: string, merchantTransactionId: string) => {
		logger.info('Payment initialization', { userId, merchantTransactionId });
	},

	paymentError: (error: Error, context: { userId?: string; merchantTransactionId?: string }) => {
		logger.error('Payment error', error, context);
	},

	paymentSuccess: (data: {
		userId: string;
		merchantTransactionId: string;
		subscriptionId: string;
	}) => {
		logger.info('Payment successful', data);
	},

	paymentVerificationStart: (merchantTransactionId: string) => {
		logger.info('Starting payment verification', { merchantTransactionId });
	},

	paymentVerificationComplete: (merchantTransactionId: string, success: boolean) => {
		logger.info('Payment verification complete', {
			merchantTransactionId,
			success,
		});
	},
};
