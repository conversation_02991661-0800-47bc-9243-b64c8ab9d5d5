import { NextResponse, type NextRequest } from 'next/server';
import { hasBetaAccess } from '@/config/beta-access';
import { createServerClient } from '@supabase/ssr';

export async function updateSession(request: NextRequest) {
	let supabaseResponse = NextResponse.next({
		request,
	});

	const supabase = createServerClient(
		process.env.NEXT_PUBLIC_SUPABASE_URL!,
		process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
		{
			cookies: {
				getAll() {
					return request.cookies.getAll();
				},
				setAll(cookiesToSet) {
					cookiesToSet.forEach(({ name, value, options }) =>
						request.cookies.set(name, value)
					);
					supabaseResponse = NextResponse.next({
						request,
					});
					cookiesToSet.forEach(({ name, value, options }) =>
						supabaseResponse.cookies.set(name, value, options)
					);
				},
			},
		}
	);
	const {
		data: { user },
	} = await supabase.auth.getUser();

	const pathname = request.nextUrl.pathname;

	// Add subscription routes to public routes
	const publicRoutes = ['/terms', '/privacy', '/beta-access', '/refund'];
	const authRoutes = ['/login', '/auth'];
	const subscriptionRoutes = ['/subscription'];

	const isPublicRoute =
		pathname === '/' || publicRoutes.some((route) => pathname.startsWith(route));
	const isAuthRoute = authRoutes.some((route) => pathname.startsWith(route));
	const isSubscriptionRoute = subscriptionRoutes.some((route) => pathname.startsWith(route));

	// TODO: Use a list of protected routes.
	const isProtectedRoute = !isPublicRoute && !isAuthRoute && !isSubscriptionRoute;

	// If no user, redirect to login
	if (!user && isProtectedRoute) {
		const url = new URL('/login', request.url);
		url.searchParams.set('next', pathname);

		const shareId = request.nextUrl.searchParams.get('shareId');
		if (!!shareId) {
			url.searchParams.set('shareId', shareId);
		}

		return NextResponse.redirect(url);
	}

	// If logged in user tries to access auth routes, redirect to dashboard
	if (user && isAuthRoute) {
		return NextResponse.redirect(new URL('/chat', request.url));
	}

	// If user is logged in and accessing protected routes
	if (user && isProtectedRoute) {
		// First check beta access
		if (!hasBetaAccess(user.email || '')) {
			await supabase.auth.signOut();
			return NextResponse.redirect(new URL('/beta-access', request.url));
		}
	}

	return supabaseResponse;
}
