const withPWA = require('next-pwa')({
	dest: 'public',
	register: true,
	skipWaiting: true,
	disable: process.env.NODE_ENV === 'development',
	buildExcludes: [/middleware-manifest\.json$/],
});

/** @type {import('next').NextConfig} */
const nextConfig = {
	env: {
		GOOGLE_FORM_URL: process.env.GOOGLE_FORM_URL,
		FIRECRAWL_API_KEY: process.env.FIRECRAWL_API_KEY,
		NEXT_PUBLIC_SERP_API_KEY: process.env.NEXT_PUBLIC_SERP_API_KEY,
		NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
		NEXT_PUBLIC_IMAGE_API_URL: process.env.NEXT_PUBLIC_IMAGE_API_URL,
	},
	images: {
		remotePatterns: [
			// Import profile pictures from google account
			{
				protocol: 'https',
				hostname: 'lh3.googleusercontent.com',
				pathname: '**',
			},
			// Import profile pictures from github account
			{
				protocol: 'https',
				hostname: 'avatars.githubusercontent.com',
				pathname: '**',
			},
			// For images present in web search results
			{
				protocol: 'https',
				hostname: 'serpapi.com',
				pathname: '**',
			},
			{
				protocol: 'https',
				hostname: '*.gstatic.com',
				pathname: '**',
			},
			{
				protocol: 'https',
				hostname: 'pasiolwuajbjqtdrnueq.supabase.co',
				port: '',
				pathname: '/storage/v1/object/public/ai-generated-images/**',
				search: '',
			},
		],
	},
	turbopack: {
		resolveAlias: {
			'@': './',
		},
		resolveExtensions: ['.tsx', '.ts', '.jsx', '.js', '.mjs', '.json'],
	},
};

module.exports = withPWA(nextConfig);
