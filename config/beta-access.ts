/**
 * Configuration for beta access control
 * Contains whitelisted email addresses and helper functions
 */

export const BETA_WHITELIST = [
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'utkar<PERSON>@xike.in',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
	'<EMAIL>',
];

/**
 * Checks if an email has beta access
 * @param email - The email to check
 * @returns boolean indicating if the email has beta access
 */
export const hasBetaAccess = (email: string): boolean => {
	if (!email) return false;
	return BETA_WHITELIST.includes(email.toLowerCase());
};

export const isZecoTeamMember = (email: string): boolean => email.endsWith('@zeco.ai');
