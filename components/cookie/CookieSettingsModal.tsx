'use client';

import { useState, useEffect } from 'react';
import { Button } from '@heroui/button';
import { Modal, ModalContent, ModalHeader, ModalBody, ModalFooter } from '@heroui/modal';
import { Switch } from '@heroui/switch';
import { CookieOptions, useCookieConsent } from '@/lib/hooks/useCookieConsent';

interface CookieSettingsModalProps {
	isOpen: boolean;
	onOpenChange: (isOpen: boolean) => void;
}

export const CookieSettingsModal = ({ isOpen, onOpenChange }: CookieSettingsModalProps) => {
	const { cookieOptions, saveCustomCookieSettings } = useCookieConsent();

	// Local state for cookie options in the modal
	const [localOptions, setLocalOptions] = useState<CookieOptions>({
		necessary: true,
		analytics: false,
		marketing: false,
	});

	// Update local options when cookieOptions changes
	useEffect(() => {
		setLocalOptions(cookieOptions);
	}, [cookieOptions]);

	// Handle option change
	const handleOptionChange = (option: keyof CookieOptions, value: boolean) => {
		setLocalOptions((prev) => ({
			...prev,
			[option]: value,
		}));
	};

	// Handle save settings
	const handleSaveSettings = () => {
		saveCustomCookieSettings(localOptions);
		onOpenChange(false);
	};

	return (
		<Modal
			isOpen={isOpen}
			onOpenChange={onOpenChange}
			classNames={{
				backdrop: 'backdrop-blur-md',
				base: 'bg-[#090909]',
				header: 'p-6 border-b border-default-50',
				body: 'flex flex-col p-6 text-sm text-white/60 gap-6',
				footer: 'flex justify-end pt-0 gap-4',
			}}
		>
			<ModalContent>
				{() => (
					<>
						<ModalHeader>
							<h3 className="text-xl font-semibold">Cookie Settings</h3>
						</ModalHeader>
						<ModalBody>
							<div className="space-y-6">
								<div className="flex justify-between items-center">
									<div>
										<h4 className="font-medium text-white">
											Necessary Cookies
										</h4>
										<p className="text-sm text-gray-400">
											These cookies are essential for the website to function
											properly.
										</p>
									</div>
									<Switch
										isSelected={localOptions.necessary}
										isDisabled={true} // Always required
										className="ml-4"
									/>
								</div>

								<div className="flex justify-between items-center">
									<div>
										<h4 className="font-medium text-white">
											Analytics Cookies
										</h4>
										<p className="text-sm text-gray-400">
											These cookies help us understand how visitors interact
											with our website.
										</p>
									</div>
									<Switch
										isSelected={localOptions.analytics}
										onValueChange={(value) =>
											handleOptionChange('analytics', value)
										}
										className="ml-4"
									/>
								</div>

								<div className="flex justify-between items-center">
									<div>
										<h4 className="font-medium text-white">
											Marketing Cookies
										</h4>
										<p className="text-sm text-gray-400">
											These cookies are used to track visitors across websites
											to display relevant advertisements.
										</p>
									</div>
									<Switch
										isSelected={localOptions.marketing}
										onValueChange={(value) =>
											handleOptionChange('marketing', value)
										}
										className="ml-4"
									/>
								</div>
							</div>
						</ModalBody>
						<ModalFooter>
							<Button
								variant="flat"
								className="bg-transparent border border-gray-700 text-white text-xs py-1.5"
								onPress={() => onOpenChange(false)}
							>
								Cancel
							</Button>
							<Button
								className="flex items-center justify-center font-semibold rounded-md text-xs py-1.5 cursor-pointer bg-white border border-white hover:border-none text-black hover:bg-gradient-to-r from-fuchsia-500 via-purple-600 to-violet-700 hover:text-white transition-all duration-300"
								onPress={handleSaveSettings}
							>
								Save Settings
							</Button>
						</ModalFooter>
					</>
				)}
			</ModalContent>
		</Modal>
	);
};

export default CookieSettingsModal;
