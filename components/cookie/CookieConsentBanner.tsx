'use client';

import { useState, useEffect } from 'react';
import { Button } from '@heroui/button';
import { <PERSON>dal, <PERSON>dalContent, <PERSON>dalHeader, <PERSON><PERSON><PERSON><PERSON>, ModalFooter } from '@heroui/modal';
import { Switch } from '@heroui/switch';
import Link from 'next/link';

// Cookie consent options
interface CookieOptions {
	necessary: boolean;
	analytics: boolean;
	marketing: boolean;
}

// Cookie consent state stored in localStorage
interface CookieConsentState {
	accepted: boolean;
	options: CookieOptions;
	timestamp: number;
}

export const CookieConsentBanner = () => {
	// State for showing the banner
	const [showBanner, setShowBanner] = useState(false);

	// State for showing the settings modal
	const [showSettings, setShowSettings] = useState(false);

	// Cookie options state
	const [cookieOptions, setCookieOptions] = useState<CookieOptions>({
		necessary: true, // Always required
		analytics: true,
		marketing: false,
	});

	// Check if consent has been given on component mount
	useEffect(() => {
		const consentState = localStorage.getItem('cookie-consent');
		if (!consentState) {
			// No consent stored, show the banner
			setShowBanner(true);
		} else {
			try {
				const { options } = JSON.parse(consentState) as CookieConsentState;
				setCookieOptions(options);
			} catch (e) {
				// If parsing fails, show the banner again
				setShowBanner(true);
			}
		}
	}, []);

	// Save consent to localStorage
	const saveConsent = (accepted: boolean, options: CookieOptions) => {
		const consentState: CookieConsentState = {
			accepted,
			options,
			timestamp: Date.now(),
		};
		localStorage.setItem('cookie-consent', JSON.stringify(consentState));
		setShowBanner(false);
	};

	// Handle accepting all cookies
	const handleAcceptAll = () => {
		const allOptions: CookieOptions = {
			necessary: true,
			analytics: true,
			marketing: true,
		};
		setCookieOptions(allOptions);
		saveConsent(true, allOptions);
	};

	// Handle accepting only necessary cookies
	const handleAcceptNecessary = () => {
		const necessaryOptions: CookieOptions = {
			necessary: true,
			analytics: false,
			marketing: false,
		};
		setCookieOptions(necessaryOptions);
		saveConsent(true, necessaryOptions);
	};

	// Handle saving custom settings
	const handleSaveSettings = () => {
		saveConsent(true, cookieOptions);
		setShowSettings(false);
	};

	// Handle opening settings modal
	const handleOpenSettings = () => {
		setShowSettings(true);
	};

	// Handle closing settings modal
	const handleCloseSettings = () => {
		setShowSettings(false);
	};

	// Update cookie option
	const handleOptionChange = (option: keyof CookieOptions, value: boolean) => {
		setCookieOptions((prev) => ({
			...prev,
			[option]: value,
		}));
	};

	return (
		<>
			{/* Cookie Consent Banner */}
			{showBanner && (
				<div className="fixed bottom-4 left-4 z-50 max-w-xs bg-black border border-gray-800 rounded-xl shadow-lg overflow-hidden animate-fade-in">
					<div className="p-3">
						<h3 className="text-base font-semibold mb-1.5">Cookie Consent</h3>
						<p className="text-xs text-gray-400 mb-3">
							We use cookies to enhance your browsing experience, serve personalized
							ads or content, and analyze our traffic. Read our{' '}
							<Link
								href="/privacy"
								className="text-zeco-purple hover:text-purple-400 underline"
							>
								Privacy Policy
							</Link>{' '}
							to learn more.
						</p>
						<div className="flex flex-col gap-2">
							<Button
								size="sm"
								className="flex items-center justify-center font-semibold rounded-md text-xs py-1.5 cursor-pointer bg-white border border-white hover:border-none text-black hover:bg-gradient-to-r from-fuchsia-500 via-purple-600 to-violet-700 hover:text-white w-full transition-all duration-300"
								onPress={handleAcceptAll}
							>
								Accept All
							</Button>
							<div className="flex gap-2 w-full">
								<Button
									size="sm"
									variant="flat"
									className="bg-transparent border border-gray-700 text-white text-xs py-1.5 flex-1"
									onPress={handleAcceptNecessary}
								>
									Necessary Only
								</Button>
								<Button
									size="sm"
									variant="flat"
									className="bg-transparent border border-gray-700 text-white text-xs py-1.5 flex-1"
									onPress={handleOpenSettings}
								>
									Customize
								</Button>
							</div>
						</div>
					</div>
				</div>
			)}

			{/* Cookie Settings Modal */}
			<Modal
				isOpen={showSettings}
				onOpenChange={setShowSettings}
				classNames={{
					backdrop: 'backdrop-blur-md',
					base: 'bg-[#090909]',
					header: 'p-6 border-b border-default-50',
					body: 'flex flex-col p-6 text-sm text-white/60 gap-6',
					footer: 'flex justify-end pt-0 gap-4',
				}}
			>
				<ModalContent>
					{() => (
						<>
							<ModalHeader>
								<h3 className="text-xl font-semibold">Cookie Settings</h3>
							</ModalHeader>
							<ModalBody>
								<div className="space-y-6">
									<div className="flex justify-between items-center">
										<div>
											<h4 className="font-medium text-white">
												Necessary Cookies
											</h4>
											<p className="text-sm text-gray-400">
												These cookies are essential for the website to
												function properly.
											</p>
										</div>
										<Switch
											isSelected={cookieOptions.necessary}
											isDisabled={true} // Always required
											className="ml-4"
										/>
									</div>

									<div className="flex justify-between items-center">
										<div>
											<h4 className="font-medium text-white">
												Analytics Cookies
											</h4>
											<p className="text-sm text-gray-400">
												These cookies help us understand how visitors
												interact with our website.
											</p>
										</div>
										<Switch
											isSelected={cookieOptions.analytics}
											onValueChange={(value) =>
												handleOptionChange('analytics', value)
											}
											className="ml-4"
										/>
									</div>

									<div className="flex justify-between items-center">
										<div>
											<h4 className="font-medium text-white">
												Marketing Cookies
											</h4>
											<p className="text-sm text-gray-400">
												These cookies are used to track visitors across
												websites to display relevant advertisements.
											</p>
										</div>
										<Switch
											isSelected={cookieOptions.marketing}
											onValueChange={(value) =>
												handleOptionChange('marketing', value)
											}
											className="ml-4"
										/>
									</div>
								</div>
							</ModalBody>
							<ModalFooter>
								<Button
									variant="flat"
									className="bg-transparent border border-gray-700 text-white text-xs py-1.5"
									onPress={handleCloseSettings}
								>
									Cancel
								</Button>
								<Button
									className="flex items-center justify-center font-semibold rounded-md text-xs py-1.5 cursor-pointer bg-white border border-white hover:border-none text-black hover:bg-gradient-to-r from-fuchsia-500 via-purple-600 to-violet-700 hover:text-white transition-all duration-300"
									onPress={handleSaveSettings}
								>
									Save Settings
								</Button>
							</ModalFooter>
						</>
					)}
				</ModalContent>
			</Modal>
		</>
	);
};

export default CookieConsentBanner;
