'use client';

import { useDisclosure } from '@heroui/modal';
import CookieSettingsModal from './CookieSettingsModal';

interface CookieSettingsLinkProps {
	children: React.ReactNode;
	className?: string;
}

export const CookieSettingsLink = ({ children, className }: CookieSettingsLinkProps) => {
	const { isOpen, onOpen, onOpenChange } = useDisclosure();

	return (
		<>
			<button
				onClick={onOpen}
				className={className}
			>
				{children}
			</button>

			<CookieSettingsModal
				isOpen={isOpen}
				onOpenChange={onOpenChange}
			/>
		</>
	);
};

export default CookieSettingsLink;
