import { cn } from '@/utils/cn';

interface SectionHeaderProps {
	header?: string;
	title: string;
	description?: string;
	sectionClass?: string;
	headerClass?: string;
	titleClass?: string;
	descriptionClass?: string;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
	header = '',
	title,
	description,
	sectionClass,
	headerClass,
	titleClass,
	descriptionClass,
}) => {
	return (
		<div className={cn('z-50 flex w-full flex-col items-center', sectionClass)}>
			<span
				className={cn(
					'text-md font-semibold text-zeco-purple md:text-xl lg:text-2xl',
					headerClass
				)}
			>
				{header}
			</span>
			<h2
				className={cn(
					'bg-linear-to-b bg-clip-text py-2 text-center text-4xl font-bold md:text-5xl xl:pb-4 2xl:text-6xl',
					titleClass
				)}
			>
				{title}
			</h2>
			<p
				className={cn(
					'md:text-md text-center text-sm text-neutral-400 xl:text-xl',
					descriptionClass
				)}
			>
				{description}
			</p>
		</div>
	);
};
