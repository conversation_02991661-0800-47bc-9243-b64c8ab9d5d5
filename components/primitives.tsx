import { cn } from '@/utils/cn';
import { tv } from 'tailwind-variants';

interface SectionHeaderProps {
	header?: string;
	title: string;
	description?: string;
	sectionClass?: string;
	headerClass?: string;
	titleClass?: string;
	descriptionClass?: string;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
	header = '',
	title,
	description,
	sectionClass,
	headerClass,
	titleClass,
	descriptionClass,
}) => {
	return (
		<div className={cn('flex flex-col items-center w-full z-50', sectionClass)}>
			<span
				className={cn(
					'text-md md:text-xl lg:text-2xl font-semibold text-zeco-purple',
					headerClass
				)}
			>
				{header}
			</span>
			<h2
				className={cn(
					'text-4xl md:text-5xl py-2 2xl:text-6xl xl:pb-4 text-center bg-gradient-to-b font-bold bg-clip-text',
					titleClass
				)}
			>
				{title}
			</h2>
			<p
				className={cn(
					'text-sm md:text-md xl:text-xl text-neutral-400 text-center',
					descriptionClass
				)}
			>
				{description}
			</p>
		</div>
	);
};

export const title = tv({
	base: 'tracking-tight inline font-semibold',
	variants: {
		color: {
			violet: 'from-[#FF1CF7] to-[#b249f8]',
			yellow: 'from-[#FF705B] to-[#FFB457]',
			blue: 'from-[#5EA2EF] to-[#0072F5]',
			cyan: 'from-[#00b7fa] to-[#01cfea]',
			green: 'from-[#6FEE8D] to-[#17c964]',
			pink: 'from-[#FF72E1] to-[#F54C7A]',
			foreground: 'dark:from-[#FFFFFF] dark:to-[#4B4B4B]',
		},
		size: {
			sm: 'text-3xl lg:text-4xl',
			md: 'text-[2.3rem] lg:text-5xl leading-9',
			lg: 'text-4xl lg:text-6xl',
			xl: 'text-6xl lg:text-8xl',
		},
		fullWidth: {
			true: 'w-full block',
		},
	},
	defaultVariants: {
		size: 'md',
	},
	compoundVariants: [
		{
			color: ['violet', 'yellow', 'blue', 'cyan', 'green', 'pink', 'foreground'],
			class: 'bg-clip-text text-transparent bg-gradient-to-b',
		},
	],
});

export const subtitle = tv({
	base: 'w-full md:w-1/2 my-2 md:text-lg lg:text-xl text-default-800 block max-w-full',
	variants: {
		fullWidth: {
			true: '!w-full',
		},
	},
	defaultVariants: {
		fullWidth: true,
	},
});
