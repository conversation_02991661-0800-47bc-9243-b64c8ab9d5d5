'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dalBody } from '@heroui/modal';
import { getProviderIcon } from '@/utils/model-selection-utils';
import { CreditInfo } from './pricing';

interface CreditInfoModalProps {
	isOpen: boolean;
	onOpenChange: () => void;
	creditInfo: CreditInfo;
}

export const CreditInfoModal = ({ isOpen, onOpenChange, creditInfo }: CreditInfoModalProps) => {
	const { title, examples } = creditInfo;

	return (
		<Modal
			isOpen={isOpen}
			onOpenChange={onOpenChange}
			size="lg"
			classNames={{
				backdrop: 'backdrop-blur-md',
				base: 'bg-neutral-950 border-neutral-800',
			}}
		>
			<ModalContent>
				<ModalHeader className="justify-center">
					<h3 className="text-white text-lg font-medium text-center">{title}</h3>
				</ModalHeader>
				<ModalBody>
					<div className="flex flex-col gap-2 py-4">
						{examples.map((item, index) => (
							<div
								key={index}
								className="flex items-start gap-2 text-sm text-neutral-300"
							>
								<span className="text-neutral-500 text-2xl leading-none">•</span>
								{getProviderIcon(item.provider, item.type as any, 20)}

								<span className="font-semibold">
									{item.text} with {item.model}
								</span>
							</div>
						))}
					</div>
					<p className="text-center mt-4 text-neutral-400 font-medium text-xs">
						Mix and match any model to fit your project. Need more credits? Top up with
						a pack anytime.
					</p>
				</ModalBody>
			</ModalContent>
		</Modal>
	);
};
