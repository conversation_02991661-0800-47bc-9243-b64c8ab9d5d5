'use client';
import { memo, useMemo } from 'react';
import { motion } from 'framer-motion';
import SparklesCore from '@/components/ui/sparkles';
import HeroInteractiveDemo from './HeroInteractiveDemo';
import { <PERSON><PERSON>I, Gemini, Flux, Ideogram, <PERSON>M<PERSON>, <PERSON>, <PERSON> } from '@lobehub/icons';

export const HeroHeaderComponent: React.FC = memo(() => {
	const particleSettings = useMemo(
		() => ({
			id: 'particles-background',
			background: 'transparent',
			minSize: 0.6,
			maxSize: 1.0,
			particleDensity: 60,
			particleColor: '#9455D3',
		}),
		[]
	);

	return (
		<div
			id="hero-section"
			className="relative flex flex-col min-h-screen h-full pb-8 items-center justify-center overflow-hidden"
		>
			{/* Background Effects */}
			<div className="w-full absolute inset-0 h-full">
				<SparklesCore
					{...particleSettings}
					className="w-full h-full"
				/>
				{/* Fade overlay from top to bottom */}
				<div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/100 pointer-events-none" />
			</div>

			{/* Main Content */}
			<div className="relative z-10 flex flex-col items-center justify-center px-4 md:px-8">
				{/* Badge */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.5 }}
					className="mt-20 pt-12 sm:mt-0"
				>
					<div className="bg-slate-800 no-underline group cursor-pointer relative shadow-2xl shadow-zinc-900 rounded-full p-px text-xs font-semibold leading-6 text-white inline-block">
						<span className="absolute inset-0 overflow-hidden rounded-full">
							<span className="absolute inset-0 rounded-full bg-[image:radial-gradient(75%_100%_at_50%_0%,rgba(148,85,211,0.6)_0%,rgba(148,85,211,0)_75%)] opacity-0 transition-opacity duration-500 group-hover:opacity-100" />
						</span>
						<div className="relative flex space-x-2 items-center z-10 rounded-full bg-zinc-950 py-0.5 px-4 ring-1 ring-white/10">
							<span>🚀 Launching soon !</span>
						</div>
						<span className="absolute -bottom-0 left-[1.125rem] h-px w-[calc(100%-2.25rem)] bg-gradient-to-r from-fuchsia-400/0 via-fuchsia-400/90 to-fuchsia-400/0 transition-opacity duration-500 group-hover:opacity-40" />
					</div>
				</motion.div>

				{/* Main Heading */}
				<div className="text-3xl sm:text-4xl text-center md:text-7xl z-10  mt-8 font-bold mx-auto max-w-5xl">
					{'One Subscription for Everything AI'.split(' ').map((word, index) => (
						<motion.span
							key={index}
							initial={{ opacity: 0, filter: 'blur(4px)', y: 10 }}
							animate={{ opacity: 1, filter: 'blur(0px)', y: 0 }}
							transition={{
								duration: 0.3,
								delay: index * 0.1,
								ease: 'easeInOut',
							}}
							className="mr-2 sm:py-1 md:py-2 inline-block bg-clip-text bg-gradient-to-b"
						>
							{word}
						</motion.span>
					))}
				</div>

				{/* Description */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8, delay: 0.6 }}
					className="text-neutral-400 max-w-3xl text-center text-sm sm:text-base md:text-lg my-6 pb-10 sm:mb-8 sm:pb-4 md:pb-8 lg:pb-0 "
				>
					<p className="flex flex-wrap items-center justify-center gap-x-2 gap-y-1">
						<span className="inline-flex items-center gap-1">
							<OpenAI size={18} />
							<span className="text-white font-semibold">ChatGPT,</span>
						</span>
						<span className="inline-flex items-center gap-1">
							<Gemini.Color size={18} />
							<span className="text-white font-semibold">Gemini,</span>
						</span>
						<span className="inline-flex items-center gap-1">
							<Claude.Color size={18} />
							<span className="text-white font-semibold">Claude,</span>
						</span>
						<span className="inline-flex items-center gap-1">
							<Flux size={18} />
							<span className="text-white font-semibold">FLUX,</span>
						</span>
						<span className="inline-flex items-center gap-1">
							<Ideogram size={18} />
							<span className="text-white font-semibold">Ideogram,</span>
						</span>
						<span className="inline-flex items-center gap-1">
							<Kling.Color size={18} />
							<span className="text-white font-semibold">Kling,</span>
						</span>
						<span className="inline-flex items-center gap-1">
							<DeepMind.Color size={18} />
							<span className="text-white font-semibold">Veo</span>
						</span>
						and 10+ premium models in a single subscription—no extra plans, ever.
					</p>
				</motion.div>

				{/* Interactive Demo */}
				<HeroInteractiveDemo />
			</div>
		</div>
	);
});

HeroHeaderComponent.displayName = 'HeroComponent';

export default HeroHeaderComponent;
