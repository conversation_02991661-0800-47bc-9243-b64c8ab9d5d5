'use client';
import { PricingQuoteSection } from '@/components/landing-page/PricingQuoteSection';
import { SectionHeader } from '@/components/primitives';
import useUserLocation from '@/lib/hooks/useUserLocation';
import { plans } from './pricing';

export const PricingSection = () => {
	const { currency } = useUserLocation();

	return (
		<div
			id="pricing"
			className="mx-auto my-28 flex max-w-6xl flex-col items-center px-8"
		>
			<div className="w-full">
				<SectionHeader
					header={'Pricing'}
					title={'Best Plans For You'}
				/>
			</div>
			<div className="mt-12">
				<PricingQuoteSection
					items={plans}
					currency={currency}
				/>
			</div>
		</div>
	);
};

export default PricingSection;
