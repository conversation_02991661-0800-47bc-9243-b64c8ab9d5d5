'use client';

import { memo } from 'react';
import {
	GoogleWorkMarkLogo,
	PwCLogo,
	UdaanLogo,
	SwiggyWorkMarkLogo,
	AmazonWorkMarkLogo,
	MicrosoftWordmarkLogo,
	DeloitteLogo,
} from '@/components/icons';
import { InfiniteMovingCards } from '../ui/InfiniteMovingCards';

const companies = [
	{
		name: 'Google',
		icon: () => (
			<div className="flex justify-center items-center h-20 px-8">
				<GoogleWorkMarkLogo
					color="#a3a3a3"
					size={120}
				/>
			</div>
		),
	},
	{
		name: 'Swiggy',
		icon: () => (
			<div className="flex justify-center items-center h-20 px-8">
				<SwiggyWorkMarkLogo
					color="#a3a3a3"
					size={150}
				/>
			</div>
		),
	},
	{
		name: 'Amazon',
		icon: () => (
			<div className="flex justify-center items-center h-20 px-8 mt-2">
				<AmazonWorkMarkLogo
					color="#a3a3a3"
					size={125}
				/>
			</div>
		),
	},
	{
		name: 'Microsoft',
		icon: () => (
			<div className="flex justify-center items-center h-20 px-8">
				<MicrosoftWordmarkLogo
					color="#a3a3a3"
					size={160}
				/>
			</div>
		),
	},
	{
		name: 'udaan',
		icon: () => (
			<div className="flex py-4 px-8 justify-center items-center">
				<UdaanLogo
					color="#a3a3a3"
					size={50}
					className="mr-4"
				/>
				<span className="font-semibold font-sans-serif text-neutral-400 text-4xl tracking-tight leading-tight">
					udaan
				</span>
			</div>
		),
	},
	{
		name: 'PwC',
		icon: () => (
			<div className="flex justify-center items-center h-20 px-8">
				<PwCLogo
					color="#a3a3a3"
					size={90}
				/>
			</div>
		),
	},
	{
		name: 'Deloitte',
		icon: () => (
			<div className="flex justify-center items-center h-20 px-8">
				<DeloitteLogo
					color="#a3a3a3"
					size={145}
				/>
			</div>
		),
	},
];

const TrustedByComponent = memo(() => {
	return (
		<div className="bg-black py-20 px-4 text-white">
			<div className="mx-auto max-w-6xl text-center">
				<p className="text-neutral-400 my-8 text-sm md:text-md xl:text-lg uppercase font-semibold">
					Our beta users include innovators from
				</p>

				<InfiniteMovingCards
					items={companies}
					direction="left"
					speed="normal"
					pauseOnHover={true}
					className="py-8"
				/>
			</div>
		</div>
	);
});

TrustedByComponent.displayName = 'TrustedByComponent';

export default TrustedByComponent;
