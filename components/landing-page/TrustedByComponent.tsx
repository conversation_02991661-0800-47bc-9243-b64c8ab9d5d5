'use client';

import { memo } from 'react';
import {
	GoogleWorkMarkLogo,
	PwCLogo,
	UdaanLogo,
	SwiggyWorkMarkLogo,
	AmazonWorkMarkLogo,
	MicrosoftWordmarkLogo,
	DeloitteLogo,
} from '@/components/icons';
import { InfiniteMovingCards } from '../ui/InfiniteMovingCards';

const companies = [
	{
		name: 'Google',
		icon: () => (
			<div className="flex h-20 items-center justify-center px-8">
				<GoogleWorkMarkLogo
					color="#a3a3a3"
					size={120}
				/>
			</div>
		),
	},
	{
		name: 'Swiggy',
		icon: () => (
			<div className="flex h-20 items-center justify-center px-8">
				<SwiggyWorkMarkLogo
					color="#a3a3a3"
					size={150}
				/>
			</div>
		),
	},
	{
		name: 'Amazon',
		icon: () => (
			<div className="mt-2 flex h-20 items-center justify-center px-8">
				<AmazonWorkMarkLogo
					color="#a3a3a3"
					size={125}
				/>
			</div>
		),
	},
	{
		name: 'Microsoft',
		icon: () => (
			<div className="flex h-20 items-center justify-center px-8">
				<MicrosoftWordmarkLogo
					color="#a3a3a3"
					size={160}
				/>
			</div>
		),
	},
	{
		name: 'udaan',
		icon: () => (
			<div className="flex items-center justify-center px-8 py-4">
				<UdaanLogo
					color="#a3a3a3"
					size={50}
					className="mr-4"
				/>
				<span className="font-sans-serif text-4xl leading-tight font-semibold tracking-tight text-neutral-400">
					udaan
				</span>
			</div>
		),
	},
	{
		name: 'PwC',
		icon: () => (
			<div className="flex h-20 items-center justify-center px-8">
				<PwCLogo
					color="#a3a3a3"
					size={90}
				/>
			</div>
		),
	},
	{
		name: 'Deloitte',
		icon: () => (
			<div className="flex h-20 items-center justify-center px-8">
				<DeloitteLogo
					color="#a3a3a3"
					size={145}
				/>
			</div>
		),
	},
];

const TrustedByComponent = memo(() => {
	return (
		<div className="bg-black px-4 py-20 text-white">
			<div className="mx-auto max-w-6xl text-center">
				<p className="md:text-md my-8 text-sm font-semibold text-neutral-400 uppercase xl:text-lg">
					Our beta users include innovators from
				</p>

				<InfiniteMovingCards
					items={companies}
					direction="left"
					speed="normal"
					pauseOnHover={true}
					className="py-8"
				/>
			</div>
		</div>
	);
});

TrustedByComponent.displayName = 'TrustedByComponent';

export default TrustedByComponent;
