'use client';

import { cn } from '@/utils/cn';
import { useState } from 'react';
import { Divider } from '@heroui/divider';
import { Card, CardHeader, CardBody, CardFooter } from '@heroui/card';
import { CheckIcon } from '@/components/icons';
import { BentoGrid } from '@/components/ui/BentoGrid';
import Link from 'next/link';
import { CreditInfoModal } from './CreditInfoModal';
import { Plan } from './pricing';
import { useDisclosure } from '@heroui/modal';
import { IconInfoCircle, IconCrown } from '@tabler/icons-react';
import { Chip } from '@heroui/chip';
import ModelsOfferedModal from './ModelsOfferedModal';

export const PricingQuoteSection = ({
	items,
	className,
	currency = 'inr',
}: {
	items: Plan[];
	className?: string;
	currency: 'usd' | 'inr';
}) => {
	const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

	return (
		<div className={cn('flex flex-col items-center w-full h-full', className)}>
			<BentoGrid className="grid-cols-1 md:grid-cols-2 md:h-full lg:grid-cols-2 md:auto-rows-[34rem] gap-y-8 md:gap-x-2 lg:gap-x-4">
				{items.map((item, idx) => (
					<div
						key={item.title}
						className="relative group block p-1 h-full w-full"
						onMouseEnter={() => setHoveredIndex(idx)}
						onMouseLeave={() => setHoveredIndex(null)}
					>
						<PriceQuoteCard
							item={item}
							currency={currency}
							index={idx}
							hoveredIndex={hoveredIndex}
						/>
					</div>
				))}
			</BentoGrid>
			<div className="mt-8 text-center">
				<p className="text-xs text-gray-400">Auto-renews until cancelled.</p>
				<p className="text-xs text-gray-400 mt-2">
					By continuing, you agree to our{' '}
					<Link
						href="/terms"
						className="text-zeco-purple hover:underline"
					>
						Terms of Service
					</Link>{' '}
					and{' '}
					<Link
						href="/privacy"
						className="text-zeco-purple hover:underline"
					>
						Privacy Policy
					</Link>
				</p>
			</div>
		</div>
	);
};

export const PriceQuoteCard = ({
	className,
	item,
	currency,
	hoveredIndex,
	index,
}: {
	className?: string;
	item: Plan;
	currency: 'usd' | 'inr';
	hoveredIndex: number | null;
	index: number;
}) => {
	const {
		isOpen: isCreditModalOpen,
		onOpen: onCreditModalOpen,
		onOpenChange: onCreditModalOpenChange,
	} = useDisclosure();

	const {
		isOpen: isModelsModalOpen,
		onOpen: onModelsModalOpen,
		onOpenChange: onModelsModalOpenChange,
	} = useDisclosure();

	const handleCreditInfoClick = () => {
		onCreditModalOpen();
	};

	const handleModelsInfoClick = () => {
		onModelsModalOpen();
	};

	const planPrice = currency === 'usd' ? item.price.usd : item.price.inr;
	const currencySymbol = currency === 'usd' ? '$' : '₹';
	const isFreePlan = planPrice === '0';
	const isProPlan = !isFreePlan;

	return (
		<div className={cn('relative')}>
			{/* Glowing gradient background for Pro plan */}
			{isProPlan && (
				<div className="absolute flex inset-0.5 blur-md rounded-2xl bg-linear-to-r from-fuchsia-500 via-purple-600 to-violet-700" />
			)}
			<div
				className={cn(
					'max-w-sm lg:max-w-md row-span-1 rounded-2xl h-full w-full overflow-hidden bg-neutral-950 relative z-20',
					// Border styling
					'border border-neutral-900',
					className
				)}
			>
				<Card
					isBlurred
					className="relative z-50 h-full"
				>
					<CardHeader className="pr-8 pl-4 sm:pl-8 sm:pr-10 pt-8 pb-4">
						<div className="flex flex-col w-full">
							<div className="flex justify-between items-center">
								<p className="text-3xl font-semibold">{item.title}</p>
								{isProPlan && (
									<Chip
										size="sm"
										variant="light"
										radius="sm"
										className="bg-[#141414] text-[#9455D3]"
										startContent={
											<IconCrown
												color="#9455D3"
												fill="#9455D3"
												size={16}
											/>
										}
									>
										Pro
									</Chip>
								)}
							</div>
							<p className="text-sm text-gray-400 mt-2">{item.subtitle}</p>
						</div>
					</CardHeader>
					<Divider />
					<CardBody className="pr-8 pl-4 sm:pl-8 sm:pr-10 text-justify pt-6 ">
						<p className="flex items-center text-gray-400 text-md">
							<span>{currencySymbol}</span>
							<span className="text-2xl text-white font-semibold mx-1">
								{planPrice}
							</span>
							{!isFreePlan && <span>/ month</span>}
						</p>
						<div className="my-6 space-y-4">
							{item.features.map((feature, featureIndex) => (
								<div
									key={`${feature.name}-${featureIndex}`}
									className="flex items-start gap-3"
								>
									<div className="mt-1">
										<CheckIcon
											size={20}
											fill="#9455d3"
										/>
									</div>
									<p className="text-sm text-gray-300">
										{feature.name}
										{/* Show info icon inline at the end of text */}
										{(feature.infoType === 'credit' ||
											feature.infoType === 'model') && (
											<IconInfoCircle
												onClick={
													feature.infoType === 'credit'
														? handleCreditInfoClick
														: handleModelsInfoClick
												}
												className="inline ml-1 cursor-pointer text-gray-400 hover:text-white align-text-bottom"
												size={16}
											/>
										)}
									</p>
								</div>
							))}
						</div>
					</CardBody>
				</Card>

				{/* Credit Info Modal */}
				<CreditInfoModal
					isOpen={isCreditModalOpen}
					onOpenChange={onCreditModalOpenChange}
					creditInfo={item.creditInfo}
				/>

				{/* Models Offered Modal */}
				<ModelsOfferedModal
					isOpen={isModelsModalOpen}
					onOpenChange={onModelsModalOpenChange}
				/>
			</div>
		</div>
	);
};
