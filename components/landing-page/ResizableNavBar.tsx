'use client';

import { useState, useCallback } from 'react';
import Link from 'next/link';
import { Logo } from '@/components/icons';
import {
	Navbar,
	NavBody,
	NavItems,
	MobileNav,
	MobileNavHeader,
	MobileNavMenu,
	MobileNavToggle,
	NavbarButton,
} from '@/components/ui/resizable-navbar';
import FormComponent from '@/components/landing-page/FormComponent';

interface ResizableNavBarProps {
	isFixed?: boolean;
}

export function ResizableNavBar({ isFixed = false }: ResizableNavBarProps) {
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
	const [isModalOpen, setIsModalOpen] = useState(false);

	const handleOpenModal = useCallback(() => {
		setIsModalOpen(true);
	}, []);

	const handleCloseModal = useCallback(() => {
		setIsModalOpen(false);
	}, []);

	const toggleMobileMenu = useCallback(() => {
		setIsMobileMenuOpen((prev) => !prev);
	}, []);

	const closeMobileMenu = useCallback(() => {
		setIsMobileMenuOpen(false);
	}, []);

	const handleJoinBetaClick = useCallback(() => {
		setIsMobileMenuOpen(false);
		setIsModalOpen(true);
	}, []);

	const navItems = [
		{
			name: 'Home',
			link: '/#hero-section',
		},
		{
			name: 'Features',
			link: '/#features',
		},
		{
			name: 'Pricing',
			link: '/#pricing',
		},
		{
			name: 'FAQs',
			link: '/#faq',
		},
	];

	const mobileNavItems = [
		{
			name: 'Home',
			link: '/#hero-section',
		},
		{
			name: 'Features',
			link: '/#features',
		},
		{
			name: 'Pricing',
			link: '/#pricing',
		},
		{
			name: 'FAQs',
			link: '/#faq',
		},
	];

	return (
		<>
			<Navbar
				className="z-5000 mt-2"
				isFixed={isFixed}
			>
				{/* Desktop Navigation */}
				<NavBody>
					{/* Logo */}
					<Link
						href="/"
						className="relative z-20 flex items-center space-x-2 px-2 py-1"
					>
						<Logo
							size={32}
							fill="#9455d3"
						/>
						<span className="font-bold text-xl text-black dark:text-white">
							ZECO AI
						</span>
					</Link>

					{/* Navigation Items */}
					<NavItems items={navItems} />

					{/* Login Button */}
					<div className="flex items-center space-x-2">
						<NavbarButton onPress={handleOpenModal}>Join Beta</NavbarButton>
					</div>
				</NavBody>

				{/* Mobile Navigation */}
				<MobileNav>
					<MobileNavHeader>
						<Link
							href="/"
							className="flex items-center space-x-2 px-2 py-1"
						>
							<Logo
								size={28}
								fill="#9455d3"
							/>
							<span className="font-bold text-lg text-black dark:text-white">
								ZECO AI
							</span>
						</Link>
						<MobileNavToggle
							isOpen={isMobileMenuOpen}
							onClick={toggleMobileMenu}
						/>
					</MobileNavHeader>

					<MobileNavMenu
						isOpen={isMobileMenuOpen}
						onClose={closeMobileMenu}
					>
						{mobileNavItems.map((item, idx) => (
							<Link
								key={idx}
								href={item.link}
								className="block w-full text-left px-2 py-2 text-neutral-700 dark:text-neutral-200 hover:text-purple-600 dark:hover:text-purple-400 text-sm transition-colors duration-200"
								onClick={closeMobileMenu}
								role="menuitem"
							>
								{item.name}
							</Link>
						))}
						<button
							onClick={handleJoinBetaClick}
							className="block w-full text-left px-2 py-1 text-purple-600 dark:text-purple-400 font-semibold text-sm transition-colors duration-200"
							role="menuitem"
						>
							Join Waitlist
						</button>
					</MobileNavMenu>
				</MobileNav>
			</Navbar>
			<FormComponent
				isOpen={isModalOpen}
				onClose={handleCloseModal}
			/>
		</>
	);
}

export default ResizableNavBar;
