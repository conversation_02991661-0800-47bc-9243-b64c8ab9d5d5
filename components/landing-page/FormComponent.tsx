import { useState, FormEvent, useMemo, memo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON>dalContent, ModalHeader, ModalBody } from '@heroui/modal';
import { Input } from '@heroui/input';
import { CircularProgress } from '@heroui/progress';
import { Logo, MailIcon } from '@/components/icons';
import { Meteors } from '@/components/ui/meteors';
import { FormButton } from '@/components/ui/Buttons';
import { validateEmail } from '@/utils';

interface FormComponentProps {
	noAccess?: boolean;
	isOpen: boolean;
	onClose: () => void;
}

const FormComponent: React.FC<FormComponentProps> = ({ isOpen, onClose, noAccess }) => {
	const router = useRouter();

	const [email, setEmail] = useState('');
	const [submitting, setSubmitting] = useState(false);
	const [submitted, setSubmitted] = useState(false);
	const [showError, setShowError] = useState(false);

	const handleSubmit = useCallback(
		async (event: FormEvent) => {
			event.preventDefault();

			if (email !== '' && !validateEmail(email)) {
				setShowError(true);
				return;
			}

			setSubmitting(true);

			const formData = new FormData();
			formData.append('Email', email);

			try {
				const response = await fetch('/api/submit-form', {
					method: 'POST',
					body: formData,
				});
				if (!response.ok) {
					const data = await response.json().catch(() => ({}));
					throw new Error(
						data.message || 'Failed to register interest. Please try again.'
					);
				}
				setSubmitted(true);
				setEmail('');
				if (noAccess) {
					setTimeout(() => {
						router.push('/');
					}, 5000);
				}
			} catch (error: any) {
				console.error('Error submitting the form', error);
				alert(error.message || 'Failed to register interest. Please try again.');
			} finally {
				setSubmitting(false);
			}
		},
		[email, noAccess, router]
	);

	const meteors = useMemo(() => <Meteors number={5} />, []);

	return (
		<Modal
			classNames={{
				wrapper: 'z-9999',
				backdrop: 'z-9998',
			}}
			className="bg-neutral-950 border border-neutral-800"
			size="4xl"
			isOpen={isOpen}
			backdrop="blur"
			placement="top-center"
			hideCloseButton={noAccess}
			onOpenChange={(open) => !open && onClose()}
		>
			<ModalContent>
				{() => (
					<form
						onSubmit={handleSubmit}
						className="py-8 sm:p-8 flex flex-col items-center"
					>
						<ModalHeader className="flex flex-col gap-1 text-center justify-center items-center">
							<div className="relative p-1">
								<div className="absolute flex inset-0 blur-lg rounded-2xl bg-linear-to-r from-fuchsia-500 via-purple-600 to-violet-700" />
								<div className="z-50 p-3 bg-black border-zeco-purple border-1.5 rounded-2xl relative group transition duration-300 ease-in-out">
									<Logo
										size={56}
										fill="#ccc"
										className="flex md:hidden"
									/>
									<Logo
										size={72}
										fill="#ccc"
										className="hidden md:flex"
									/>
								</div>
							</div>
							<p className="text-2xl md:text-5xl lg:text-6xl py-2 xl:text-7xl xl:pb-4 text-center capitalize bg-linear-to-b from-neutral-200 to-neutral-500 font-bold bg-clip-text text-transparent">
								{noAccess ? 'Beta Access Required' : 'Join Our Beta Waitlist'}
							</p>
							<p className="text-xs sm:text-md md:text-lg font-normal text-neutral-500">
								{noAccess
									? 'It seems you do not have beta access with this email. Please sign up for our beta waitlist by entering your email below. '
									: 'Be the first one to access and experience ZECO! Sign up for our beta waitlist by entering your email below.'}
							</p>
						</ModalHeader>
						<ModalBody className={`${submitted ? 'py-4' : 'py-8'} sm:py-8 text-center`}>
							{submitted ? (
								<p className="text-sm sm:text-lg xl:text-xl font-normal text-neutral-100">
									{
										"You've been added to our waitlist!  We'll let you know the moment we launch. 🚀"
									}
								</p>
							) : (
								<div className="flex flex-col sm:flex-row gap-4 h-16 sm:items-start items-center justify-center">
									<Input
										type="text"
										isInvalid={showError}
										errorMessage={'Please enter a valid email'}
										className="w-full sm:max-w-xs h-12 sm:h-full mb-4 sm:mb-0"
										placeholder="Enter your email"
										value={email}
										disabled={submitting}
										onChange={(e) => {
											setEmail(e.target.value);
											setShowError(false);
										}}
										startContent={
											<MailIcon className="text-2xl text-gray-400 pointer-events-none shrink-0 mr-2" />
										}
										size="lg"
									/>
									{!submitting ? (
										<FormButton
											type="submit"
											isDisabled={email === ''}
										/>
									) : (
										<CircularProgress
											className="mt-2"
											classNames={{
												svg: 'w-6 h-6 drop-shadow-md',
												indicator: 'stroke-blue-500',
												track: 'stroke-gray-200',
											}}
										/>
									)}
								</div>
							)}
						</ModalBody>
						{meteors}
					</form>
				)}
			</ModalContent>
		</Modal>
	);
};

export default memo(FormComponent);
