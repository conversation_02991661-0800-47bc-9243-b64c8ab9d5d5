import { useState, FormEvent, useMemo, memo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON>dalContent, ModalHeader, ModalBody } from '@heroui/modal';
import { Input } from '@heroui/input';
import { CircularProgress } from '@heroui/progress';
import { Logo, MailIcon } from '@/components/icons';
import { Meteors } from '@/components/ui/meteors';
import { FormButton } from '@/components/ui/Buttons';
import { validateEmail } from '@/utils';

interface FormComponentProps {
	noAccess?: boolean;
	isOpen: boolean;
	onClose: () => void;
}

const FormComponent: React.FC<FormComponentProps> = ({ isOpen, onClose, noAccess }) => {
	const router = useRouter();

	const [email, setEmail] = useState('');
	const [submitting, setSubmitting] = useState(false);
	const [submitted, setSubmitted] = useState(false);
	const [showError, setShowError] = useState(false);

	const handleSubmit = useCallback(
		async (event: FormEvent) => {
			event.preventDefault();

			if (email !== '' && !validateEmail(email)) {
				setShowError(true);
				return;
			}

			setSubmitting(true);

			const formData = new FormData();
			formData.append('Email', email);

			try {
				const response = await fetch('/api/submit-form', {
					method: 'POST',
					body: formData,
				});
				if (!response.ok) {
					const data = await response.json().catch(() => ({}));
					throw new Error(
						data.message || 'Failed to register interest. Please try again.'
					);
				}
				setSubmitted(true);
				setEmail('');
				if (noAccess) {
					setTimeout(() => {
						router.push('/');
					}, 5000);
				}
			} catch (error: any) {
				console.error('Error submitting the form', error);
				alert(error.message || 'Failed to register interest. Please try again.');
			} finally {
				setSubmitting(false);
			}
		},
		[email, noAccess, router]
	);

	const meteors = useMemo(() => <Meteors number={5} />, []);

	return (
		<Modal
			classNames={{
				wrapper: 'z-9999',
				backdrop: 'z-9998',
			}}
			className="border border-neutral-800 bg-neutral-950"
			size="4xl"
			isOpen={isOpen}
			backdrop="blur"
			placement="top-center"
			hideCloseButton={noAccess}
			onOpenChange={(open) => !open && onClose()}
		>
			<ModalContent>
				{() => (
					<form
						onSubmit={handleSubmit}
						className="flex flex-col items-center py-8 sm:p-8"
					>
						<ModalHeader className="flex flex-col items-center justify-center gap-1 text-center">
							<div className="relative p-1">
								<div className="absolute inset-0 flex rounded-2xl bg-linear-to-r from-fuchsia-500 via-purple-600 to-violet-700 blur-lg" />
								<div className="group relative z-50 rounded-2xl border-1.5 border-zeco-purple bg-black p-3 transition duration-300 ease-in-out">
									<Logo
										size={56}
										fill="#ccc"
										className="flex md:hidden"
									/>
									<Logo
										size={72}
										fill="#ccc"
										className="hidden md:flex"
									/>
								</div>
							</div>
							<p className="bg-linear-to-b from-neutral-200 to-neutral-500 bg-clip-text py-2 text-center text-2xl font-bold text-transparent capitalize md:text-5xl lg:text-6xl xl:pb-4 xl:text-7xl">
								{noAccess ? 'Beta Access Required' : 'Join Our Beta Waitlist'}
							</p>
							<p className="sm:text-md text-xs font-normal text-neutral-500 md:text-lg">
								{noAccess
									? 'It seems you do not have beta access with this email. Please sign up for our beta waitlist by entering your email below. '
									: 'Be the first one to access and experience ZECO! Sign up for our beta waitlist by entering your email below.'}
							</p>
						</ModalHeader>
						<ModalBody className={`${submitted ? 'py-4' : 'py-8'} text-center sm:py-8`}>
							{submitted ? (
								<p className="text-sm font-normal text-neutral-100 sm:text-lg xl:text-xl">
									{
										"You've been added to our waitlist!  We'll let you know the moment we launch. 🚀"
									}
								</p>
							) : (
								<div className="flex h-16 flex-col items-center justify-center gap-4 sm:flex-row sm:items-start">
									<Input
										type="text"
										isInvalid={showError}
										errorMessage={'Please enter a valid email'}
										className="mb-4 h-12 w-full sm:mb-0 sm:h-full sm:max-w-xs"
										placeholder="Enter your email"
										value={email}
										disabled={submitting}
										onChange={(e) => {
											setEmail(e.target.value);
											setShowError(false);
										}}
										startContent={
											<MailIcon className="pointer-events-none mr-2 shrink-0 text-2xl text-gray-400" />
										}
										size="lg"
									/>
									{!submitting ? (
										<FormButton
											type="submit"
											isDisabled={email === ''}
										/>
									) : (
										<CircularProgress
											className="mt-2"
											classNames={{
												svg: 'w-6 h-6 drop-shadow-md',
												indicator: 'stroke-blue-500',
												track: 'stroke-gray-200',
											}}
										/>
									)}
								</div>
							)}
						</ModalBody>
						{meteors}
					</form>
				)}
			</ModalContent>
		</Modal>
	);
};

export default memo(FormComponent);
