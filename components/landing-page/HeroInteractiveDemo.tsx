'use client';
import { memo, useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@heroui/button';
import { Textarea } from '@heroui/input';
import { Tooltip } from '@heroui/tooltip';
import {
	IconArrowUp,
	IconCameraAi,
	IconMessageChatbot,
	IconVideo,
	IconWorld,
	IconWand,
	IconPaperclip,
} from '@tabler/icons-react';
import { getProviderIcon } from '@/utils/model-selection-utils';
import {
	ModelDisplayName,
	modelDisplayNameToProviderMap,
	ModelProvider,
	ConversationalModelsList,
} from '@/models/conversational/conversational-models';
import {
	ImageModelDisplayName,
	imageModelDisplayNameToProviderMap,
	ImageModelProvider,
	ImageModelsList,
} from '@/models/image/image-generation-models';
import {
	VideoModelDisplayName,
	videoModelDisplayNameToProviderMap,
	VideoModelProvider,
	VideoModelsList,
} from '@/models/video/video-generation-models';

import ModelSelectionDropdown from '@/components/chat/model-selection/ModelSelectionDropdown';
import FormComponent from '@/components/landing-page/FormComponent';

type DemoType = 'agent' | 'image' | 'video';

// Use the actual model lists from the app
const models = {
	image: ImageModelsList.filter((model) => !model.disabled),
	video: VideoModelsList.filter((model) => !model.disabled),
};

// Get model icon using the real provider system
const getModelIcon = (modelName: string, type: DemoType) => {
	let provider: string;

	if (type === 'agent') {
		provider = modelDisplayNameToProviderMap[modelName as ModelDisplayName];
	} else if (type === 'image') {
		provider = imageModelDisplayNameToProviderMap[modelName as ImageModelDisplayName];
	} else {
		provider = videoModelDisplayNameToProviderMap[modelName as VideoModelDisplayName];
	}

	return getProviderIcon(provider, type === 'agent' ? 'chat' : type, 16);
};

// Get the selected model provider for icons
const getSelectedModelProvider = (selectedModel: string, type: DemoType) => {
	if (type === 'agent') {
		return modelDisplayNameToProviderMap[selectedModel as ModelDisplayName];
	} else if (type === 'image') {
		return imageModelDisplayNameToProviderMap[selectedModel as ImageModelDisplayName];
	} else {
		return videoModelDisplayNameToProviderMap[selectedModel as VideoModelDisplayName];
	}
};

const placeholders = {
	agent: [
		'Search the web for latest AI trends and create a comprehensive report',
		'Analyze this PDF document and extract key business insights',
		'Research competitors using web search and compare their strategies',
		'Generate a comprehensive business plan with market research',
		'Create detailed product documentation from my notes',
		'Generate a marketing strategy using real-time market data',
		'Analyze multiple documents and create a unified summary',
		'Search for current industry news and write a trend analysis',
		'Process this spreadsheet data and provide actionable insights',
		'Research and fact-check information across multiple sources',
		'Generate content ideas based on trending topics from web search',
		'Create a competitive analysis using live web data',
	],
	image: [
		'Professional LinkedIn headshot, corporate style, clean background',
		'Modern minimalist logo for a tech startup called "DataFlow"',
		'Instagram post design: healthy meal prep with vibrant colors',
		'Product mockup: sleek wireless headphones on marble surface',
		'Book cover design: sci-fi thriller with neon cyberpunk aesthetic',
		'Website hero banner: diverse team collaborating in modern office',
		'Social media infographic: climate change statistics, clean design',
		'App icon design: fitness tracker with gradient and modern typography',
	],
	video: [
		'Product demo: smartphone rotating with feature highlights appearing',
		'Social media ad: coffee brewing process in slow motion',
		'Explainer animation: how blockchain works, simple visual metaphors',
		'Marketing video: team meeting transitioning to remote work setup',
		'Tutorial intro: coding workspace setup with smooth camera movements',
		'Brand story: startup journey from garage to office, time-lapse style',
		'App walkthrough: user interface interactions with smooth transitions',
		'Event promo: conference highlights with dynamic text overlays',
	],
};

interface HeroPromptSectionProps {
	input: string;
	onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
	type: DemoType;
	currentPlaceholder: number;
	onTryDemo: () => void;
	isWebSearchEnabled: boolean;
	onWebSearchToggle: () => void;
	selectedModel: string;
	onModelSelect: (model: string) => void;
	selectedModelProvider: ModelProvider | ImageModelProvider | VideoModelProvider;
}

const HeroPromptSection = memo(
	({
		input,
		onInputChange,
		type,
		currentPlaceholder,
		onTryDemo,
		isWebSearchEnabled,
		onWebSearchToggle,
		selectedModel,
		onModelSelect,
		selectedModelProvider,
	}: HeroPromptSectionProps) => {
		const isSearchEnabledClasses = (isEnabled: boolean) =>
			isEnabled
				? 'bg-blue-50 text-blue-500 border-blue-200 dark:bg-blue-950 dark:text-blue-400 dark:border-blue-900'
				: 'text-neutral-500 border-neutral-200 dark:border-neutral-700 dark:text-neutral-400';

		const textAreaClasses =
			'bg-[#101010] data-[focus=true]:bg-[#101010] data-[hover=true]:bg-[#101010]';

		return (
			<div className="w-full flex flex-col space-y-4 bg-[#101010] px-6 py-4 rounded-3xl">
				<div className="relative">
					<Textarea
						value={input}
						onChange={onInputChange}
						placeholder=""
						variant="flat"
						minRows={2}
						maxRows={6}
						classNames={{
							base: `${textAreaClasses} w-full`,
							input: 'text-white/90 placeholder:text-white/60',
							inputWrapper: `${textAreaClasses} group-data-[focus=true]:bg-[#101010]`,
						}}
						onKeyDown={(e) => {
							if (e.key === 'Enter' && !e.shiftKey) {
								e.preventDefault();
								onTryDemo();
							}
						}}
					/>
					{!input && (
						<div className="absolute left-3 top-3 pointer-events-none">
							<AnimatePresence mode="wait">
								<motion.p
									key={`placeholder-${type}-${currentPlaceholder}`}
									initial={{ opacity: 0, y: 5 }}
									animate={{ opacity: 1, y: 0 }}
									exit={{ opacity: 0, y: -5 }}
									transition={{ duration: 0.3, ease: 'linear' }}
									className="text-white/60 text-sm"
								>
									{placeholders[type][currentPlaceholder]}
								</motion.p>
							</AnimatePresence>
						</div>
					)}
				</div>
				<div className="flex flex-row gap-2 w-full">
					<Tooltip
						content="Attach files"
						placement="bottom"
						size="sm"
						radius="sm"
						className="bg-gray-50 text-[#090909]"
						delay={500}
						closeDelay={0}
					>
						<Button
							disableRipple
							isIconOnly
							variant="light"
							radius="full"
							size="md"
							className="text-neutral-500 dark:text-neutral-400"
							onPress={onTryDemo}
						>
							<IconPaperclip size={18} />
						</Button>
					</Tooltip>
					<div className="w-auto sm:w-[100px] h-10 flex items-center justify-center">
						<AnimatePresence mode="wait">
							{type === 'agent' && (
								<motion.div
									key="search"
									initial={{ opacity: 0 }}
									animate={{ opacity: 1 }}
									exit={{ opacity: 0 }}
									transition={{ duration: 0.2 }}
								>
									<div className="hidden sm:block">
										<Tooltip
											content="Search the Web"
											placement="bottom"
											size="sm"
											radius="sm"
											className="bg-gray-50 text-[#090909]"
											delay={500}
											closeDelay={0}
										>
											<Button
												disableRipple
												variant="bordered"
												size="md"
												radius="full"
												onPress={onWebSearchToggle}
												startContent={
													<IconWorld
														size={20}
														className="pointer-events-none select-none"
													/>
												}
												className={`w-[100px] gap-1 px-3 border ${isSearchEnabledClasses(isWebSearchEnabled)}`}
											>
												<span className="text-xs">Search</span>
											</Button>
										</Tooltip>
									</div>
									<div className="block sm:hidden">
										<Tooltip
											content="Search the Web"
											placement="bottom"
											size="sm"
											radius="sm"
											className="bg-gray-50 text-[#090909]"
											delay={500}
											closeDelay={0}
										>
											<Button
												disableRipple
												isIconOnly
												variant="light"
												radius="full"
												size="md"
												onPress={onWebSearchToggle}
												className={`${isWebSearchEnabled ? 'text-blue-500 bg-blue-500/10' : 'text-neutral-500 dark:text-neutral-400'}`}
											>
												<IconWorld
													size={20}
													className="pointer-events-none select-none"
												/>
											</Button>
										</Tooltip>
									</div>
								</motion.div>
							)}
							{(type === 'image' || type === 'video') && (
								<motion.div
									key="enhance"
									initial={{ opacity: 0 }}
									animate={{ opacity: 1 }}
									exit={{ opacity: 0 }}
									transition={{ duration: 0.2 }}
								>
									<div className="hidden sm:block">
										<Tooltip
											content="Enhance Prompt"
											placement="bottom"
											size="sm"
											radius="sm"
											className="bg-gray-50 text-[#090909]"
											delay={500}
											closeDelay={0}
										>
											<Button
												disableRipple
												variant="bordered"
												size="md"
												radius="full"
												onPress={onTryDemo}
												startContent={
													<IconWand
														size={18}
														className="pointer-events-none select-none"
													/>
												}
												className="w-[100px] text-neutral-500 border-neutral-200 dark:border-neutral-700 dark:text-neutral-400 gap-1 px-3"
											>
												<span className="text-xs">Enhance</span>
											</Button>
										</Tooltip>
									</div>
									<div className="block sm:hidden">
										<Tooltip
											content="Enhance Prompt"
											placement="bottom"
											size="sm"
											radius="sm"
											className="bg-gray-50 text-[#090909]"
											delay={500}
											closeDelay={0}
										>
											<Button
												disableRipple
												isIconOnly
												variant="light"
												radius="full"
												size="md"
												onPress={onTryDemo}
												className="text-neutral-500 dark:text-neutral-400"
											>
												<IconWand
													size={18}
													className="pointer-events-none select-none"
												/>
											</Button>
										</Tooltip>
									</div>
								</motion.div>
							)}
						</AnimatePresence>
					</div>
					<div className="grow" />
					<ModelSelectionDropdown
						type={type === 'agent' ? 'chat' : type}
						models={
							type === 'agent'
								? ConversationalModelsList.filter((model) => !model.disabled)
								: type === 'image'
									? models.image
									: models.video
						}
						selectedModel={selectedModel}
						selectedModelProvider={selectedModelProvider}
						onModelSelect={onModelSelect}
					/>
					<Tooltip
						content="Send message"
						placement="bottom"
						size="sm"
						radius="sm"
						className="bg-gray-50 text-[#090909]"
						delay={500}
						closeDelay={0}
					>
						<Button
							disableRipple
							isIconOnly
							variant="light"
							radius="full"
							size="md"
							onPress={onTryDemo}
							className="text-white/80 hover:bg-white/5"
						>
							<IconArrowUp size={18} />
						</Button>
					</Tooltip>
				</div>
			</div>
		);
	}
);
HeroPromptSection.displayName = 'HeroPromptSection';

interface DemoTypeTabProps {
	tabType: DemoType;
	activeType: DemoType;
	onPress: (type: DemoType) => void;
}

const DemoTypeTab = memo(({ tabType, activeType, onPress }: DemoTypeTabProps) => {
	const isEnabledClasses = (isEnabled: boolean) =>
		isEnabled
			? 'text-white border-white/40 bg-white/5'
			: 'text-neutral-500 border-transparent dark:text-neutral-400';

	const getIcon = () => {
		switch (tabType) {
			case 'agent':
				return (
					<IconMessageChatbot
						className="text-blue-500 pointer-events-none select-none"
						size={16}
					/>
				);
			case 'image':
				return (
					<IconCameraAi
						className="text-purple-500 pointer-events-none select-none"
						size={16}
					/>
				);
			case 'video':
				return (
					<IconVideo
						className="text-orange-500 pointer-events-none select-none"
						size={16}
					/>
				);
		}
	};

	return (
		<Tooltip
			content={`Switch to ${tabType}`}
			placement="bottom"
			size="sm"
			radius="sm"
			className="bg-gray-50 text-[#090909]"
			delay={500}
			closeDelay={0}
		>
			<Button
				disableRipple
				variant="bordered"
				size="md"
				radius="full"
				onPress={() => onPress(tabType)}
				startContent={getIcon()}
				className={`gap-1 px-3 border ${isEnabledClasses(activeType === tabType)}`}
			>
				<span className="text-xs capitalize">{tabType}</span>
			</Button>
		</Tooltip>
	);
});
DemoTypeTab.displayName = 'DemoTypeTab';

const HeroInteractiveDemo: React.FC = memo(() => {
	const [type, setType] = useState<DemoType>('agent');
	const [input, setInput] = useState('');
	const [selectedModel, setSelectedModel] = useState<string>(ModelDisplayName.GPT_5);

	const [isWebSearchEnabled, setIsWebSearchEnabled] = useState(false);
	const [currentPlaceholder, setCurrentPlaceholder] = useState(0);
	const [isModalOpen, setIsModalOpen] = useState(false);

	const handleOpenModal = useCallback(() => {
		setIsModalOpen(true);
	}, []);

	const handleCloseModal = useCallback(() => {
		setIsModalOpen(false);
	}, []);

	// Animated placeholder effect
	useEffect(() => {
		const interval = setInterval(() => {
			setCurrentPlaceholder((prev) => (prev + 1) % placeholders[type].length);
		}, 2500);
		return () => clearInterval(interval);
	}, [type]);

	// Reset placeholder when type changes
	useEffect(() => {
		setCurrentPlaceholder(0);
	}, [type]);

	const handleTypeChange = useCallback((newType: DemoType) => {
		setType(newType);
		if (newType === 'agent') {
			setSelectedModel(ModelDisplayName.GPT_5);
		} else if (newType === 'image') {
			setSelectedModel(ImageModelDisplayName.FLUX_1_1_PRO);
		} else {
			setSelectedModel(VideoModelDisplayName.Veo_3);
		}
		setInput('');
		setIsWebSearchEnabled(false);
	}, []);

	const handleTryDemo = handleOpenModal;

	const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
		setInput(e.target.value);
	}, []);

	const toggleWebSearch = useCallback(() => {
		setIsWebSearchEnabled((prev) => !prev);
	}, []);

	const handleModelSelect = useCallback((model: string) => {
		setSelectedModel(model);
	}, []);

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.8, delay: 0.8 }}
			className="w-full max-w-3xl mx-auto mt-10 flex flex-col gap-4"
		>
			{/* Prompt Section */}
			<HeroPromptSection
				input={input}
				onInputChange={handleInputChange}
				type={type}
				currentPlaceholder={currentPlaceholder}
				onTryDemo={handleTryDemo}
				isWebSearchEnabled={isWebSearchEnabled}
				onWebSearchToggle={toggleWebSearch}
				selectedModel={selectedModel}
				onModelSelect={handleModelSelect}
				selectedModelProvider={getSelectedModelProvider(selectedModel, type)}
			/>

			{/* Type selection tabs - below prompt box */}
			<div className="flex gap-2 justify-center w-full">
				{(['agent', 'image', 'video'] as const).map((t) => (
					<DemoTypeTab
						key={t}
						tabType={t}
						activeType={type}
						onPress={handleTypeChange}
					/>
				))}
			</div>
			<FormComponent
				isOpen={isModalOpen}
				onClose={handleCloseModal}
			/>
		</motion.div>
	);
});

HeroInteractiveDemo.displayName = 'HeroInteractiveDemo';

export default HeroInteractiveDemo;
