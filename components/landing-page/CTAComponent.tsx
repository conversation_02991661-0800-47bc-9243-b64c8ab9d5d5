'use client';

import { memo } from 'react';
import { CTAButton } from '@/components/ui/Buttons';
import { InfiniteMovingCards } from '@/components/ui/InfiniteMovingCards';
import { SectionHeader } from '@/components/primitives';
import { Logo } from '@/components/icons';

import {
	DeepSeek,
	Gemini,
	Kling,
	Luma,
	Meta,
	Mistral,
	Minimax,
	Flux,
	Ideogram,
	OpenAI,
	Stability,
	Claude,
	G<PERSON>,
} from '@lobehub/icons';

const CTAComponent: React.FC = memo(() => {
	return (
		<>
			<div className="relative h-1 w-full overflow-hidden bg-linear-to-r from-transparent via-gray-500 to-transparent" />

			<div className="relative z-50 my-32 flex w-full flex-col items-center rounded-2xl bg-transparent py-16">
				<ModelsTickerComponent />
				<div className="relative z-10 mt-12 p-1">
					<div className="absolute inset-0 flex rounded-2xl bg-linear-to-r from-fuchsia-500 via-purple-600 to-violet-700 blur-lg" />
					<div className="group relative rounded-2xl border-1.5 border-zeco-purple bg-black p-3 transition duration-300 ease-in-out">
						<Logo
							size={56}
							fill="#ccc"
							className="flex md:hidden"
						/>
						<Logo
							size={72}
							fill="#ccc"
							className="hidden md:flex"
						/>
					</div>
				</div>

				<SectionHeader
					header={'ZECO AI'}
					headerClass={'mt-8 mb-4'}
					title={'Best of AI. One Platform.'}
					description={'We are rolling out beta access. Join now!'}
				/>
				<CTAButton
					title="Join the Waitlist"
					className="mt-6 py-3"
					arrowNeeded={true}
				/>
			</div>
		</>
	);
});

CTAComponent.displayName = 'CTAComponent';

const modelsToDisplayRow1 = [
	{
		icon: () => (
			<Gemini.Combine
				size={48}
				type="color"
			/>
		),
	},
	{ icon: () => <OpenAI.Combine size={48} /> },
	{
		icon: () => (
			<Claude.Combine
				size={48}
				type="color"
			/>
		),
	},
	{
		icon: () => (
			<Mistral.Combine
				size={48}
				type="color"
			/>
		),
	},
	{
		icon: () => (
			<DeepSeek.Combine
				size={48}
				type="color"
			/>
		),
	},
	{ icon: () => <Grok.Combine size={48} /> },
	{
		icon: () => (
			<Meta.Combine
				size={48}
				type="color"
			/>
		),
	},
	{ icon: () => <Stability.Color size={48} /> },
	{
		icon: () => (
			<Kling.Combine
				size={48}
				type="color"
			/>
		),
	},
	{
		icon: () => (
			<Luma.Combine
				size={48}
				type="color"
			/>
		),
	},
	{
		icon: () => (
			<Minimax.Combine
				size={48}
				type="color"
			/>
		),
	},
	{ icon: () => <Flux.Combine size={48} /> },
	{ icon: () => <Ideogram.Combine size={48} /> },
];

const modelsToDisplayRow2 = [
	{ icon: () => <Stability.Color size={48} /> },
	{
		icon: () => (
			<Kling.Combine
				size={48}
				type="color"
			/>
		),
	},
	{
		icon: () => (
			<Luma.Combine
				size={48}
				type="color"
			/>
		),
	},
	{
		icon: () => (
			<Minimax.Combine
				size={48}
				type="color"
			/>
		),
	},
	{ icon: () => <Flux.Combine size={48} /> },
	{ icon: () => <Ideogram.Combine size={48} /> },
	{
		icon: () => (
			<Gemini.Combine
				size={48}
				type="color"
			/>
		),
	},
	{ icon: () => <OpenAI.Combine size={48} /> },
	{
		icon: () => (
			<Claude.Combine
				size={48}
				type="color"
			/>
		),
	},
	{
		icon: () => (
			<Mistral.Combine
				size={48}
				type="color"
			/>
		),
	},
	{
		icon: () => (
			<DeepSeek.Combine
				size={48}
				type="color"
			/>
		),
	},
	{ icon: () => <Grok.Combine size={48} /> },
	{
		icon: () => (
			<Meta.Combine
				size={48}
				type="color"
			/>
		),
	},
];

const ModelsTickerComponent = memo(() => {
	return (
		<div className="absolute inset-0 bottom-3/4 -z-10 flex h-64 w-full flex-col items-center justify-center overflow-hidden antialiased">
			<InfiniteMovingCards
				items={modelsToDisplayRow1}
				direction="right"
				speed="normal"
				className={'mb-4'}
				pauseOnHover={false}
			/>
			<InfiniteMovingCards
				items={modelsToDisplayRow2}
				direction="right"
				speed="slow"
				pauseOnHover={false}
				className={'mt-12 blur-sm'}
			/>
		</div>
	);
});
ModelsTickerComponent.displayName = 'ModelsTickerComponent';

export default CTAComponent;
