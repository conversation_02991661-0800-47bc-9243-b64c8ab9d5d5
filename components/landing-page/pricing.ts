import { ImageModelProvider } from '@/models/image/image-generation-models';
import { VideoModelProvider } from '@/models/video/video-generation-models';

export enum PricingPlanType {
	FREE = 'free',
	PRO = 'pro',
}

// Clean feature interface with optional info icon configuration
export interface PlanFeature {
	name: string;
	infoType?: 'credit' | 'model'; // Optional info icon type
}

export interface PriceQuote {
	usd: string;
	inr: string;
}

export interface CreditExample {
	text: string;
	model: string;
	provider: ImageModelProvider | VideoModelProvider;
	type: 'image' | 'video';
}

export interface CreditInfo {
	title: string;
	examples: CreditExample[];
}

export interface Plan {
	title: string;
	subtitle: string;
	price: PriceQuote;
	planType: PricingPlanType;
	features: PlanFeature[];
	creditInfo: CreditInfo; // Move credit info to plan level
}

// Credit examples data
export const CREDIT_EXAMPLES: Record<PricingPlanType, CreditInfo> = {
	[PricingPlanType.FREE]: {
		title: 'With your 25 Free Credits, you can generate either:',
		examples: [
			{
				text: '50 images',
				model: 'SDXL Hyper',
				provider: ImageModelProvider.StabilityAI,
				type: 'image',
			},
			{
				text: '6 video clips',
				model: 'Stable Video Diffusion',
				provider: VideoModelProvider.StabilityAI,
				type: 'video',
			},
			{
				text: '2 x 5-second video clips',
				model: 'Kling 1.6 Standard',
				provider: VideoModelProvider.KlingAI,
				type: 'video',
			},
			{
				text: '1 x 6-second video clip',
				model: 'Minimax Video API',
				provider: VideoModelProvider.MiniMax,
				type: 'video',
			},
		],
	},
	[PricingPlanType.PRO]: {
		title: 'With 500 ZECO Credits, you can generate either:',
		examples: [
			{
				text: '1000 images',
				model: 'SDXL Hyper',
				provider: ImageModelProvider.StabilityAI,
				type: 'image',
			},
			{
				text: '714 images',
				model: 'FLUX 1.1 Pro',
				provider: ImageModelProvider.BlackForestLabs,
				type: 'image',
			},
			{
				text: '500 images',
				model: 'Imagen 4 Fast, Ideogram',
				provider: ImageModelProvider.Ideogram,
				type: 'image',
			},
			{
				text: '333 images',
				model: 'GPT-Image-1',
				provider: ImageModelProvider.OpenAI,
				type: 'image',
			},
			{
				text: '250 images',
				model: 'Imagen 4',
				provider: ImageModelProvider.Google,
				type: 'image',
			},
			{
				text: '133 video clips',
				model: 'Stable Video Diffusion',
				provider: VideoModelProvider.StabilityAI,
				type: 'video',
			},
			{
				text: '44 x 5-second video clips',
				model: 'Kling 1.6 standard',
				provider: VideoModelProvider.KlingAI,
				type: 'video',
			},
			{
				text: '24 x 6-second video clips',
				model: 'Minimax Video API',
				provider: VideoModelProvider.MiniMax,
				type: 'video',
			},
			{
				text: '4 x 5-second video clips',
				model: 'Google Veo 2',
				provider: VideoModelProvider.Google,
				type: 'video',
			},
		],
	},
};

// Plans data with credit info at plan level
export const plans: Plan[] = [
	{
		title: 'Free',
		subtitle: 'Get started with ZECO for free',
		price: { usd: '0', inr: '0' },
		planType: PricingPlanType.FREE,
		creditInfo: CREDIT_EXAMPLES[PricingPlanType.FREE],
		features: [
			{
				name: 'Access to standard models',
				infoType: 'model',
			},
			{
				name: 'Limited access to features',
			},
			{
				name: '25 one-time trial credits for media generation',
				infoType: 'credit',
			},
			{
				name: 'Strict limits on AI Agent, Web Search & premium models',
			},
			{
				name: 'Community support',
			},
		],
	},
	{
		title: 'ZECO Pro',
		subtitle: 'The best AI plan you can get',
		price: { usd: '14.99', inr: '1299' },
		planType: PricingPlanType.PRO,
		creditInfo: CREDIT_EXAMPLES[PricingPlanType.PRO],
		features: [
			{
				name: 'Access to all models including pro and exclusive ones',
				infoType: 'model',
			},
			{
				name: 'Unlimited access to text models like GPT-5, o3, Gemini 2.5 Pro, Grok 3, Claude 4 & more',
			},
			{
				name: '500 credits for media generation',
				infoType: 'credit',
			},
			{
				name: 'Leading image models like GPT-Image 1, FLUX 1.1 Pro, Ideogram, Stable Diffusion & more',
			},
			{
				name: 'Leading video models like Google Veo 3, Luma, Kling & more',
			},
			{
				name: 'Priority Support & access to new features',
			},
		],
	},
];
