'use client';

import { AnimatePresence, motion } from 'motion/react';
import { useState } from 'react';
import {
	ConversationalModelsList,
	ModelProvider,
	ConversationalModel,
} from '@/models/conversational/conversational-models';
import {
	ImageModelsList,
	ImageModelProvider,
	ImageGenerationModel,
} from '@/models/image/image-generation-models';
import {
	VideoModelsList,
	VideoModelProvider,
	VideoGenerationModel,
} from '@/models/video/video-generation-models';
import { getProviderIcon } from '@/utils/model-selection-utils';
import { Card, CardBody, CardHeader, CardFooter } from '@heroui/card';
import {
	Modal,
	ModalBody,
	ModalContent,
	ModalHeader,
	useDisclosure,
	ModalFooter,
} from '@heroui/modal';
import { Tab, Tabs } from '@heroui/tabs';
import { Chip } from '@heroui/chip';
import { ModelTier } from '@/models/model-tiers';
import { cn } from '@/utils/cn';
import { IconCrown, IconInfoCircle } from '@tabler/icons-react';
import { Divider } from '@heroui/divider';
import { Tooltip } from '@heroui/tooltip';

type Model = ConversationalModel | ImageGenerationModel | VideoGenerationModel;

const ModelDetailsModal = ({
	isOpen,
	onOpenChange,
	selectedModelDetails,
}: {
	isOpen: boolean;
	onOpenChange: () => void;
	selectedModelDetails: Model | null;
}) => {
	if (!selectedModelDetails) return null;

	const { key, provider, tier, type } = selectedModelDetails as any;
	const isChat = type === 'chat';
	const chatModel = isChat ? (selectedModelDetails as ConversationalModel) : null;
	const primaryCategory = chatModel ? chatModel.categories[0] : null;

	return (
		<Modal
			hideCloseButton
			isOpen={isOpen}
			onOpenChange={onOpenChange}
			size="2xl"
			classNames={{
				backdrop: 'backdrop-blur-md',
				base: 'bg-neutral-950 border-neutral-800',
			}}
		>
			<ModalContent>
				<ModalHeader className="flex flex-col gap-2">
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-3">
							{getProviderIcon(
								provider as ModelProvider | ImageModelProvider | VideoModelProvider,
								type,
								32
							)}
							<span className="text-lg font-medium">{key}</span>
						</div>
						{tier === ModelTier.Premium && (
							<Chip
								size="sm"
								variant="light"
								radius="sm"
								className="bg-[#141414] text-[#9455D3]"
								startContent={
									<IconCrown
										color="#9455D3"
										fill="#9455D3"
										size={16}
									/>
								}
							>
								Pro
							</Chip>
						)}
					</div>
				</ModalHeader>
				<Divider />
				<ModalBody className="py-4">
					<div className="flex flex-col gap-4">
						{/* Key Features / Tags */}
						{isChat && primaryCategory && chatModel!.tags[primaryCategory] && (
							<div className="flex flex-wrap gap-1.5">
								{chatModel!.tags[primaryCategory]?.map((tag) => (
									<Chip
										key={tag}
										size="sm"
										variant="flat"
										className="bg-[#141414] text-[#A1A1AA]"
									>
										{tag}
									</Chip>
								))}
							</div>
						)}

						{/* Facts or Description */}
						{isChat && primaryCategory && chatModel!.facts[primaryCategory] ? (
							<>
								{chatModel!.facts[primaryCategory]?.map((fact, index) => (
									<div
										key={index}
										className="flex items-start gap-2"
									>
										<div className="mt-1 rounded-full bg-zeco-purple/10 p-1">
											<div className="h-1.5 w-1.5 rounded-full bg-zeco-purple" />
										</div>
										<p className="text-sm text-gray-300">{fact}</p>
									</div>
								))}
							</>
						) : (
							<p className="text-sm text-gray-300">
								{(selectedModelDetails as any).description}
							</p>
						)}
					</div>
				</ModalBody>
				<ModalFooter />
			</ModalContent>
		</Modal>
	);
};

const ModelCardRow = ({ model, onShowDetails }: { model: Model; onShowDetails: () => void }) => {
	const { key, provider, tier, type } = model as any;
	const isChat = type === 'chat';
	const chatModel = isChat ? (model as ConversationalModel) : null;
	const primaryCategory = chatModel ? chatModel.categories[0] : null;

	return (
		<Card
			isPressable
			disableRipple
			onPress={onShowDetails}
			classNames={{
				base: `w-full overflow-visible bg-transparent`,
				body: 'flex flex-row items-center gap-4 p-4',
			}}
		>
			<CardBody>
				{getProviderIcon(
					provider as ModelProvider | ImageModelProvider | VideoModelProvider,
					type,
					32
				)}
				<div className="flex flex-1 flex-col gap-1">
					<div className="flex flex-row items-center gap-1">
						<span className="text-sm font-semibold">{key}</span>
						<Tooltip
							content="Model Details"
							placement="bottom"
							size="sm"
							radius="sm"
							className="bg-gray-50 text-[#090909]"
							delay={500}
							closeDelay={0}
						>
							<IconInfoCircle
								size={16}
								className="cursor-pointer"
								onClick={(e) => {
									e.stopPropagation();
									onShowDetails();
								}}
							/>
						</Tooltip>
					</div>

					{isChat && primaryCategory && chatModel && chatModel.tags[primaryCategory] && (
						<div className="flex flex-row gap-1">
							{chatModel.tags[primaryCategory]?.map((tag) => (
								<Chip
									key={tag}
									size="sm"
									variant="flat"
									className="bg-[#141414] text-[#A1A1AA]"
								>
									{tag}
								</Chip>
							))}
						</div>
					)}
					{!isChat && (
						<p className="line-clamp-1 text-xs text-gray-400">
							{(model as any).description}
						</p>
					)}
				</div>
				<div className="flex items-center justify-end gap-1">
					{tier === ModelTier.Premium && (
						<Chip
							size="sm"
							variant="light"
							radius="sm"
							className="bg-[#141414] text-[#9455D3]"
							startContent={
								<IconCrown
									color="#9455D3"
									fill="#9455D3"
									size={16}
								/>
							}
						>
							Pro
						</Chip>
					)}
				</div>
			</CardBody>
		</Card>
	);
};

interface ModelsOfferedModalProps {
	isOpen: boolean;
	onOpenChange: () => void;
}

const ModelsOfferedModal = ({ isOpen, onOpenChange }: ModelsOfferedModalProps) => {
	const {
		isOpen: isDetailsOpen,
		onOpen: onDetailsOpen,
		onOpenChange: onDetailsOpenChange,
	} = useDisclosure();
	const [selectedModelDetails, setSelectedModelDetails] = useState<Model | null>(null);

	const [selectedTab, setSelectedTab] = useState<'text' | 'image' | 'video'>('text');

	const handleShowDetails = (model: Model) => {
		setSelectedModelDetails(model);
		onDetailsOpen();
	};

	const renderContent = () => {
		let models: Model[] = [];
		switch (selectedTab) {
			case 'text':
				models = ConversationalModelsList.filter((m) => !m.disabled).map((m) => ({
					...m,
					type: 'chat',
				}));
				break;
			case 'image':
				models = ImageModelsList.map((m) => ({ ...m, type: 'image' }));
				break;
			case 'video':
				models = VideoModelsList.map((m) => ({ ...m, type: 'video' }));
				break;
		}

		return (
			<AnimatePresence mode="popLayout">
				{models.map((model) => (
					<motion.div
						key={(model as any).key}
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						whileHover={{ scale: 1.02 }}
						transition={{ duration: 0.2 }}
						layout="position"
					>
						<ModelCardRow
							model={model}
							onShowDetails={() => handleShowDetails(model)}
						/>
					</motion.div>
				))}
			</AnimatePresence>
		);
	};

	return (
		<>
			<Modal
				isOpen={isOpen}
				onOpenChange={onOpenChange}
				size="4xl"
				scrollBehavior="inside"
				classNames={{
					backdrop: 'backdrop-blur-md',
					base: 'bg-neutral-950 border-neutral-800',
					header: 'flex flex-col gap-2 pt-4',
					body: cn('h-[70vh] flex-none overflow-y-auto p-4', 'flex flex-col gap-4'),
				}}
			>
				<ModalContent className="h-[80vh]">
					<ModalHeader>
						<div className="flex flex-row items-center justify-between">
							<Tabs
								variant="light"
								radius="full"
								selectedKey={selectedTab}
								onSelectionChange={(key) => setSelectedTab(key as any)}
								classNames={{
									cursor: 'hidden',
									tab: cn(
										'data-[hover-unselected=true]:bg-zeco-purple/20 data-[hover-unselected=true]:opacity-100',
										'bg-[#141414] data-[selected=true]:bg-zeco-purple'
									),
									tabContent: 'font-medium text-xs',
									base: 'justify-start',
									tabList: 'p-0',
								}}
							>
								<Tab
									key="text"
									title="Text"
								/>
								<Tab
									key="image"
									title="Image"
								/>
								<Tab
									key="video"
									title="Video"
								/>
							</Tabs>
						</div>
					</ModalHeader>
					<ModalBody>{renderContent()}</ModalBody>
				</ModalContent>
			</Modal>
			<ModelDetailsModal
				isOpen={isDetailsOpen}
				onOpenChange={onDetailsOpenChange}
				selectedModelDetails={selectedModelDetails}
			/>
		</>
	);
};

export default ModelsOfferedModal;
