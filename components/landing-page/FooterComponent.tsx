import { InstagramIcon, XIcon } from '@/components/icons';
import Link from 'next/link';
import { memo } from 'react';
import CookieSettingsLink from '@/components/cookie/CookieSettingsLink';

const FooterComponent: React.FC = () => {
	return (
		<footer className="py-4 md:py-8 px-4 w-full bg-black">
			<div className="flex flex-col md:flex-row justify-between">
				{/* First half section */}
				<div className="w-full md:w-2/5 flex mb-4 flex-col items-center md:items-start justify-center">
					<div className="text-md ml-1 md:text-2xl text-neutral-400">
						Built in India with love 🇮🇳 ❤️
					</div>
					<div className="flex text-6xl md:text-7xl lg:text-8xl xl:text-9xl font-bold justify-center bg-clip-text text-transparent bg-gradient-to-b from-violet-500 to-fuchsia-400 leading-none select-none">
						ZECO AI
					</div>
				</div>

				{/* Second half section */}
				<div className="w-full mb-4 md:w-3/5 flex flex-col md:flex-row items-center md:items-start md:justify-end">
					<FooterColumn title="PRODUCT">
						<FooterLink href="/#hero-section">Home</FooterLink>
						<FooterLink href="/#features">Features</FooterLink>
						<FooterLink href="/#pricing">Pricing</FooterLink>
						<FooterLink href="#faq">FAQs</FooterLink>
					</FooterColumn>
					<FooterColumn title="COMPANY">
						<FooterLink href="mailto:<EMAIL>">Contact Us</FooterLink>
						<FooterLink href="mailto:<EMAIL>">Business</FooterLink>
					</FooterColumn>
					<FooterColumn title="LEGAL">
						<FooterLink href="/privacy">Privacy Policy</FooterLink>
						<FooterLink href="/terms">Terms of use</FooterLink>
						<FooterLink href="/refund">Refund Policy</FooterLink>
						<div className="text-gray-500 hover:text-white transition-colors duration-300 cursor-pointer">
							<CookieSettingsLink className="text-gray-500 hover:text-white transition-colors duration-300">
								Cookie Settings
							</CookieSettingsLink>
						</div>
					</FooterColumn>
				</div>
			</div>
			<hr className="border-t border-gray-700" />
			<div className="flex flex-col md:flex-row items-center justify-between mt-2 md:mt-4 space-y-2">
				<div className="flex space-x-5 mt-4 md:mt-0 select-none">
					<FooterIconLink href="https://www.instagram.com/zeco.ai/">
						<InstagramIcon size={28} />
					</FooterIconLink>
					<FooterIconLink href="https://twitter.com/zeco_ai">
						<XIcon size={28} />
					</FooterIconLink>
				</div>
				<p className="text-md">&copy; 2025 ZECO AI. All Rights Reserved.</p>
			</div>
		</footer>
	);
};

FooterComponent.displayName = 'FooterComponent';

const FooterColumn: React.FC<{ title: string; children: React.ReactNode }> = memo(
	({ title, children }) => (
		<div className="w-1/2 md:w-32 lg:w-44 xl:w-56 my-4 text-center md:text-left flex flex-col">
			<h5 className="font-semibold mb-1 md:mb-4 text-lg">{title}</h5>
			<div className="flex-col flex space-y-1">{children}</div>
		</div>
	)
);

FooterColumn.displayName = 'FooterColumn';

const FooterLink: React.FC<{ href: string; children: React.ReactNode }> = memo(
	({ href, children }) => (
		<Link
			href={href}
			className="text-gray-500 hover:text-white transition-colors duration-300"
		>
			{children}
		</Link>
	)
);

FooterLink.displayName = 'FooterLink';

const FooterIconLink: React.FC<{ href: string; children: React.ReactNode }> = memo(
	({ href, children }) => (
		<Link
			href={href}
			className="text-gray-500 hover:text-white transition-colors duration-300"
		>
			{children}
		</Link>
	)
);

FooterIconLink.displayName = 'FooterIconLink';

export default memo(FooterComponent);
