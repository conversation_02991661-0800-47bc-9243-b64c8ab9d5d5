import { InstagramIcon, XIcon } from '@/components/icons';
import Link from 'next/link';
import { memo } from 'react';
import CookieSettingsLink from '@/components/cookie/CookieSettingsLink';

const FooterComponent: React.FC = () => {
	return (
		<footer className="w-full bg-black px-4 py-4 md:py-8">
			<div className="flex flex-col justify-between md:flex-row">
				{/* First half section */}
				<div className="mb-4 flex w-full flex-col items-center justify-center md:w-2/5 md:items-start">
					<div className="text-md ml-1 text-neutral-400 md:text-2xl">
						Built in India with love 🇮🇳 ❤️
					</div>
					<div className="flex justify-center bg-linear-to-b from-violet-500 to-fuchsia-400 bg-clip-text text-6xl leading-none font-bold text-transparent select-none md:text-7xl lg:text-8xl xl:text-9xl">
						ZECO AI
					</div>
				</div>

				{/* Second half section */}
				<div className="mb-4 flex w-full flex-col items-center md:w-3/5 md:flex-row md:items-start md:justify-end">
					<FooterColumn title="PRODUCT">
						<FooterLink href="/#hero-section">Home</FooterLink>
						<FooterLink href="/#features">Features</FooterLink>
						<FooterLink href="/#pricing">Pricing</FooterLink>
						<FooterLink href="#faq">FAQs</FooterLink>
					</FooterColumn>
					<FooterColumn title="COMPANY">
						<FooterLink href="mailto:<EMAIL>">Contact Us</FooterLink>
						<FooterLink href="mailto:<EMAIL>">Business</FooterLink>
					</FooterColumn>
					<FooterColumn title="LEGAL">
						<FooterLink href="/privacy">Privacy Policy</FooterLink>
						<FooterLink href="/terms">Terms of use</FooterLink>
						<FooterLink href="/refund">Refund Policy</FooterLink>
						<div className="cursor-pointer text-gray-500 transition-colors duration-300 hover:text-white">
							<CookieSettingsLink className="text-gray-500 transition-colors duration-300 hover:text-white">
								Cookie Settings
							</CookieSettingsLink>
						</div>
					</FooterColumn>
				</div>
			</div>
			<hr className="border-t border-gray-700" />
			<div className="mt-2 flex flex-col items-center justify-between space-y-2 md:mt-4 md:flex-row">
				<div className="mt-4 flex space-x-5 select-none md:mt-0">
					<FooterIconLink href="https://www.instagram.com/zeco.ai/">
						<InstagramIcon size={28} />
					</FooterIconLink>
					<FooterIconLink href="https://twitter.com/zeco_ai">
						<XIcon size={28} />
					</FooterIconLink>
				</div>
				<p className="text-md">&copy; 2025 ZECO AI. All Rights Reserved.</p>
			</div>
		</footer>
	);
};

FooterComponent.displayName = 'FooterComponent';

const FooterColumn: React.FC<{ title: string; children: React.ReactNode }> = memo(
	({ title, children }) => (
		<div className="my-4 flex w-1/2 flex-col text-center md:w-32 md:text-left lg:w-44 xl:w-56">
			<h5 className="mb-1 text-lg font-semibold md:mb-4">{title}</h5>
			<div className="flex flex-col space-y-1">{children}</div>
		</div>
	)
);

FooterColumn.displayName = 'FooterColumn';

const FooterLink: React.FC<{ href: string; children: React.ReactNode }> = memo(
	({ href, children }) => (
		<Link
			href={href}
			className="text-gray-500 transition-colors duration-300 hover:text-white"
		>
			{children}
		</Link>
	)
);

FooterLink.displayName = 'FooterLink';

const FooterIconLink: React.FC<{ href: string; children: React.ReactNode }> = memo(
	({ href, children }) => (
		<Link
			href={href}
			className="text-gray-500 transition-colors duration-300 hover:text-white"
		>
			{children}
		</Link>
	)
);

FooterIconLink.displayName = 'FooterIconLink';

export default memo(FooterComponent);
