import { cn } from '@/utils/cn';
import {
	IconRouteAltLeft,
	IconShare,
	IconSearch,
	IconBrain,
	IconLock,
	IconFile,
	IconBrush,
	IconTools,
} from '@tabler/icons-react';
import { SectionHeader } from '../primitives';
import { memo } from 'react';

const extraFeatures = [
	{
		title: 'Multi-Modality',
		description: 'Upload and interact directly with images, PDFs, and documents in your chat.',
		icon: <IconFile />,
	},
	{
		title: 'Prompt Enhancement',
		description:
			'Enhance your media generation prompts to suit the model requirements and get the best results.',
		icon: <IconBrush />,
	},
	{
		title: 'AI Media Tools',
		description:
			'Instantly remove backgrounds, upscale images, edit, and remix any of your media creations.',
		icon: <IconTools />,
	},
	{
		title: 'Seamless Sharing',
		description: 'Share chats and prompt workflows in a single click. No more copy-pasting.',
		icon: <IconShare />,
	},
	{
		title: 'Enterprise-Grade Security',
		description:
			'SOC 2-ready encryption means your data stays yours — always secure, always compliant.',
		icon: <IconLock />,
	},
	{
		title: 'Deep Research',
		description:
			'Get structured, in-depth reports synthesized from across the web to answer your most complex questions.',
		icon: <IconSearch />,
		comingSoon: true,
	},
	{
		title: 'Spaces: Shared Memory',
		description:
			'Group chats, files and results in a Space where context follows every conversation automatically.',
		icon: <IconBrain />,
		comingSoon: true,
	},
	{
		title: 'AI Workflows',
		description:
			'Chain models across steps, reuse and share — think Gemini ➜ GPT-4o ➜ Mistral in one visual flow.',
		icon: <IconRouteAltLeft />,
		comingSoon: true,
	},
];

export const ExtraFeaturesSection = memo(() => {
	return (
		<div className="relative">
			<SectionHeader
				header="More than an AI"
				title="A Complete Creative Suite"
				//description="The AI world is overwhelming. ZECO makes it effortless."
			/>
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4  relative z-10 py-10 max-w-7xl mx-auto">
				{extraFeatures.map((feature, index) => (
					<Feature
						key={feature.title}
						{...feature}
						index={index}
					/>
				))}
			</div>
		</div>
	);
});

ExtraFeaturesSection.displayName = 'ExtraFeaturesSection';

const Feature = memo(
	({
		title,
		description,
		icon,
		index,
		comingSoon,
	}: {
		title: string;
		description: string;
		icon: React.ReactNode;
		index: number;
		comingSoon?: boolean;
	}) => {
		return (
			<div
				className={cn(
					'flex flex-col lg:border-r  py-10 relative group/feature dark:border-neutral-800',
					(index === 0 || index === 4) && 'lg:border-l dark:border-neutral-800',
					index < 4 && 'lg:border-b dark:border-neutral-800'
				)}
			>
				{index < 4 && (
					<div className="opacity-0 group-hover/feature:opacity-100 transition duration-200 absolute inset-0 h-full w-full bg-gradient-to-t from-neutral-100 dark:from-neutral-800 to-transparent pointer-events-none" />
				)}
				{index >= 4 && (
					<div className="opacity-0 group-hover/feature:opacity-100 transition duration-200 absolute inset-0 h-full w-full bg-gradient-to-b from-neutral-100 dark:from-neutral-800 to-transparent pointer-events-none" />
				)}
				{comingSoon && (
					<div className="absolute top-4 right-4">
						<div className="text-white text-xs w-fit h-fit px-2 py-1 rounded-2xl bg-zeco-purple">
							Soon
						</div>
					</div>
				)}
				<div className="mb-4 relative z-10 px-10 text-neutral-600 dark:text-neutral-400">
					{icon}
				</div>
				<div className="text-lg font-bold mb-2 relative z-10 px-10">
					<div className="absolute left-0 inset-y-0 h-6 group-hover/feature:h-8 w-1 rounded-tr-full rounded-br-full bg-neutral-300 dark:bg-neutral-700 group-hover/feature:bg-zeco-purple transition-all duration-200 origin-center" />
					<span className="group-hover/feature:translate-x-2 transition duration-200 inline-block text-neutral-800 dark:text-neutral-100">
						{title}
					</span>
				</div>
				<p className="text-sm text-neutral-600 dark:text-neutral-300 max-w-xs relative z-10 px-10">
					{description}
				</p>
			</div>
		);
	}
);

Feature.displayName = 'Feature';
