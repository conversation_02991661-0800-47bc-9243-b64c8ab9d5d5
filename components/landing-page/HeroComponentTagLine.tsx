'use client';
import { memo, useMemo } from 'react';
import { TypewriterEffectSmooth } from '@/components/ui/TypeWriterEffect';
import { cn } from '@/utils/cn';

const HeroComponentTagLine: React.FC<{ className?: string }> = memo(({ className }) => {
	const words = useMemo(
		() => [
			{ text: 'One' },
			{ text: 'subscription' },
			{ text: 'for' },
			{ text: 'everything' },
			{
				text: 'AI.',
				className:
					'bg-clip-text text-transparent bg-linear-to-r from-neutral-200 to-neutral-400',
			},
		],
		[]
	);

	return (
		<div className={cn('flex items-center justify-center', className)}>
			<TypewriterEffectSmooth words={words} />
		</div>
	);
});

HeroComponentTagLine.displayName = 'HeroComponentTagLine';

export default HeroComponentTagLine;
