import { IconSvgProps } from '@/types';

export const Logo: React.FC<IconSvgProps> = ({
	size = 48,
	fill = 'white',
	className = '',
	...props
}) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 300 300"
		className={className}
		style={{ backgroundColor: 'transparent' }}
		height={size}
		width={size}
		fill={fill}
		stroke={fill}
	>
		<rect
			width="175.582348"
			height="20"
			rx="0"
			ry="0"
			transform="matrix(1.495927 0 0 1 21.918407 247.221573)"
		/>
		<rect
			width="175.582348"
			height="20"
			rx="0"
			ry="0"
			transform="matrix(1.495927 0 0 1 8.928026 30.709508)"
		/>
		<rect
			width="174.212598"
			height="20"
			rx="0"
			ry="0"
			transform="matrix(.57659-.998683 0.866025 0.5 153.816652 214.692699)"
		/>
		<rect
			width="174.212598"
			height="20"
			rx="0"
			ry="0"
			transform="matrix(.57659-.998683 0.866025 0.5 21.918407 247.22158)"
		/>
		<rect
			width="174.212598"
			height="20"
			rx="0"
			ry="0"
			transform="matrix(.281887 0.488242-.866025 0.5 26.248534 40.709508)"
		/>
		<rect
			width="174.212598"
			height="20"
			rx="0"
			ry="0"
			transform="matrix(.281887 0.488242-.866025 0.5 235.468604 162.163718)"
		/>
	</svg>
);

export const PDFIcon: React.FC<IconSvgProps> = ({
	size = 48,
	fill = 'white',
	className = '',
	...props
}) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		height={size}
		width={size}
		fill={fill}
		className={className}
		shapeRendering="geometricPrecision"
		textRendering="geometricPrecision"
		imageRendering="optimizeQuality"
		fillRule="evenodd"
		clipRule="evenodd"
		viewBox="0 0 500 511.56"
	>
		<path
			fillRule="nonzero"
			d="M117.91 0h201.68c3.93 0 7.44 1.83 9.72 4.67l114.28 123.67c2.21 2.37 3.27 5.4 3.27 8.41l.06 310c0 35.43-29.4 64.81-64.8 64.81H117.91c-35.57 0-64.81-29.24-64.81-64.81V64.8C53.1 29.13 82.23 0 117.91 0zM325.5 37.15v52.94c2.4 31.34 23.57 42.99 52.93 43.5l36.16-.04-89.09-96.4zm96.5 121.3l-43.77-.04c-42.59-.68-74.12-21.97-77.54-66.54l-.09-66.95H117.91c-21.93 0-39.89 17.96-39.89 39.88v381.95c0 21.82 18.07 39.89 39.89 39.89h264.21c21.71 0 39.88-18.15 39.88-39.89v-288.3z"
		/>
		<path
			fill="red"
			d="M28.04 194.61h443.92c15.43 0 28.04 12.63 28.04 28.04v188.54c0 15.4-12.63 28.04-28.04 28.04H28.04C12.64 439.23 0 426.61 0 411.19V222.65c0-15.43 12.62-28.04 28.04-28.04z"
		/>
		<path
			fill="#fff"
			fillRule="nonzero"
			d="M150.36 348.17H125.2v29.21H86.5V256.45h60.95c27.74 0 41.6 14.9 41.6 44.7 0 16.38-3.61 28.51-10.83 36.37-2.71 2.97-6.45 5.49-11.22 7.55-4.78 2.07-10.32 3.1-16.64 3.1zm-25.16-60.76v29.8h8.9c4.65 0 8.03-.49 10.16-1.45 2.13-.97 3.19-3.2 3.19-6.68v-13.54c0-3.49-1.06-5.71-3.19-6.68-2.13-.97-5.51-1.45-10.16-1.45h-8.9zm79.82 89.97V256.45h54.17c21.8 0 36.77 4.65 44.89 13.93 8.13 9.29 12.19 24.8 12.19 46.54 0 21.73-4.06 37.24-12.19 46.53-8.12 9.29-23.09 13.93-44.89 13.93h-54.17zm54.75-89.97h-16.06v59.02h16.06c5.29 0 9.13-.62 11.52-1.84 2.38-1.23 3.58-4.03 3.58-8.42v-38.5c0-4.39-1.2-7.2-3.58-8.42-2.39-1.23-6.23-1.84-11.52-1.84zm145.99 45.08h-32.89v44.89h-38.7V256.45h79.33l-4.84 30.96h-35.79v16.25h32.89v28.83z"
		/>
	</svg>
);

export const StabilityAIIcon: React.FC<IconSvgProps> = ({
	size = 24,
	fill = 'white',
	className = '',
}) => {
	return (
		<svg
			width={size}
			height={size}
			className={className}
			viewBox="0 0 256 213"
			version="1.1"
			xmlns="http://www.w3.org/2000/svg"
			preserveAspectRatio="xMidYMid"
		>
			<defs>
				<linearGradient
					x1="50%"
					y1="0%"
					x2="50%"
					y2="100%"
					id="linearGradient-1"
				>
					<stop
						stopColor="#9D39FF"
						offset="0%"
					></stop>
					<stop
						stopColor="#A380FF"
						offset="100%"
					></stop>
				</linearGradient>
			</defs>
			<g>
				<path
					d="M72.418311,212.449785 C121.895828,212.449785 154.076327,186.244638 154.076327,146.823527 C154.076327,116.252053 134.503517,96.8259255 99.5072246,88.7808007 L77.0381637,82.0413304 C57.327608,77.6165118 45.8227858,72.3031898 48.5334508,58.7294108 C50.7884827,47.4372353 57.5348302,41.0624336 73.2228235,41.0624336 C123.095165,41.0624336 141.57308,58.7294108 141.57308,58.7294108 L141.57308,16.2373709 C141.57308,16.2373709 123.58314,0 73.2228235,0 C25.7565874,0 2.1513352e-14,24.4237956 2.1513352e-14,62.235882 C2.1513352e-14,92.8073562 17.8493354,110.58622 54.0523968,119.033601 C56.5861391,119.667138 57.8811457,119.992613 57.9374165,120.010028 C63.4441876,121.714275 70.87496,123.966007 80.2297338,126.765222 C98.7335207,131.190041 103.491968,135.886327 103.491968,149.965296 C103.491968,162.837495 90.1175855,170.154389 72.418311,170.154389 C21.4319569,170.154389 2.1513352e-14,144.361226 2.1513352e-14,144.361226 L2.1513352e-14,191.438959 C2.1513352e-14,191.438959 13.4024656,212.449785 72.418311,212.449785 Z"
					fill="url(#linearGradient-1)"
				></path>
				<path
					d="M225.442113,209.266419 C242.957,209.266419 256,196.596076 256,179.453847 C256,161.938961 243.329657,149.641275 225.442113,149.641275 C207.927227,149.641275 195.256884,161.938961 195.256884,179.453847 C195.256884,196.968733 207.927227,209.266419 225.442113,209.266419 Z"
					fill="#E80000"
				></path>
			</g>
		</svg>
	);
};

export const DiscordIcon: React.FC<IconSvgProps> = ({ size = 24, width, height, ...props }) => {
	return (
		<svg
			height={size || height}
			viewBox="0 0 24 24"
			width={size || width}
			{...props}
		>
			<path
				d="M14.82 4.26a10.14 10.14 0 0 0-.53 1.1 14.66 14.66 0 0 0-4.58 0 10.14 10.14 0 0 0-.53-1.1 16 16 0 0 0-4.13 1.3 17.33 17.33 0 0 0-3 11.59 16.6 16.6 0 0 0 5.07 2.59A12.89 12.89 0 0 0 8.23 18a9.65 9.65 0 0 1-1.71-.83 3.39 3.39 0 0 0 .42-.33 11.66 11.66 0 0 0 10.12 0q.21.18.42.33a10.84 10.84 0 0 1-1.71.84 12.41 12.41 0 0 0 1.08 1.78 16.44 16.44 0 0 0 5.06-2.59 17.22 17.22 0 0 0-3-11.59 16.09 16.09 0 0 0-4.09-1.35zM8.68 14.81a1.94 1.94 0 0 1-1.8-2 1.93 1.93 0 0 1 1.8-2 1.93 1.93 0 0 1 1.8 2 1.93 1.93 0 0 1-1.8 2zm6.64 0a1.94 1.94 0 0 1-1.8-2 1.93 1.93 0 0 1 1.8-2 1.92 1.92 0 0 1 1.8 2 1.92 1.92 0 0 1-1.8 2z"
				fill="currentColor"
			/>
		</svg>
	);
};

export const XIcon: React.FC<IconSvgProps> = ({ size = 24, width, height, ...props }) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			shapeRendering="geometricPrecision"
			textRendering="geometricPrecision"
			imageRendering="optimizeQuality"
			fillRule="evenodd"
			clipRule="evenodd"
			viewBox="0 0 512 462.799"
			height={size}
			width={size}
		>
			<path
				fill="#fff"
				fillRule="nonzero"
				d="M403.229 0h78.506L310.219 196.04 512 462.799H354.002L230.261 301.007 88.669 462.799h-78.56l183.455-209.683L0 0h161.999l111.856 147.88L403.229 0zm-27.556 415.805h43.505L138.363 44.527h-46.68l283.99 371.278z"
			/>
		</svg>
	);
};

export const InstagramIcon: React.FC<IconSvgProps> = ({
	size = 24,
	fill = 'white',
}: IconSvgProps) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			width={size}
			height={size}
			viewBox="0 0 2476 2476"
			id="instagram"
			fill={fill}
		>
			<path d="M825.4 1238c0-227.9 184.7-412.7 412.6-412.7 227.9 0 412.7 184.8 412.7 412.7 0 227.9-184.8 412.7-412.7 412.7-227.9 0-412.6-184.8-412.6-412.7m-223.1 0c0 351.1 284.6 635.7 635.7 635.7s635.7-284.6 635.7-635.7-284.6-635.7-635.7-635.7S602.3 886.9 602.3 1238m1148-660.9c0 82 66.5 148.6 148.6 148.6 82 0 148.6-66.6 148.6-148.6s-66.5-148.5-148.6-148.5-148.6 66.5-148.6 148.5M737.8 2245.7c-120.7-5.5-186.3-25.6-229.9-42.6-57.8-22.5-99-49.3-142.4-92.6-43.3-43.3-70.2-84.5-92.6-142.3-17-43.6-37.1-109.2-42.6-229.9-6-130.5-7.2-169.7-7.2-500.3s1.3-369.7 7.2-500.3c5.5-120.7 25.7-186.2 42.6-229.9 22.5-57.8 49.3-99 92.6-142.4 43.3-43.3 84.5-70.2 142.4-92.6 43.6-17 109.2-37.1 229.9-42.6 130.5-6 169.7-7.2 500.2-7.2 330.6 0 369.7 1.3 500.3 7.2 120.7 5.5 186.2 25.7 229.9 42.6 57.8 22.4 99 49.3 142.4 92.6 43.3 43.3 70.1 84.6 92.6 142.4 17 43.6 37.1 109.2 42.6 229.9 6 130.6 7.2 169.7 7.2 500.3 0 330.5-1.2 369.7-7.2 500.3-5.5 120.7-25.7 186.3-42.6 229.9-22.5 57.8-49.3 99-92.6 142.3-43.3 43.3-84.6 70.1-142.4 92.6-43.6 17-109.2 37.1-229.9 42.6-130.5 6-169.7 7.2-500.3 7.2-330.5 0-369.7-1.2-500.2-7.2M727.6 7.5c-131.8 6-221.8 26.9-300.5 57.5-81.4 31.6-150.4 74-219.3 142.8C139 276.6 96.6 345.6 65 427.1 34.4 505.8 13.5 595.8 7.5 727.6 1.4 859.6 0 901.8 0 1238s1.4 378.4 7.5 510.4c6 131.8 26.9 221.8 57.5 300.5 31.6 81.4 73.9 150.5 142.8 219.3 68.8 68.8 137.8 111.1 219.3 142.8 78.8 30.6 168.7 51.5 300.5 57.5 132.1 6 174.2 7.5 510.4 7.5 336.3 0 378.4-1.4 510.4-7.5 131.8-6 221.8-26.9 300.5-57.5 81.4-31.7 150.4-74 219.3-142.8 68.8-68.8 111.1-137.9 142.8-219.3 30.6-78.7 51.6-168.7 57.5-300.5 6-132.1 7.4-174.2 7.4-510.4s-1.4-378.4-7.4-510.4c-6-131.8-26.9-221.8-57.5-300.5-31.7-81.4-74-150.4-142.8-219.3C2199.4 139 2130.3 96.6 2049 65c-78.8-30.6-168.8-51.6-300.5-57.5-132-6-174.2-7.5-510.4-7.5-336.3 0-378.4 1.4-510.5 7.5"></path>
		</svg>
	);
};

export const YoutubeIcon: React.FC<IconSvgProps> = ({
	size = 24,
	fill = 'white',
	className = '',
}: IconSvgProps) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			width={size}
			height={size}
			className={className}
			viewBox="0 0 333333 333333"
			shapeRendering="geometricPrecision"
			textRendering="geometricPrecision"
			imageRendering="optimizeQuality"
			fillRule="evenodd"
			clipRule="evenodd"
		>
			<path
				d="M329930 100020s-3254-22976-13269-33065c-12691-13269-26901-13354-33397-14124-46609-3396-116614-3396-116614-3396h-122s-69973 0-116608 3396c-6522 793-20712 848-33397 14124C6501 77044 3316 100020 3316 100020S-1 126982-1 154001v25265c0 26962 3315 53979 3315 53979s3254 22976 13207 33082c12685 13269 29356 12838 36798 14254 26685 2547 113354 3315 113354 3315s70065-124 116675-3457c6522-770 20706-848 33397-14124 10021-10089 13269-33090 13269-33090s3319-26962 3319-53979v-25263c-67-26962-3384-53979-3384-53979l-18 18-2-2zM132123 209917v-93681l90046 46997-90046 46684z"
				fill={fill}
			/>
		</svg>
	);
};

export const GithubIcon: React.FC<IconSvgProps> = ({ size = 24, width, height, ...props }) => {
	return (
		<svg
			height={size || height}
			viewBox="0 0 24 24"
			width={size || width}
			{...props}
		>
			<path
				clipRule="evenodd"
				d="M12.026 2c-5.509 0-9.974 4.465-9.974 9.974 0 4.406 2.857 8.145 6.821 9.465.499.09.679-.217.679-.481 0-.237-.008-.865-.011-1.696-2.775.602-3.361-1.338-3.361-1.338-.452-1.152-1.107-1.459-1.107-1.459-.905-.619.069-.605.069-.605 1.002.07 1.527 1.028 1.527 1.028.89 1.524 2.336 1.084 2.902.829.091-.645.351-1.085.635-1.334-2.214-.251-4.542-1.107-4.542-4.93 0-1.087.389-1.979 1.024-2.675-.101-.253-.446-1.268.099-2.64 0 0 .837-.269 2.742 1.021a9.582 9.582 0 0 1 2.496-.336 9.554 9.554 0 0 1 2.496.336c1.906-1.291 2.742-1.021 2.742-1.021.545 1.372.203 2.387.099 2.64.64.696 1.024 1.587 1.024 2.675 0 3.833-2.33 4.675-4.552 4.922.355.308.675.916.675 1.846 0 1.334-.012 2.41-.012 2.737 0 .267.178.577.687.479C19.146 20.115 22 16.379 22 11.974 22 6.465 17.535 2 12.026 2z"
				fill="currentColor"
				fillRule="evenodd"
			/>
		</svg>
	);
};

export const WebsiteIcon: React.FC<IconSvgProps> = ({
	size = 24,
	fill = 'white',
	className = '',
}) => {
	return (
		<svg
			height={size}
			width={size}
			fill={fill}
			className={className}
			viewBox="0 0 122.879 122.879"
			enableBackground="new 0 0 122.879 122.879"
		>
			<g>
				<path d="M109.465,89.503c0.182,0,0.359,0.019,0.533,0.053c1.146-1.998,2.191-4.095,3.135-6.286 c0.018-0.044,0.037-0.086,0.059-0.128c1.418-3.345,2.488-6.819,3.209-10.419c0.559-2.793,0.904-5.657,1.035-8.591h-16.893 c-0.307,8.574-2.867,17.03-7.639,25.371H109.465L109.465,89.503z M106.52,94.889H89.506c-5.164,7.481-12.121,14.87-20.838,22.167 c1.367-0.17,2.719-0.388,4.055-0.655c3.646-0.729,7.164-1.817,10.549-3.264l-0.002-0.004c3.441-1.48,6.646-3.212,9.609-5.199 c2.969-1.992,5.721-4.255,8.25-6.795l0.01-0.01l0,0C103.096,99.18,104.889,97.099,106.52,94.889L106.52,94.889z M54.21,117.055 c-8.716-7.296-15.673-14.685-20.838-22.166H16.361c1.631,2.21,3.423,4.291,5.379,6.24l0.01,0.011v-0.001 c2.53,2.54,5.282,4.803,8.25,6.795c2.962,1.987,6.167,3.719,9.609,5.199c0.043,0.019,0.086,0.038,0.128,0.059 c3.345,1.419,6.819,2.488,10.42,3.209C51.493,116.668,52.843,116.886,54.21,117.055L54.21,117.055z M12.852,89.503h17.122 c-4.771-8.341-7.332-16.797-7.637-25.371H5.445c0.13,2.934,0.475,5.797,1.034,8.59c0.729,3.646,1.818,7.164,3.264,10.549 l0.004-0.001C10.682,85.442,11.716,87.521,12.852,89.503L12.852,89.503z M5.445,58.747h16.997c0.625-8.4,3.412-16.857,8.407-25.371 H12.852c-1.136,1.982-2.17,4.061-3.105,6.234c-0.019,0.043-0.039,0.086-0.059,0.127C8.269,43.083,7.2,46.557,6.479,50.157 C5.92,52.95,5.575,55.814,5.445,58.747L5.445,58.747z M16.361,27.991h17.938c5.108-7.361,11.862-14.765,20.29-22.212 c-1.496,0.175-2.973,0.408-4.431,0.7c-3.647,0.729-7.164,1.818-10.549,3.264l0.001,0.003c-3.442,1.481-6.647,3.212-9.609,5.2 c-2.968,1.992-5.72,4.255-8.25,6.794l-0.011,0.01h0C19.784,23.7,17.992,25.78,16.361,27.991L16.361,27.991z M68.289,5.778 c8.428,7.447,15.182,14.851,20.291,22.212h17.939c-1.631-2.21-3.424-4.291-5.381-6.24l-0.01-0.01l0,0 c-2.529-2.54-5.281-4.802-8.25-6.794c-2.963-1.988-6.168-3.719-9.609-5.2c-0.043-0.018-0.086-0.038-0.127-0.059 c-3.346-1.418-6.82-2.488-10.42-3.208C71.264,6.187,69.785,5.954,68.289,5.778L68.289,5.778z M110.027,33.376H92.029 c4.996,8.514,7.783,16.971,8.408,25.371h16.998c-0.131-2.934-0.477-5.797-1.035-8.59c-0.73-3.646-1.818-7.164-3.264-10.549 l-0.004,0.002C112.197,37.437,111.164,35.358,110.027,33.376L110.027,33.376z M49.106,1.198C53.098,0.399,57.21,0,61.44,0 c4.23,0,8.341,0.399,12.333,1.198c3.934,0.788,7.758,1.97,11.473,3.547c0.051,0.018,0.1,0.037,0.148,0.058 c3.703,1.594,7.197,3.485,10.471,5.684c3.268,2.192,6.291,4.677,9.066,7.462c2.785,2.775,5.27,5.799,7.461,9.065 c2.197,3.275,4.09,6.768,5.684,10.473l-0.004,0.001l0.004,0.009c1.607,3.758,2.809,7.628,3.605,11.609 c0.799,3.992,1.197,8.104,1.197,12.334c0,4.23-0.398,8.343-1.197,12.335c-0.787,3.932-1.971,7.758-3.547,11.472 c-0.018,0.05-0.037,0.099-0.059,0.147c-1.594,3.705-3.486,7.197-5.684,10.472c-2.191,3.267-4.676,6.29-7.461,9.065 c-2.775,2.785-5.799,5.271-9.066,7.462c-3.273,2.198-6.768,4.091-10.471,5.684l-0.002-0.004l-0.01,0.004 c-3.758,1.606-7.629,2.808-11.609,3.604c-3.992,0.799-8.104,1.198-12.333,1.198c-4.229,0-8.342-0.399-12.334-1.198 c-3.933-0.787-7.758-1.97-11.474-3.546c-0.049-0.019-0.098-0.037-0.147-0.059c-3.705-1.593-7.197-3.485-10.472-5.684 c-3.266-2.191-6.29-4.677-9.065-7.462c-2.785-2.775-5.27-5.799-7.461-9.065c-2.198-3.274-4.09-6.767-5.684-10.472l0.004-0.002 l-0.004-0.009c-1.606-3.758-2.808-7.628-3.604-11.609C0.4,69.782,0,65.67,0,61.439c0-4.229,0.4-8.342,1.199-12.334 c0.787-3.933,1.97-7.758,3.546-11.473c0.018-0.049,0.037-0.099,0.058-0.147c1.594-3.705,3.485-7.198,5.684-10.473 c2.192-3.266,4.677-6.29,7.461-9.065c2.775-2.785,5.799-5.27,9.065-7.462c3.275-2.198,6.768-4.09,10.472-5.684l0.001,0.004 l0.009-0.004C41.254,3.197,45.125,1.995,49.106,1.198L49.106,1.198z M64.133,9.268v18.723h17.826 C77.275,21.815,71.34,15.575,64.133,9.268L64.133,9.268z M64.133,33.376v25.371h30.922c-0.699-8.332-3.789-16.788-9.318-25.371 H64.133L64.133,33.376z M64.133,64.132v25.371h22.51c5.328-8.396,8.189-16.854,8.531-25.371H64.133L64.133,64.132z M64.133,94.889 v18.952c7.645-6.283,13.902-12.601,18.746-18.952H64.133L64.133,94.889z M58.747,113.843V94.889H40 C44.843,101.24,51.1,107.559,58.747,113.843L58.747,113.843z M58.747,89.503V64.132H27.706c0.341,8.518,3.201,16.975,8.531,25.371 H58.747L58.747,89.503z M58.747,58.747V33.376H37.143c-5.529,8.583-8.619,17.04-9.319,25.371H58.747L58.747,58.747z M58.747,27.991 V9.266C51.54,15.573,45.604,21.815,40.92,27.991H58.747L58.747,27.991z" />
			</g>
		</svg>
	);
};

export const ChatGPTIcon: React.FC<IconSvgProps> = ({ size = 24, className = '' }) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			shapeRendering="geometricPrecision"
			textRendering="geometricPrecision"
			imageRendering="optimizeQuality"
			fillRule="evenodd"
			clipRule="evenodd"
			viewBox="0 0 512 512"
			height={size}
			width={size}
			className={className}
		>
			<rect
				fill="#10a37f"
				width="512"
				height="512"
				rx="104.187"
				ry="105.042"
			/>
			<path
				fill="#fff"
				fillRule="nonzero"
				d="M378.68 230.011a71.432 71.432 0 003.654-22.541 71.383 71.383 0 00-9.783-36.064c-12.871-22.404-36.747-36.236-62.587-36.236a72.31 72.31 0 00-15.145 1.604 71.362 71.362 0 00-53.37-23.991h-.453l-.17.001c-31.297 0-59.052 20.195-68.673 49.967a71.372 71.372 0 00-47.709 34.618 72.224 72.224 0 00-9.755 36.226 72.204 72.204 0 0018.628 48.395 71.395 71.395 0 00-3.655 22.541 71.388 71.388 0 009.783 36.064 72.187 72.187 0 0077.728 34.631 71.375 71.375 0 0053.374 23.992H271l.184-.001c31.314 0 59.06-20.196 68.681-49.995a71.384 71.384 0 0047.71-34.619 72.107 72.107 0 009.736-36.194 72.201 72.201 0 00-18.628-48.394l-.003-.004zM271.018 380.492h-.074a53.576 53.576 0 01-34.287-12.423 44.928 44.928 0 001.694-.96l57.032-32.943a9.278 9.278 0 004.688-8.06v-80.459l24.106 13.919a.859.859 0 01.469.661v66.586c-.033 29.604-24.022 53.619-53.628 53.679zm-115.329-49.257a53.563 53.563 0 01-7.196-26.798c0-3.069.268-6.146.79-9.17.424.254 1.164.706 1.695 1.011l57.032 32.943a9.289 9.289 0 009.37-.002l69.63-40.205v27.839l.001.048a.864.864 0 01-.345.691l-57.654 33.288a53.791 53.791 0 01-26.817 7.17 53.746 53.746 0 01-46.506-26.818v.003zm-15.004-124.506a53.5 53.5 0 0127.941-23.534c0 .491-.028 1.361-.028 1.965v65.887l-.001.054a9.27 9.27 0 004.681 8.053l69.63 40.199-24.105 13.919a.864.864 0 01-.813.074l-57.66-33.316a53.746 53.746 0 01-26.805-46.5 53.787 53.787 0 017.163-26.798l-.003-.003zm198.055 46.089l-69.63-40.204 24.106-13.914a.863.863 0 01.813-.074l57.659 33.288a53.71 53.71 0 0126.835 46.491c0 22.489-14.033 42.612-35.133 50.379v-67.857c.003-.025.003-.051.003-.076a9.265 9.265 0 00-4.653-8.033zm23.993-36.111a81.919 81.919 0 00-1.694-1.01l-57.032-32.944a9.31 9.31 0 00-4.684-1.266 9.31 9.31 0 00-4.684 1.266l-69.631 40.205v-27.839l-.001-.048c0-.272.129-.528.346-.691l57.654-33.26a53.696 53.696 0 0126.816-7.177c29.644 0 53.684 24.04 53.684 53.684a53.91 53.91 0 01-.774 9.077v.003zm-150.831 49.618l-24.111-13.919a.859.859 0 01-.469-.661v-66.587c.013-29.628 24.053-53.648 53.684-53.648a53.719 53.719 0 0134.349 12.426c-.434.237-1.191.655-1.694.96l-57.032 32.943a9.272 9.272 0 00-4.687 8.057v.053l-.04 80.376zm13.095-28.233l31.012-17.912 31.012 17.9v35.812l-31.012 17.901-31.012-17.901v-35.8z"
			/>
		</svg>
	);
};

export const ChatGPTPlusIcon: React.FC<IconSvgProps> = ({ size = 24, className = '' }) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			shapeRendering="geometricPrecision"
			textRendering="geometricPrecision"
			imageRendering="optimizeQuality"
			fillRule="evenodd"
			clipRule="evenodd"
			viewBox="0 0 512 512"
			height={size}
			width={size}
			className={className}
		>
			<rect
				fill="#ab68ff"
				width="512"
				height="512"
				rx="104.187"
				ry="105.042"
			/>
			<path
				fill="#fff"
				fillRule="nonzero"
				d="M378.68 230.011a71.432 71.432 0 003.654-22.541 71.383 71.383 0 00-9.783-36.064c-12.871-22.404-36.747-36.236-62.587-36.236a72.31 72.31 0 00-15.145 1.604 71.362 71.362 0 00-53.37-23.991h-.453l-.17.001c-31.297 0-59.052 20.195-68.673 49.967a71.372 71.372 0 00-47.709 34.618 72.224 72.224 0 00-9.755 36.226 72.204 72.204 0 0018.628 48.395 71.395 71.395 0 00-3.655 22.541 71.388 71.388 0 009.783 36.064 72.187 72.187 0 0077.728 34.631 71.375 71.375 0 0053.374 23.992H271l.184-.001c31.314 0 59.06-20.196 68.681-49.995a71.384 71.384 0 0047.71-34.619 72.107 72.107 0 009.736-36.194 72.201 72.201 0 00-18.628-48.394l-.003-.004zM271.018 380.492h-.074a53.576 53.576 0 01-34.287-12.423 44.928 44.928 0 001.694-.96l57.032-32.943a9.278 9.278 0 004.688-8.06v-80.459l24.106 13.919a.859.859 0 01.469.661v66.586c-.033 29.604-24.022 53.619-53.628 53.679zm-115.329-49.257a53.563 53.563 0 01-7.196-26.798c0-3.069.268-6.146.79-9.17.424.254 1.164.706 1.695 1.011l57.032 32.943a9.289 9.289 0 009.37-.002l69.63-40.205v27.839l.001.048a.864.864 0 01-.345.691l-57.654 33.288a53.791 53.791 0 01-26.817 7.17 53.746 53.746 0 01-46.506-26.818v.003zm-15.004-124.506a53.5 53.5 0 0127.941-23.534c0 .491-.028 1.361-.028 1.965v65.887l-.001.054a9.27 9.27 0 004.681 8.053l69.63 40.199-24.105 13.919a.864.864 0 01-.813.074l-57.66-33.316a53.746 53.746 0 01-26.805-46.5 53.787 53.787 0 017.163-26.798l-.003-.003zm198.055 46.089l-69.63-40.204 24.106-13.914a.863.863 0 01.813-.074l57.659 33.288a53.71 53.71 0 0126.835 46.491c0 22.489-14.033 42.612-35.133 50.379v-67.857c.003-.025.003-.051.003-.076a9.265 9.265 0 00-4.653-8.033zm23.993-36.111a81.919 81.919 0 00-1.694-1.01l-57.032-32.944a9.31 9.31 0 00-4.684-1.266 9.31 9.31 0 00-4.684 1.266l-69.631 40.205v-27.839l-.001-.048c0-.272.129-.528.346-.691l57.654-33.26a53.696 53.696 0 0126.816-7.177c29.644 0 53.684 24.04 53.684 53.684a53.91 53.91 0 01-.774 9.077v.003zm-150.831 49.618l-24.111-13.919a.859.859 0 01-.469-.661v-66.587c.013-29.628 24.053-53.648 53.684-53.648a53.719 53.719 0 0134.349 12.426c-.434.237-1.191.655-1.694.96l-57.032 32.943a9.272 9.272 0 00-4.687 8.057v.053l-.04 80.376zm13.095-28.233l31.012-17.912 31.012 17.9v35.812l-31.012 17.901-31.012-17.901v-35.8z"
			/>
		</svg>
	);
};

export const GeminiLogo: React.FC<IconSvgProps> = ({ size = 24 }) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			width={size}
			height={size}
			viewBox="0 0 344 127"
			fill="none"
		>
			<mask
				id="mask0_958_15881"
				maskUnits="userSpaceOnUse"
				x="0"
				y="0"
				width="344"
				height="127"
			>
				<path
					fillRule="evenodd"
					clipRule="evenodd"
					d="M234.123 41.2204C235.489 44.3354 236.172 47.6638 236.172 51.2055C236.172 47.6638 236.833 44.3354 238.156 41.2204C239.521 38.1054 241.356 35.3958 243.66 33.0916C245.965 30.7873 248.674 28.9738 251.789 27.651C254.904 26.2855 258.233 25.6028 261.774 25.6028C258.233 25.6028 254.904 24.9414 251.789 23.6185C248.674 22.2531 245.965 20.4182 243.66 18.114C241.356 15.8097 239.521 13.1001 238.156 9.98507C236.833 6.87007 236.172 3.54171 236.172 0C236.172 3.54171 235.489 6.87007 234.123 9.98507C232.801 13.1001 230.987 15.8097 228.683 18.114C226.379 20.4182 223.669 22.2531 220.554 23.6185C217.439 24.9414 214.111 25.6028 210.569 25.6028C214.111 25.6028 217.439 26.2855 220.554 27.651C223.669 28.9738 226.379 30.7873 228.683 33.0916C230.987 35.3958 232.801 38.1054 234.123 41.2204ZM26.1532 123.14C31.3762 125.291 36.9448 126.366 42.859 126.366C48.8501 126.366 54.3035 125.406 59.2192 123.486C64.1349 121.566 68.3978 118.839 72.0078 115.306C75.6178 111.773 78.4213 107.587 80.4183 102.748C82.4153 97.8321 83.4138 92.4555 83.4138 86.6181V86.5029C83.4138 85.4276 83.337 84.429 83.1834 83.5073C83.1066 82.5856 82.9914 81.6255 82.8377 80.627H43.0895V90.1897H73.0447C72.7374 94.7982 71.6621 98.7922 69.8187 102.172C68.0521 105.475 65.7863 108.201 63.0212 110.352C60.3329 112.502 57.2222 114.115 53.689 115.191C50.2326 116.189 46.6226 116.689 42.859 116.689C38.7114 116.689 34.6789 115.92 30.7617 114.384C26.8445 112.848 23.3497 110.621 20.2774 107.702C17.2819 104.783 14.9008 101.288 13.1342 97.2176C11.3676 93.07 10.4843 88.4231 10.4843 83.2769C10.4843 78.1308 11.3292 73.5223 13.019 69.4514C14.7856 65.3038 17.1667 61.809 20.1622 58.9671C23.1577 56.0484 26.6141 53.8209 30.5313 52.2848C34.5253 50.7486 38.6346 49.9805 42.859 49.9805C46.0082 49.9805 49.0037 50.403 51.8456 51.2479C54.6875 52.0159 57.299 53.1297 59.68 54.589C62.1379 56.0484 64.2501 57.815 66.0167 59.8888L73.1599 52.5152C69.7035 48.598 65.287 45.564 59.9105 43.4134C54.6107 41.2628 48.9269 40.1875 42.859 40.1875C37.0216 40.1875 31.4914 41.2628 26.2684 43.4134C21.1223 45.564 16.5522 48.598 12.5582 52.5152C8.64093 56.4324 5.5686 61.0025 3.34116 66.2255C1.11372 71.4484 0 77.1323 0 83.2769C0 89.4216 1.11372 95.1054 3.34116 100.328C5.5686 105.551 8.64093 110.121 12.5582 114.039C16.4754 117.956 21.0071 120.99 26.1532 123.14ZM104.058 122.334C108.512 125.022 113.582 126.366 119.266 126.366C125.717 126.366 131.132 124.907 135.511 121.988C139.889 119.07 143.23 115.383 145.534 110.928L136.778 106.78C135.165 109.699 132.937 112.157 130.096 114.154C127.33 116.151 123.874 117.149 119.726 117.149C116.5 117.149 113.39 116.343 110.394 114.73C107.399 113.117 104.941 110.697 103.021 107.472C101.362 104.684 100.419 101.266 100.194 97.2176H146.456C146.533 96.8336 146.571 96.3343 146.571 95.7199C146.648 95.1054 146.686 94.5293 146.686 93.9917C146.686 88.1542 145.534 82.9697 143.23 78.438C141.002 73.9063 137.776 70.3731 133.552 67.8385C129.327 65.227 124.297 63.9212 118.459 63.9212C112.698 63.9212 107.668 65.3806 103.366 68.2993C99.065 71.1412 95.7238 74.9432 93.3428 79.7053C91.0385 84.4674 89.8864 89.652 89.8864 95.259C89.8864 101.25 91.1153 106.588 93.5732 111.274C96.1079 115.959 99.6027 119.646 104.058 122.334ZM100.781 88.8071C101.143 87.0971 101.66 85.4841 102.329 83.9682C103.789 80.6654 105.901 78.054 108.666 76.1338C111.508 74.1367 114.811 73.1382 118.574 73.1382C121.723 73.1382 124.373 73.6759 126.524 74.7512C128.675 75.7497 130.441 77.0554 131.824 78.6684C133.206 80.2814 134.205 82.0096 134.819 83.853C135.434 85.6196 135.779 87.271 135.856 88.8071H100.781ZM155.497 65.7646V124.523H165.866V91.8026C165.866 88.5767 166.519 85.5428 167.825 82.7009C169.131 79.859 170.936 77.5931 173.24 75.9033C175.544 74.1367 178.156 73.2534 181.074 73.2534C185.145 73.2534 188.294 74.444 190.522 76.825C192.826 79.1293 193.978 83.0849 193.978 88.6919V124.523H204.232V91.5722C204.232 88.3463 204.885 85.3507 206.191 82.5856C207.496 79.7437 209.301 77.4779 211.606 75.7881C213.91 74.0983 216.521 73.2534 219.44 73.2534C223.588 73.2534 226.775 74.4056 229.003 76.7098C231.307 79.0141 232.459 82.9697 232.459 88.5767V124.523H242.713V86.8485C242.713 80.0126 241.023 74.4824 237.644 70.2579C234.341 66.0335 229.156 63.9212 222.09 63.9212C217.405 63.9212 213.372 64.9965 209.993 67.1472C206.613 69.2978 203.925 72.0245 201.928 75.3273C200.545 71.9477 198.279 69.221 195.13 67.1472C192.058 64.9965 188.333 63.9212 183.955 63.9212C181.497 63.9212 179.039 64.4205 176.581 65.419C174.2 66.3407 172.088 67.608 170.244 69.221C168.401 70.7572 166.942 72.4854 165.866 74.4056H165.405V65.7646H155.497ZM252.045 65.7646V124.523H262.299V65.7646H252.045ZM251.93 53.3217C253.389 54.7042 255.118 55.3955 257.115 55.3955C259.188 55.3955 260.917 54.7042 262.299 53.3217C263.682 51.8623 264.373 50.1341 264.373 48.1371C264.373 46.0633 263.682 44.3351 262.299 42.9526C260.917 41.4932 259.188 40.7635 257.115 40.7635C255.118 40.7635 253.389 41.4932 251.93 42.9526C250.547 44.3351 249.856 46.0633 249.856 48.1371C249.856 50.1341 250.547 51.8623 251.93 53.3217ZM271.929 65.7646V124.523H282.298V91.8026C282.298 88.6535 282.951 85.6964 284.257 82.9313C285.64 80.0894 287.521 77.7851 289.902 76.0185C292.283 74.1751 295.087 73.2534 298.313 73.2534C302.614 73.2534 306.071 74.444 308.682 76.825C311.293 79.1293 312.599 83.0849 312.599 88.6919V124.523H322.968V86.8485C322.968 79.9358 321.125 74.4056 317.438 70.2579C313.751 66.0335 308.336 63.9212 301.193 63.9212C296.969 63.9212 293.128 64.9581 289.672 67.032C286.216 69.1058 283.719 71.5637 282.183 74.4056H281.722V65.7646H271.929ZM331.672 65.7646V124.523H341.926V65.7646H331.672ZM331.557 53.3217C333.016 54.7042 334.745 55.3955 336.742 55.3955C338.815 55.3955 340.544 54.7042 341.926 53.3217C343.309 51.8623 344 50.1341 344 48.1371C344 46.0633 343.309 44.3351 341.926 42.9526C340.544 41.4932 338.815 40.7635 336.742 40.7635C334.745 40.7635 333.016 41.4932 331.557 42.9526C330.175 44.3351 329.483 46.0633 329.483 48.1371C329.483 50.1341 330.175 51.8623 331.557 53.3217Z"
					fill="white"
				/>
			</mask>
			<g mask="url(#mask0_958_15881)">
				<rect
					x="-158.25"
					y="-455.443"
					width="832.09"
					height="685.324"
					fill="url(#paint0_linear_958_15881)"
				/>
			</g>
			<defs>
				<linearGradient
					id="paint0_linear_958_15881"
					x1="-57.4049"
					y1="130.441"
					x2="354.97"
					y2="30.369"
					gradientUnits="userSpaceOnUse"
				>
					<stop stopColor="#439DDF" />
					<stop
						offset="0.524208"
						stopColor="#4F87ED"
					/>
					<stop
						offset="0.781452"
						stopColor="#9476C5"
					/>
					<stop
						offset="0.888252"
						stopColor="#BC688E"
					/>
					<stop
						offset="1"
						stopColor="#D6645D"
					/>
				</linearGradient>
			</defs>
		</svg>
	);
};

export const MidJourneyLogo: React.FC<IconSvgProps> = ({
	size = 24,
	fill = 'white',
	className = '',
}) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			fill="#4299E1"
			height={size}
			width={size}
			className={className}
			shapeRendering="geometricPrecision"
			textRendering="geometricPrecision"
			imageRendering="optimizeQuality"
			fillRule="evenodd"
			clipRule="evenodd"
			viewBox="0 0 512 428.862"
		>
			<path
				fillRule="nonzero"
				d="M315.727 314.278c-34.675-13.59-71.246-20.273-109.715-20.045-45.411.271-89.115 13.359-126.204 37.771-7.383 4.852-15.279-4.41-9.688-10.456 3.158-3.423 5.989-6.923 8.493-10.499 22.421-32.048 35.685-68.188 39.792-108.42 6.957-68.245-14.952-133.303-48.527-192.599-1.38-2.433-.043-7.014 2.433-8.636C74.9-.303 77.542-.451 80.235.953 132.75 28.42 179.636 62.378 220.893 102.83c57.162 56.038 99.358 124.894 109.245 204.947.589 4.733-1.261 7.606-5.548 8.621-2.76.655-6.018-1.01-8.863-2.12zm52.41 95.631c8.821 6.616 17.016 5.976 25.395-.156 9.437-6.915 19.158-14.018 29.164-21.312 8.607-6.274 18.865-8.493 29.307-5.662 4.088 1.119 8.821 3.67 14.198 7.654a2169.868 2169.868 0 0028.296 20.671c3.064 2.191 6.35 3.31 9.86 3.358 4.486.066 7.018 2.323 7.597 6.771a4.672 4.672 0 01-.285 2.391c-1.29 3.319-3.713 4.984-7.27 4.993-6.373.029-12.183-1.783-17.427-5.434a955.133 955.133 0 01-30.047-21.838c-7.867-5.933-16.516-7.981-24.854-1.949a12480.728 12480.728 0 00-31.326 22.705c-8.859 6.441-18.476 8.266-28.852 5.478-4.391-1.186-9.238-3.771-14.539-7.754-9.74-7.312-18.955-14.027-27.643-20.144-7.511-5.302-15.094-5.269-22.747.099-8.394 5.89-17.2 12.306-26.419 19.248-5.994 4.515-11.173 7.365-15.536 8.551-10.309 2.816-19.955.995-28.936-5.464a2286.243 2286.243 0 01-28.696-20.969c-9.858-7.313-17.512-6.971-27.215.085a18230.827 18230.827 0 00-29.065 21.197c-8.469 6.184-17.973 7.887-28.509 5.108-4.382-1.157-9.219-3.732-14.512-7.725-9.152-6.905-18.954-14.023-29.406-21.354-5.932-4.169-14.853-3.159-20.472.939a3850.176 3850.176 0 01-32.735 23.602c-5.444 3.879-11.798 5.728-19.064 5.548-2.93-.066-4.993-1.888-6.188-5.463a4.026 4.026 0 01-.185-1.75c.446-4.4 2.878-6.701 7.298-6.9 4.126-.18 7.607-1.304 10.442-3.371a12303.151 12303.151 0 0131.242-22.692c.256-.19.308-.427.156-.711a2602.345 2602.345 0 00-13.97-24.555c-3.016-5.207-.355-10.955 5.833-11.353a823836.566 823836.566 0 00428.29-26.931 45.835 45.835 0 015.576-.028c4.354.242 6.943 5.164 5.734 9.119-.398 1.319-1.584 2.865-3.557 4.638-31.81 28.61-69.298 51.23-108.819 70.151-.693.341-.73.745-.114 1.209zM53.319 365.636a.17.17 0 00-.1.241l8.934 15.608a1.197 1.197 0 001.238.582c8.953-1.507 17 .214 24.142 5.165 9.456 6.545 19.457 13.795 30.004 21.752 7.924 5.976 16.233 8.081 24.655 2.12 8.241-5.833 18.02-12.941 29.335-21.325 10.214-7.569 20.372-10.272 32.636-6.601 3.518 1.052 8.369 3.897 14.553 8.535a1354.678 1354.678 0 0025.295 18.466c9.162 6.53 17.072 5.563 25.892-1.095a958.934 958.934 0 0129.99-21.781c12.662-8.835 28.04-7.042 40.261 2.148a537.008 537.008 0 0013.145 9.603c.419.305.984.363 1.47.146 35.174-16.069 67.429-35.299 96.764-57.692 1.243-.949 1.086-1.37-.469-1.266-132.354 8.336-263.751 16.603-394.189 24.797a17.908 17.908 0 00-3.556.597zm302.626-66.395c-16.573-64.745-45.269-126.858-86.582-177.533-17.764-21.785-36.98-39.849-57.646-54.189-5.193-3.599-2.291-12.433 4.054-12.405 1.622 0 3.386.431 5.293 1.294 93.354 42.111 160.646 122.491 199.257 216.443a349.933 349.933 0 0111.31 31.81c2.034 6.701-4.212 12.776-10.457 8.806-16.076-10.2-37.487-10.954-57.589-8.422-3.756.47-6.758-2.332-7.64-5.804zm13.06-9.005c14.369-1.423 29.492-.484 43.448 3.628a.4.4 0 00.483-.513c-27.841-75.557-75.385-144.214-140.7-191.19-1.185-.845-1.299-.716-.341.384 46.407 53.321 78.033 118.536 96.228 187.079.123.455.417.659.882.612zM122.061 95.346c20.301 70.066 15.18 144.727-21.354 207.936a.213.213 0 00.285.298c33.394-15.488 68.638-23.346 105.731-23.573 36.648-.228 72.347 5.842 107.098 18.21a.428.428 0 00.569-.471c-13.942-87.805-72.186-161.698-139.207-216.84-24.83-20.438-51.457-38.938-79.882-55.497-.92-.541-1.133-.333-.64.626 11.116 21.539 20.249 44.643 27.4 69.311z"
			/>
		</svg>
	);
};

export const AnthropicIcon: React.FC<IconSvgProps> = ({ size = 24, className = '' }) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			shapeRendering="geometricPrecision"
			textRendering="geometricPrecision"
			imageRendering="optimizeQuality"
			fillRule="evenodd"
			clipRule="evenodd"
			viewBox="0 0 512 512"
			height={size}
			width={size}
			className={className}
		>
			<rect
				fill="#CC9B7A"
				width="512"
				height="512"
				rx="104.187"
				ry="105.042"
			/>
			<path
				fill="#1F1F1E"
				fillRule="nonzero"
				d="M318.663 149.787h-43.368l78.952 212.423 43.368.004-78.952-212.427zm-125.326 0l-78.952 212.427h44.255l15.932-44.608 82.846-.004 16.107 44.612h44.255l-79.126-212.427h-45.317zm-4.251 128.341l26.91-74.701 27.083 74.701h-53.993z"
			/>
		</svg>
	);
};

export const MistralIcon: React.FC<IconSvgProps> = ({ size = 24, className = '' }) => {
	return (
		<svg
			height={size}
			width={size}
			className={className}
			viewBox="0 0 512 512"
			xmlns="http://www.w3.org/2000/svg"
			fillRule="evenodd"
			clipRule="evenodd"
			strokeLinejoin="round"
			strokeMiterlimit="2"
		>
			<path
				d="M189.08 303.228H94.587l.044-94.446h94.497l-.048 94.446z"
				fill="#1c1c1b"
				fillRule="nonzero"
			/>
			<path
				d="M283.528 397.674h-94.493l.044-94.446h94.496l-.047 94.446z"
				fill="#1c1c1b"
				fillRule="nonzero"
			/>
			<path
				d="M283.575 303.228H189.08l.046-94.446h94.496l-.047 94.446z"
				fill="#1c1c1b"
				fillRule="nonzero"
			/>
			<path
				d="M378.07 303.228h-94.495l.044-94.446h94.498l-.047 94.446zM189.128 208.779H94.633l.044-94.448h94.498l-.047 94.448zM378.115 208.779h-94.494l.045-94.448h94.496l-.047 94.448zM94.587 303.227H.093l.044-96.017h94.496l-.046 96.017z"
				fill="#1c1c1b"
				fillRule="nonzero"
			/>
			<path
				d="M94.633 208.779H.138l.046-94.448H94.68l-.047 94.448z"
				fill="#1c1c1b"
				fillRule="nonzero"
			/>
			<path
				d="M94.68 115.902H.185L.23 19.885h94.498l-.047 96.017zM472.657 114.331h-94.495l.044-94.446h94.497l-.046 94.446zM94.54 399.244H.046l.044-97.588h94.497l-.047 97.588z"
				fill="#1c1c1b"
				fillRule="nonzero"
			/>
			<path
				d="M94.495 492.123H0l.044-94.446H94.54l-.045 94.446zM472.563 303.228H378.07l.044-94.446h94.496l-.047 94.446zM472.61 208.779h-94.495l.044-94.448h94.498l-.047 94.448z"
				fill="#1c1c1b"
				fillRule="nonzero"
			/>
			<path
				d="M472.517 397.674h-94.494l.044-94.446h94.497l-.047 94.446z"
				fill="#1c1c1b"
				fillRule="nonzero"
			/>
			<path
				d="M472.47 492.121h-94.493l.044-96.017h94.496l-.047 96.017z"
				fill="#1c1c1b"
				fillRule="nonzero"
			/>
			<path
				d="M228.375 303.22h-96.061l.046-94.446h96.067l-.052 94.446z"
				fill="#ff7000"
				fillRule="nonzero"
			/>
			<path
				d="M322.827 397.666h-94.495l.044-96.018h94.498l-.047 96.018z"
				fill="#ff4900"
				fillRule="nonzero"
			/>
			<path
				d="M324.444 303.22h-97.636l.046-94.446h97.638l-.048 94.446z"
				fill="#ff7000"
				fillRule="nonzero"
			/>
			<path
				d="M418.938 303.22h-96.064l.045-94.446h96.066l-.047 94.446z"
				fill="#ff7000"
				fillRule="nonzero"
			/>
			<path
				d="M228.423 208.77H132.36l.045-94.445h96.066l-.05 94.446zM418.985 208.77H322.92l.044-94.445h96.069l-.048 94.446z"
				fill="#ffa300"
				fillRule="nonzero"
			/>
			<path
				d="M133.883 304.79H39.392l.044-96.017h94.496l-.049 96.017z"
				fill="#ff7000"
				fillRule="nonzero"
			/>
			<path
				d="M133.929 208.77H39.437l.044-95.445h94.496l-.048 95.445z"
				fill="#ffa300"
				fillRule="nonzero"
			/>
			<path
				d="M133.976 114.325H39.484l.044-94.448h94.497l-.05 94.448zM511.954 115.325h-94.493l.044-95.448h94.497l-.048 95.448z"
				fill="#ffce00"
				fillRule="nonzero"
			/>
			<path
				d="M133.836 399.667H39.345l.044-96.447h94.496l-.049 96.447z"
				fill="#ff4900"
				fillRule="nonzero"
			/>
			<path
				d="M133.79 492.117H39.3l.044-94.448h94.496l-.049 94.448z"
				fill="#ff0107"
				fillRule="nonzero"
			/>
			<path
				d="M511.862 303.22h-94.495l.046-94.446h94.496l-.047 94.446z"
				fill="#ff7000"
				fillRule="nonzero"
			/>
			<path
				d="M511.907 208.77h-94.493l.044-94.445h94.496l-.047 94.446z"
				fill="#ffa300"
				fillRule="nonzero"
			/>
			<path
				d="M511.815 398.666h-94.493l.044-95.447h94.496l-.047 95.447z"
				fill="#ff4900"
				fillRule="nonzero"
			/>
			<path
				d="M511.77 492.117h-94.496l.046-94.448h94.496l-.047 94.448z"
				fill="#ff0107"
				fillRule="nonzero"
			/>
		</svg>
	);
};

export const MistralAILogo: React.FC<IconSvgProps> = ({ size = 24, className = '' }) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			width={size}
			height={size}
			className={className}
			viewBox="0 0 512 164"
			version="1.1"
			preserveAspectRatio="xMidYMid"
		>
			<title>Mistral AI</title>
			<g>
				<g
					transform="translate(213.3167, 27.4293)"
					fill="#FFFFFF"
				>
					<path d="M3.54926427,0.866543136 L14.7228067,0.866543136 L19.3327713,17.5381056 L20.9144735,24.0433432 L21.204218,24.0433432 L22.7664598,17.5381056 L27.3980472,0.866543136 L38.5715896,0.866543136 L38.5715896,47.9106495 L29.5614007,47.9106495 L29.5614007,33.3473459 L29.5670358,32.5879187 C29.6450324,27.0205397 30.5137834,16.3474271 30.9885004,11.494286 L30.7722732,11.494286 L27.1612784,24.1211468 L23.498389,35.4534436 L18.3305581,35.4534436 L14.6038817,24.1211468 L11.2361425,11.494286 L10.9864,11.494286 L11.101213,12.6193443 C11.6427988,18.1141469 12.4134998,28.4113913 12.4134998,33.3473459 L12.4134998,47.9106495 L3.54926427,47.9106495 L3.54926427,0.866543136 Z M48.0762959,38.9834424 L59.0682074,38.9834424 L59.0682074,9.79416023 L48.0762959,9.79416023 L48.0762959,0.86620785 L80.7103914,0.86620785 L80.7103914,9.79416023 L69.7217233,9.79416023 L69.7217233,38.9834424 L80.7103914,38.9834424 L80.7103914,47.9113948 L48.0762959,47.9113948 L48.0762959,38.9834424 Z M89.7022114,41.9836366 L95.8160364,34.6711876 C99.3740556,37.5834161 103.76455,39.5803727 108.328025,39.5803727 C112.848256,39.5803727 114.93593,37.8308744 114.93593,35.5313486 C114.93593,32.1436542 112.137949,31.3245562 107.684749,29.4605139 L101.552545,26.844371 C96.3382252,24.8636234 91.5758204,20.8664682 91.5758204,14.018679 C91.5758204,6.22752234 98.5977998,0 108.826429,0 C114.487258,0 120.20755,2.12230676 124.457496,6.15944427 L119.090736,12.8710773 C115.774891,10.4915867 112.841769,9.19594223 108.242616,9.19594223 C104.73541,9.19594223 102.353667,10.6504356 102.353667,13.3973316 C102.353667,16.2274341 105.508422,17.2637336 110.023247,18.9548793 L115.781378,21.3938031 C121.946017,23.7030544 125.835945,27.5456831 125.835945,34.1849157 C125.835945,42.0052487 119.272367,48.776315 107.620962,48.776315 C101.260638,48.776315 94.6927359,46.4508546 89.7022114,41.9836366 Z M145.739032,9.79417006 L131.628043,9.79417006 L131.628043,0.866217681 L170.501375,0.866217681 L170.501375,9.79417006 L156.392548,9.79417006 L156.392548,47.9114046 L145.739032,47.9114046 L145.739032,9.79417006 Z M188.420134,9.31643532 L193.613913,9.31643532 C199.208792,9.31643532 202.211107,10.8779085 202.211107,15.5234262 C202.211107,20.1289615 199.208792,22.5365478 193.613913,22.5365478 L188.420134,22.5365478 L188.420134,9.31643532 Z M214.913376,47.911297 L204.26851,29.0904126 C209.293631,26.8189825 212.655964,22.4360516 212.655964,15.5234262 C212.655964,4.44245627 204.614496,0.936171221 194.779555,0.867153617 L177.746077,0.866110028 L177.746077,47.911297 L188.420134,47.911297 L188.420134,30.9868731 L194.011771,30.9868731 L202.998175,47.911297 L214.913376,47.911297 Z M232.595264,28.4272429 L233.708834,24.0929392 L237.360912,8.92125547 L237.649576,8.92125547 C238.458111,12.4671812 239.420543,16.2915278 240.339083,19.8992811 L242.513607,28.4272429 L232.595264,28.4272429 Z M247.484672,47.9105377 L258.779301,47.9105377 L244.119095,0.866431332 L231.350876,0.866431332 L216.69067,47.9105377 L227.590685,47.9105377 L230.464345,36.7230418 L244.630472,36.7230418 L247.484672,47.9105377 Z M267.100055,0.866543136 L277.731949,0.866543136 L277.731949,38.9837777 L298.683287,38.9837777 L298.683287,47.9106495 L267.100055,47.9106495 L267.100055,0.866543136 Z" />
					<path d="M15.9208113,103.897061 L17.0343816,99.5627576 L20.6864596,84.3899932 L20.975123,84.3899932 C21.7836584,87.9359189 22.7460902,91.7608169 23.6646304,95.3688852 L25.8391546,103.897061 L15.9208113,103.897061 Z M30.8058942,123.398726 L42.1048484,123.398726 L27.4392362,76.3351691 L14.6656122,76.3351691 L0,123.398726 L10.9043395,123.398726 L13.7801617,112.204747 L27.9506136,112.204747 L30.8058942,123.398726 Z M48.0762959,114.453272 L59.0682074,114.453272 L59.0682074,85.2629094 L48.0762959,85.2629094 L48.0762959,76.334957 L80.7103914,76.334957 L80.7103914,85.2629094 L69.7217233,85.2629094 L69.7217233,114.453272 L80.7103914,114.453272 L80.7103914,123.381225 L48.0762959,123.381225 L48.0762959,114.453272 Z M90.1825178,128.063257 L125.049168,128.063257 L125.049168,136.17178 L90.1825178,136.17178 L90.1825178,128.063257 Z" />
				</g>
				<g>
					<rect
						fill="#FFFFFF"
						x="122.925214"
						y="0"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#F7D046"
						x="138.290866"
						y="0"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#FFFFFF"
						x="0"
						y="0"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#FFFFFF"
						x="0"
						y="30.7313035"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#FFFFFF"
						x="0"
						y="61.462607"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#FFFFFF"
						x="0"
						y="92.1939105"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#FFFFFF"
						x="0"
						y="122.925214"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#F7D046"
						x="15.3656518"
						y="0"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#F2A73B"
						x="138.290866"
						y="30.7313035"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#F2A73B"
						x="15.3656518"
						y="30.7313035"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#FFFFFF"
						x="92.1939105"
						y="30.7313035"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#F2A73B"
						x="107.559562"
						y="30.7313035"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#F2A73B"
						x="46.0969553"
						y="30.7313035"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#EE792F"
						x="76.8282588"
						y="61.462607"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#EE792F"
						x="107.559562"
						y="61.462607"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#EE792F"
						x="46.0969553"
						y="61.462607"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#FFFFFF"
						x="61.462607"
						y="92.1939105"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#EB5829"
						x="76.8282588"
						y="92.1939105"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#EE792F"
						x="138.290866"
						y="61.462607"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#EE792F"
						x="15.3656518"
						y="61.462607"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#FFFFFF"
						x="122.925214"
						y="92.1939105"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#EB5829"
						x="138.290866"
						y="92.1939105"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#FFFFFF"
						x="122.925214"
						y="122.925214"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#EB5829"
						x="15.3656518"
						y="92.1939105"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#EA3326"
						x="138.290866"
						y="122.925214"
						width="30.7313035"
						height="30.7313035"
					/>
					<rect
						fill="#EA3326"
						x="15.3656518"
						y="122.925214"
						width="30.7313035"
						height="30.7313035"
					/>
				</g>
			</g>
		</svg>
	);
};

export const CohereLogo: React.FC<IconSvgProps> = ({ size = 24, className = '' }) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			height={size}
			width={size}
			className={className}
			viewBox="0 0.5 101.9 17.02"
		>
			<path
				fill="#39594D"
				d="M28.706 17.4966c2.5252 0 4.7353-1.2626 5.609-3.8119.1696-.5105-.0737-.8497-.5585-.8497h-.9474c-.4369 0-.7281.1936-.9234.6064-.7521 1.482-1.7971 2.0148-3.1077 2.0148-2.3317 0-3.7639-1.6276-3.7639-4.3464 0-2.71884 1.4819-4.34637 3.7142-4.34637 1.3602 0 2.4773.58248 3.1814 1.96674.2193.41288.4848.60647.9234.60647h.9474c.4848 0 .7281-.31523.5585-.77779-1.0194-2.76851-3.3031-3.83583-5.633-3.83583-3.5206 0-6.1435 2.59891-6.1435 6.38678 0 3.7879 2.5013 6.3868 6.1435 6.3868ZM92.25 9.99279c.3152-2.06439 1.7235-3.351 3.6422-3.351 1.9188 0 3.351 1.31059 3.5206 3.351H92.25Zm3.7399 7.50381c2.234 0 4.4681-1.0434 5.5611-3.399.267-.5585.024-.9474-.461-.9474h-.898c-.4365 0-.7038.1936-.9231.5825-.7281 1.2866-1.9907 1.8211-3.2773 1.8211-2.21 0-3.6422-1.5059-3.8119-3.9575h8.9123c.485 0 .802-.2672.802-.7777-.098-3.8119-2.5737-6.09387-5.9983-6.09387-3.4247 0-6.1435 2.47728-6.1435 6.38677 0 3.9095 2.6468 6.3868 6.2411 6.3868l-.0034-.0017Zm-14.1321-6.0699h.8017c.4849 0 .7521-.2672.8258-.7778.4626-3.28071 2.3831-3.71587 4.4252-3.61821.4368.02055.7949-.31523.7949-.75381v-.75209c0-.48483-.2433-.77779-.7281-.80177-1.8074-.06853-3.4195.55164-4.3515 2.30767-.0514.09593-.1953.07024-.2073-.03769l-.1508-1.3243c-.0479-.48483-.3152-.72811-.8017-.72811h-3.6663c-.4283 0-.7778.34778-.7778.77779v.41288c0 .4283.3478.77779.7778.77779h1.5059c.4283 0 .7778.34778.7778.77779v2.96206c0 .4283.3478.7778.7778.7778h-.0034Zm-3.2774 5.8283h7.5997c.4849 0 .7778-.2912.7778-.7778v-.4129c0-.4848-.2912-.7777-.7778-.7777h-1.9427c-.4848 0-.7778-.2913-.7778-.7778v-1.3363c0-.4849-.2912-.7778-.7778-.7778h-.8257c-.4849 0-.7778.2912-.7778.7778v1.3363c0 .4848-.2913.7778-.7778.7778H78.577c-.4848 0-.7778.2912-.7778.7777v.4129c0 .4849.2913.7778.7778.7778h.0034ZM66.8519 9.99451c.3153-2.0644 1.7235-3.351 3.6423-3.351 1.9187 0 3.351 1.31059 3.5206 3.351h-7.1629Zm3.7399 7.50379c2.234 0 4.468-1.0433 5.561-3.399.2673-.5585.024-.9474-.4608-.9474h-.8977c-.4369 0-.7041.1936-.9234.5825-.7281 1.2866-1.9908 1.8211-3.2774 1.8211-2.21 0-3.6422-1.5059-3.8118-3.9574h8.912c.4848 0 .8018-.2673.8018-.7778-.0977-3.81188-2.5732-6.09386-5.9979-6.09386-3.4247 0-6.1435 2.47728-6.1435 6.38676 0 3.9095 2.6469 6.3868 6.2412 6.3868l-.0035-.0017Zm-28.0929 0c3.6423 0 6.2412-2.6949 6.2412-6.3868 0-3.69192-2.5989-6.38677-6.2412-6.38677-3.6422 0-6.2411 2.74453-6.2411 6.38677 0 .8498.1456 1.7971.5824 2.8405.2193.5105.6322.5825 1.0691.2672l.7041-.5105c.3649-.2673.4608-.5825.3392-1.0433-.1936-.6065-.2433-1.141-.2433-1.6019 0-2.54919 1.5299-4.29836 3.7879-4.29836s3.7879 1.72347 3.7879 4.34636c0 2.6229-1.5059 4.3464-3.7399 4.3464-.7778 0-1.5059-.1456-2.3797-.8018-.3649-.2912-.7041-.3392-1.093-.048l-.5345.3889c-.4369.3153-.4848.7521-.0737 1.093 1.2627 1.0194 2.7189 1.4083 4.0312 1.4083h.0034Zm9.3232-.2433h.8018c.4283 0 .7778-.3478.7778-.7778v-5.8026c0-2.45153 1.3105-3.90946 3.351-3.90946 1.8451 0 2.9141 1.21465 2.9141 3.44866v6.2651c0 .4283.3478.7778.7778.7778h.8257c.4283 0 .7778-.3478.7778-.7778v-6.654c0-3.27733-1.6755-5.09846-4.5159-5.09846-1.9342 0-3.0769.7915-3.9301 1.89308-.0651.08395-.197.03769-.197-.06681V1.27779C53.3999.847778 53.0522.5 52.6239.5h-.8018c-.4283 0-.7778.347778-.7778.77779V16.4772c0 .4283.3478.7778.7778.7778Z"
			/>
			<g clipPath="url(#a)">
				<path
					fill="#39594D"
					fillRule="evenodd"
					d="M5.50773 10.6219c.45757 0 1.36777-.0251 2.62589-.5431 1.46611-.60362 4.38298-1.69938 6.48718-2.8249 1.4716-.78722 2.1167-1.82837 2.1167-3.23046C16.7375 2.07751 15.16.5 13.2141.5H5.06095C2.26586.5 0 2.76586 0 5.56095s2.12151 5.06095 5.50773 5.06095Z"
					clipRule="evenodd"
				/>
				<path
					fill="#D18EE2"
					fillRule="evenodd"
					d="M6.88672 14.107c0-1.3701.82483-2.6054 2.09027-3.1306l2.56761-1.0656c2.5971-1.07786 5.4557.8307 5.4557 3.6427 0 2.1785-1.7664 3.9444-3.945 3.9438l-2.7799-.0007c-1.87168-.0005-3.38868-1.5179-3.38868-3.3896Z"
					clipRule="evenodd"
				/>
				<path
					fill="#FF7759"
					d="M2.91749 11.2891h-.00005C1.30618 11.2891 0 12.5952 0 14.2065v.3779c0 1.6112 1.30618 2.9174 2.91744 2.9174h.00005c1.61126 0 2.91744-1.3062 2.91744-2.9174v-.3779c0-1.6113-1.30618-2.9174-2.91744-2.9174Z"
				/>
			</g>
			<defs>
				<clipPath id="a">
					<path
						fill="#fff"
						d="M0 .5h17v17H0z"
					/>
				</clipPath>
			</defs>
		</svg>
	);
};

export const OpenAILogo: React.FC<IconSvgProps> = ({
	size = 24,
	fill = 'white',
	className = '',
}) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			width={size}
			height={size}
			fill={fill}
			className={className}
			viewBox="0 0 512 126"
			version="1.1"
			preserveAspectRatio="xMidYMid"
		>
			<title>OpenAI</title>
			<g fill={fill}>
				<path d="M365.131428,49.0741817 C357.593526,49.0741817 352.214006,51.6489012 349.573549,56.5244338 L348.154714,59.1648908 L348.154714,50.3451071 L335.889189,50.3451071 L335.889189,103.954053 L348.790177,103.954053 L348.790177,72.0604006 C348.790177,64.4403266 352.93164,60.0687816 360.146333,60.0687816 C367.02686,60.0687816 370.971111,64.3252859 370.971111,71.7426692 L370.971111,103.954053 L383.877577,103.954053 L383.877577,69.4418561 C383.877577,56.6778213 376.871053,49.0741817 365.131428,49.0741817 Z M302.565745,49.0741817 C287.342032,49.0741817 277.914176,58.5732531 277.914176,73.8627042 L277.914176,81.38965 C277.914176,96.0929416 287.451594,105.224978 302.806783,105.224978 C313.07827,105.224978 320.276528,101.461506 324.806939,93.7209128 L316.808874,89.1192865 C313.461739,93.5839596 308.115087,96.3504135 302.812261,96.3504135 C295.038799,96.3504135 290.398826,91.5515747 290.398826,83.509685 L290.398826,81.3786938 L326.406552,81.3786938 L326.406552,72.4876944 C326.406552,58.2445655 317.05539,49.0741817 302.576701,49.0741817 L302.565745,49.0741817 Z M314.666927,72.7122976 L290.355001,72.7122976 L290.355001,71.4249379 C290.355001,62.5996761 294.688199,57.7296216 302.554789,57.7296216 C310.131038,57.7296216 314.65597,62.5284604 314.65597,70.5703502 L314.666927,72.7122976 Z M512,41.5198452 L512,31.2647924 L467.375181,31.2647924 L467.375181,41.5198452 L483.020711,41.5198452 L483.020711,93.6770878 L467.375181,93.6770878 L467.375181,103.932141 L512,103.932141 L512,93.6770878 L496.348993,93.6770878 L496.348993,41.5198452 L512,41.5198452 Z M173.638038,29.7856983 C153.708613,29.7856983 141.317091,42.2046111 141.317091,62.205251 L141.317091,73.0190727 C141.317091,93.0142345 153.703135,105.438625 173.638038,105.438625 C193.57294,105.438625 205.958984,93.0142345 205.958984,73.0190727 L205.958984,62.205251 C205.953506,42.1826986 193.551028,29.7856983 173.638038,29.7856983 Z M192.625224,73.7586198 C192.625224,87.0375985 185.70635,94.6521943 173.638038,94.6521943 C161.569725,94.6521943 154.656329,87.0375985 154.656329,73.7586198 L154.656329,61.4602258 C154.656329,48.1812471 161.580681,40.5666512 173.643516,40.5666512 C185.70635,40.5666512 192.630702,48.1812471 192.630702,61.4602258 L192.625224,73.7586198 Z M246.480686,49.0741817 C239.709722,49.0741817 233.848126,51.8789825 230.791332,56.5737369 L229.405366,58.7102063 L229.405366,50.3451071 L217.134362,50.3451071 L217.134362,122.398905 L230.040828,122.398905 L230.040828,96.3011104 L231.421316,98.3499297 C234.324723,102.655737 239.994584,105.224978 246.590248,105.224978 C257.716323,105.224978 268.941005,97.9555046 268.941005,81.7073814 L268.941005,72.5917788 C268.941005,60.8850225 262.022131,49.0741817 246.480686,49.0741817 L246.480686,49.0741817 Z M256.034539,81.0773968 C256.034539,89.7164023 250.994662,95.0849663 242.887035,95.0849663 C235.32722,95.0849663 230.051785,89.4096272 230.051785,81.2910437 L230.051785,73.2272415 C230.051785,65.0100517 235.371045,59.2251501 242.996597,59.2251501 C251.043965,59.2251501 256.045495,64.588236 256.045495,73.2272415 L256.034539,81.0773968 Z M419.540181,31.2702705 L393.502645,103.954053 L406.611802,103.954053 L411.596897,88.3742613 L441.529381,88.3742613 L441.578684,88.5276489 L446.508998,103.954053 L459.612676,103.954053 L433.531316,31.2647924 L419.540181,31.2702705 Z M414.796123,78.1246866 L426.541226,41.377414 L438.165811,78.1246866 L414.796123,78.1246866 Z" />
				<path d="M116.08484,51.5612512 C118.942613,42.9827523 117.960247,33.5883781 113.389602,25.7866659 C106.508682,13.8288345 92.6973243,7.67791081 79.2060928,10.5629523 C73.2060618,3.79625563 64.5786473,-0.0532364603 55.5351081,0.000556328614 C41.7355849,-0.0302926402 29.4968898,8.85809568 25.2575029,21.9903242 C16.3994728,23.8073685 8.75503049,29.3555286 4.28175645,37.2140378 C-2.63538569,49.152298 -1.05721438,64.1903007 8.18766065,74.4324294 C5.34111759,83.0030169 6.32316165,92.3843321 10.8828989,100.179624 C17.7575638,112.161259 31.5861367,118.328205 45.093799,115.436206 C51.0883814,122.194518 59.7036911,126.043062 68.737393,125.998425 C82.5369162,126.029451 94.7756114,117.141063 99.0149983,104.008834 C107.871293,102.191014 115.513744,96.6428507 119.985267,88.7851208 C126.920873,76.8497637 125.343882,61.7996855 116.08484,51.5612512 Z M68.737393,117.780853 C63.2141366,117.785642 57.8654034,115.846289 53.6287201,112.302718 C53.8149764,112.198634 54.1765327,112.017855 54.3847016,111.880902 L79.474521,97.3967357 C80.7562304,96.6686637 81.5441301,95.3045077 81.5342966,93.8304753 L81.5342966,58.4527343 L92.1344714,64.5718016 C92.2510065,64.6269956 92.3300309,64.7392935 92.3426402,64.8676205 L92.3426402,94.1482067 C92.3426402,107.189463 81.7786405,117.765729 68.737393,117.780853 L68.737393,117.780853 Z M18.0154196,96.1203322 C15.2495784,91.3404624 14.2510197,85.7407779 15.1941845,80.2995028 C15.3804408,80.4145435 15.7091284,80.6117561 15.9392097,80.7432311 L41.0345072,95.2328757 C42.3066308,95.9779237 43.8819348,95.9779237 45.1540584,95.2328757 L75.7713075,77.5275708 L75.7713075,89.7657055 C75.7828997,89.8935989 75.7268067,90.0182501 75.6233981,90.0943931 L50.2596723,104.731947 C38.9678692,111.233586 24.5441209,107.369119 18.0154196,96.0929416 L18.0154196,96.1203322 Z M11.4416678,41.3390671 C14.1945376,36.5519884 18.5445232,32.8865971 23.7291056,30.985408 L23.7291056,60.8083287 C23.7215951,62.2817457 24.5088656,63.6448123 25.7888811,64.3745891 L56.4116084,82.0579815 L45.7730867,88.1989613 C45.6677553,88.2698947 45.5335448,88.2822834 45.4170084,88.23183 L20.0587608,73.5887979 C8.77671207,67.0619249 4.91115861,52.6316692 11.4197553,41.3390671 L11.4416678,41.3390671 Z M98.5438794,61.6081352 L67.9211522,43.9247428 L78.5103707,37.8001973 C78.6160478,37.73024 78.7497514,37.7178982 78.8664489,37.7673286 L104.224697,52.4103608 C112.153676,56.9917322 116.718476,65.7447168 115.937024,74.8686966 C115.155572,83.9926763 109.16882,91.84194 100.576264,95.0082725 L100.576264,65.1908299 C100.593141,63.7135298 99.8092479,62.3427655 98.527445,61.6081352 L98.5438794,61.6081352 Z M109.100229,45.7215683 C108.913973,45.6065276 108.585285,45.4093151 108.355204,45.27784 L83.2599064,30.7881954 C81.9877828,30.0431474 80.4124789,30.0431474 79.1403553,30.7881954 L48.5173955,48.4661097 L48.5173955,36.2334532 C48.5132731,36.1056698 48.5702821,35.9835077 48.6710156,35.9047656 L74.0292632,21.2672115 C81.9611862,16.694978 91.8213323,17.1204093 99.3299585,22.3588493 C106.838585,27.5972893 110.641969,36.7043054 109.089273,45.7270464 L109.100229,45.7215683 Z M42.7382046,67.5464243 L32.1325516,61.427357 C32.0220863,61.3662676 31.9469493,61.2566082 31.9298541,61.1315382 L31.9298541,31.8509519 C31.9229009,22.6891727 37.2178921,14.3508632 45.5129032,10.4609995 C53.8079142,6.57113579 63.604525,7.83238731 70.6437811,13.6964407 C70.4575247,13.8005251 70.123359,13.9813033 69.8877996,14.1182564 L44.7979802,28.6024229 C43.5179646,29.3321997 42.7306941,30.6952663 42.7382046,32.1686833 L42.7382046,67.5464243 Z M48.4957155,55.1275115 L62.1362506,47.2499656 L75.7713075,55.1275115 L75.7713075,70.8716471 L62.1307724,78.749193 L48.4902374,70.8716471 L48.4957155,55.1275115 Z" />
			</g>
		</svg>
	);
};

export const GeminiIcon: React.FC<IconSvgProps> = ({ size = 24, className = '' }) => {
	return (
		<svg
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			viewBox="0 0 16 16"
			height={size}
			width={size}
			className={className}
		>
			<path
				d="M16 8.016A8.522 8.522 0 008.016 16h-.032A8.521 8.521 0 000 8.016v-.032A8.521 8.521 0 007.984 0h.032A8.522 8.522 0 0016 7.984v.032z"
				fill="url(#prefix__paint0_radial_980_20147)"
			/>
			<defs>
				<radialGradient
					id="prefix__paint0_radial_980_20147"
					cx="0"
					cy="0"
					r="1"
					gradientUnits="userSpaceOnUse"
					gradientTransform="matrix(16.1326 5.4553 -43.70045 129.2322 1.588 6.503)"
				>
					<stop
						offset=".067"
						stopColor="#9168C0"
					/>
					<stop
						offset=".343"
						stopColor="#5684D1"
					/>
					<stop
						offset=".672"
						stopColor="#1BA1E3"
					/>
				</radialGradient>
			</defs>
		</svg>
	);
};

export const MicrosoftLogo: React.FC<IconSvgProps> = ({ size = 24 }) => {
	return (
		<svg
			x="0px"
			y="0px"
			viewBox="0 0 129 129"
			width={size}
			height={size}
		>
			<style type="text/css">
				{`
                    .st0{fill:#F25022;}
                    .st1{fill:#7FBA00;}
                    .st2{fill:#00A4EF;}
                    .st3{fill:#FFB900;}
                `}
			</style>
			<path
				className="st0"
				d="M0,0h61.3v61.3H0V0z"
			/>
			<path
				className="st1"
				d="M67.7,0H129v61.3H67.7V0z"
			/>
			<path
				className="st2"
				d="M0,67.7h61.3V129H0V67.7z"
			/>
			<path
				className="st3"
				d="M67.7,67.7H129V129H67.7V67.7z"
			/>
		</svg>
	);
};

export const GoogleColorIcon: React.FC<IconSvgProps> = ({ size = 24, className = '' }) => {
	return (
		<svg
			height={size}
			width={size}
			className={className}
			viewBox="0 0 326667 333333"
			shapeRendering="geometricPrecision"
			textRendering="geometricPrecision"
			imageRendering="optimizeQuality"
			fillRule="evenodd"
			clipRule="evenodd"
		>
			<path
				d="M326667 170370c0-13704-1112-23704-3518-34074H166667v61851h91851c-1851 15371-11851 38519-34074 54074l-311 2071 49476 38329 3428 342c31481-29074 49630-71852 49630-122593m0 0z"
				fill="#4285f4"
			/>
			<path
				d="M166667 333333c44999 0 82776-14815 110370-40370l-52593-40742c-14074 9815-32963 16667-57777 16667-44074 0-81481-29073-94816-69258l-1954 166-51447 39815-673 1870c27407 54444 83704 91852 148890 91852z"
				fill="#34a853"
			/>
			<path
				d="M71851 199630c-3518-10370-5555-21482-5555-32963 0-11482 2036-22593 5370-32963l-93-2209-52091-40455-1704 811C6482 114444 1 139814 1 166666s6482 52221 17777 74814l54074-41851m0 0z"
				fill="#fbbc04"
			/>
			<path
				d="M166667 64444c31296 0 52406 13519 64444 24816l47037-45926C249260 16482 211666 1 166667 1 101481 1 45185 37408 17777 91852l53889 41853c13520-40185 50927-69260 95001-69260m0 0z"
				fill="#ea4335"
			/>
		</svg>
	);
};

export const GithubColorIcon: React.FC<IconSvgProps> = ({ size = 24 }) => {
	return (
		<svg
			viewBox="0 0 20 20"
			version="1.1"
			xmlns="http://www.w3.org/2000/svg"
			xmlnsXlink="http://www.w3.org/1999/xlink"
			fill="#ffffff"
			height={size}
			width={size}
		>
			<g
				id="SVGRepo_bgCarrier"
				strokeWidth="0"
			></g>
			<g
				id="SVGRepo_tracerCarrier"
				strokeLinecap="round"
				strokeLinejoin="round"
			></g>
			<g id="SVGRepo_iconCarrier">
				<title>github</title>
				<desc>Created with Sketch.</desc>
				<defs></defs>
				<g
					stroke="none"
					strokeWidth="1"
					fill="none"
					fillRule="evenodd"
				>
					<g
						transform="translate(-140.000000, -7559.000000)"
						fill="#ffffff"
					>
						<g transform="translate(56.000000, 160.000000)">
							<path d="M94,7399 C99.523,7399 104,7403.59 104,7409.253 C104,7413.782 101.138,7417.624 97.167,7418.981 C96.66,7419.082 96.48,7418.762 96.48,7418.489 C96.48,7418.151 96.492,7417.047 96.492,7415.675 C96.492,7414.719 96.172,7414.095 95.813,7413.777 C98.04,7413.523 100.38,7412.656 100.38,7408.718 C100.38,7407.598 99.992,7406.684 99.35,7405.966 C99.454,7405.707 99.797,7404.664 99.252,7403.252 C99.252,7403.252 98.414,7402.977 96.505,7404.303 C95.706,7404.076 94.85,7403.962 94,7403.958 C93.15,7403.962 92.295,7404.076 91.497,7404.303 C89.586,7402.977 88.746,7403.252 88.746,7403.252 C88.203,7404.664 88.546,7405.707 88.649,7405.966 C88.01,7406.684 87.619,7407.598 87.619,7408.718 C87.619,7412.646 89.954,7413.526 92.175,7413.785 C91.889,7414.041 91.63,7414.493 91.54,7415.156 C90.97,7415.418 89.522,7415.871 88.63,7414.304 C88.63,7414.304 88.101,7413.319 87.097,7413.247 C87.097,7413.247 86.122,7413.234 87.029,7413.87 C87.029,7413.87 87.684,7414.185 88.139,7415.37 C88.139,7415.37 88.726,7417.2 91.508,7416.58 C91.513,7417.437 91.522,7418.245 91.522,7418.489 C91.522,7418.76 91.338,7419.077 90.839,7418.982 C86.865,7417.627 84,7413.783 84,7409.253 C84,7403.59 88.478,7399 94,7399"></path>
						</g>
					</g>
				</g>
			</g>
		</svg>
	);
};

export const AnthropicLogo: React.FC<IconSvgProps> = ({
	size = 24,
	fill = 'white',
	className = '',
}) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			width={size}
			height={size}
			className={className}
			viewBox="0 0 1024.2 115"
			fill={fill}
		>
			<path d="M250.649,23.3494h37.328V113.094h23.825V23.3494H349.13V1.9061H250.649Z" />
			<path d="M208.557,79.7376,158.522,1.9061H131.519V113.094h23.032V35.2624l50.035,77.8316h27.002V1.9061H208.557Z" />
			<path d="M444.434,46.2224H392.017V1.9061H368.191V113.094h23.826V67.6657h52.417V113.094H468.26V1.9061H444.434Z" />
			<path d="M44.3163,1.9061,0,113.094H24.779l9.0634-23.3495H80.2061L89.268,113.094h24.779L69.7307,1.9061ZM41.8575,69.0953l15.166-39.0746L72.19,69.0953Z" />
			<path d="M665.698,0c-32.086,0-54.8,23.826-54.8,57.6588,0,33.5152,22.714,57.3412,54.8,57.3412,31.926,0,54.482-23.826,54.482-57.3412C720.18,23.826,697.624,0,665.698,0Zm0,92.7624c-18.744,0-30.18-13.3425-30.18-35.1036,0-22.0787,11.436-35.4212,30.18-35.4212,18.584,0,29.861,13.3425,29.861,35.4212C695.559,79.42,684.282,92.7624,665.698,92.7624Z" />
			<path d="M998.785,75.7666c-4.13,10.8011-12.39,16.9958-23.668,16.9958-18.743,0-30.179-13.3425-30.179-35.1036,0-22.0787,11.436-35.4212,30.179-35.4212,11.278,0,19.538,6.1947,23.668,16.9958h25.255C1017.85,15.4075,999.261,0,975.117,0c-32.085,0-54.799,23.826-54.799,57.6588C920.318,91.174,943.032,115,975.117,115,999.42,115,1018,99.4337,1024.2,75.7666Z" />
			<path d="M846.934,1.9061,891.25,113.094h24.302L871.236,1.9061Z" />
			<path d="M796.74,1.9061H742.417V113.094h23.826V72.7486H796.74c25.256,0,40.663-13.3425,40.663-35.4213S821.996,1.9061,796.74,1.9061Zm-1.112,49.3991H766.243V23.3494h29.385c11.755,0,17.949,4.7652,17.949,13.9779S807.383,51.3052,795.628,51.3052Z" />
			<path d="M592.631,35.7389c0-20.9668-15.407-33.8328-40.663-33.8328H497.645V113.094h23.826V69.5718h26.525l23.827,43.5222h26.368L571.807,66.2568C585.05,61.166,592.631,50.4459,592.631,35.7389Zm-71.16-12.39h29.385c11.754,0,17.949,4.2887,17.949,12.39s-6.195,12.39-17.949,12.39H521.471Z" />
		</svg>
	);
};

export const MoonFilledIcon = ({ size = 24, width, height, ...props }: IconSvgProps) => (
	<svg
		aria-hidden="true"
		focusable="false"
		height={size || height}
		role="presentation"
		viewBox="0 0 24 24"
		width={size || width}
		{...props}
	>
		<path
			d="M21.53 15.93c-.16-.27-.61-.69-1.73-.49a8.46 8.46 0 01-1.88.13 8.409 8.409 0 01-5.91-2.82 8.068 8.068 0 01-1.44-8.66c.44-1.01.13-1.54-.09-1.76s-.77-.55-1.83-.11a10.318 10.318 0 00-6.32 10.21 10.475 10.475 0 007.04 8.99 10 10 0 002.89.55c.16.01.32.02.48.02a10.5 10.5 0 008.47-4.27c.67-.93.49-1.519.32-1.79z"
			fill="currentColor"
		/>
	</svg>
);

export const AudioSpeechIcon: React.FC<IconSvgProps> = ({
	height = 24,
	width = 48,
	fill = 'white',
	className = '',
}) => {
	return (
		<svg
			x="0px"
			y="0px"
			viewBox="0 0 122.88 61.34"
			height={height}
			width={width}
			fill={fill}
			className={className}
		>
			<g>
				<path d="M49.05,15.88c0-1.42,1.15-2.57,2.57-2.57s2.57,1.15,2.57,2.57v29.6c0,1.42-1.15,2.57-2.57,2.57s-2.57-1.15-2.57-2.57V15.88 L49.05,15.88L49.05,15.88z M73.83,15.88c0-1.42-1.15-2.57-2.57-2.57c-1.42,0-2.57,1.15-2.57,2.57v29.6c0,1.42,1.15,2.57,2.57,2.57 c1.42,0,2.57-1.15,2.57-2.57V15.88L73.83,15.88L73.83,15.88z M122.88,9.46c0-1.42-1.14-2.56-2.53-2.56c-1.4,0-2.53,1.14-2.53,2.56 v42.43c0,1.42,1.14,2.56,2.53,2.56c1.4,0,2.53-1.14,2.53-2.56V9.46L122.88,9.46L122.88,9.46z M113.11,2.57 c0-1.42-1.15-2.57-2.57-2.57s-2.57,1.15-2.57,2.57v56.2c0,1.42,1.15,2.57,2.57,2.57s2.57-1.15,2.57-2.57V2.57L113.11,2.57 L113.11,2.57z M83.65,21.66c0-1.42-1.15-2.57-2.57-2.57c-1.42,0-2.57,1.15-2.57,2.57v18.02c0,1.42,1.15,2.57,2.57,2.57 c1.42,0,2.57-1.15,2.57-2.57V21.66L83.65,21.66L83.65,21.66z M93.46,15.88c0-1.42-1.15-2.57-2.57-2.57c-1.42,0-2.57,1.15-2.57,2.57 v29.6c0,1.42,1.15,2.57,2.57,2.57c1.42,0,2.57-1.15,2.57-2.57V15.88L93.46,15.88L93.46,15.88z M103.25,9.46 c0-1.42-1.14-2.56-2.53-2.56c-1.4,0-2.53,1.14-2.53,2.56v42.43c0,1.42,1.14,2.56,2.53,2.56c1.4,0,2.53-1.14,2.53-2.56V9.46 L103.25,9.46L103.25,9.46z M0,9.46C0,8.05,1.14,6.9,2.53,6.9c1.4,0,2.53,1.14,2.53,2.56v42.43c0,1.42-1.14,2.56-2.53,2.56 C1.13,54.45,0,53.3,0,51.89V9.46L0,9.46L0,9.46z M9.78,2.57C9.78,1.15,10.93,0,12.35,0c1.42,0,2.57,1.15,2.57,2.57v56.2 c0,1.42-1.15,2.57-2.57,2.57c-1.42,0-2.57-1.15-2.57-2.57V2.57L9.78,2.57L9.78,2.57z M39.23,21.66c0-1.42,1.15-2.57,2.57-2.57 c1.42,0,2.57,1.15,2.57,2.57v18.02c0,1.42-1.15,2.57-2.57,2.57c-1.42,0-2.57-1.15-2.57-2.57V21.66L39.23,21.66L39.23,21.66z M29.42,15.88c0-1.42,1.15-2.57,2.57-2.57c1.42,0,2.57,1.15,2.57,2.57v29.6c0,1.42-1.15,2.57-2.57,2.57 c-1.42,0-2.57-1.15-2.57-2.57V15.88L29.42,15.88L29.42,15.88z M19.63,9.46c0-1.42,1.14-2.56,2.53-2.56c1.4,0,2.53,1.14,2.53,2.56 v42.43c0,1.42-1.14,2.56-2.53,2.56c-1.4,0-2.53-1.14-2.53-2.56V9.46L19.63,9.46L19.63,9.46z M58.9,9.46c0-1.42,1.14-2.56,2.53-2.56 c1.4,0,2.53,1.14,2.53,2.56v42.43c0,1.42-1.14,2.56-2.53,2.56c-1.4,0-2.53-1.14-2.53-2.56V9.46L58.9,9.46L58.9,9.46z" />
			</g>
		</svg>
	);
};

export const SunFilledIcon = ({ size = 24, width, height, ...props }: IconSvgProps) => (
	<svg
		aria-hidden="true"
		focusable="false"
		height={size || height}
		role="presentation"
		viewBox="0 0 24 24"
		width={size || width}
		{...props}
	>
		<g fill="currentColor">
			<path d="M19 12a7 7 0 11-7-7 7 7 0 017 7z" />
			<path d="M12 22.96a.969.969 0 01-1-.96v-.08a1 1 0 012 0 1.038 1.038 0 01-1 1.04zm7.14-2.82a1.024 1.024 0 01-.71-.29l-.13-.13a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.984.984 0 01-.7.29zm-14.28 0a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a1 1 0 01-.7.29zM22 13h-.08a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zM2.08 13H2a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zm16.93-7.01a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a.984.984 0 01-.7.29zm-14.02 0a1.024 1.024 0 01-.71-.29l-.13-.14a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.97.97 0 01-.7.3zM12 3.04a.969.969 0 01-1-.96V2a1 1 0 012 0 1.038 1.038 0 01-1 1.04z" />
		</g>
	</svg>
);

export const HeartFilledIcon = ({ size = 24, width, height, ...props }: IconSvgProps) => (
	<svg
		aria-hidden="true"
		focusable="false"
		height={size || height}
		role="presentation"
		viewBox="0 0 24 24"
		width={size || width}
		{...props}
	>
		<path
			d="M12.62 20.81c-.34.12-.9.12-1.24 0C8.48 19.82 2 15.69 2 8.69 2 5.6 4.49 3.1 7.56 3.1c1.82 0 3.43.88 4.44 2.24a5.53 5.53 0 0 1 4.44-2.24C19.51 3.1 22 5.6 22 8.69c0 7-6.48 11.13-9.38 12.12Z"
			fill="currentColor"
			strokeLinecap="round"
			strokeLinejoin="round"
			strokeWidth={1.5}
		/>
	</svg>
);

export const DashboardIcon: React.FC<IconSvgProps> = ({
	size = 24,
	fill = 'white',
	className = '',
}) => {
	return (
		<svg
			x="0px"
			y="0px"
			viewBox="0 0 122.9 85.6"
			width={size}
			height={size}
			fill={fill}
			className={className}
		>
			<path d="M7.5,0h107.9c4.1,0,7.5,3.4,7.5,7.5v70.6c0,4.1-3.4,7.5-7.5,7.5H7.5c-4.1,0-7.5-3.4-7.5-7.5V7.5 C0,3.4,3.4,0,7.5,0L7.5,0z M69.9,63.3h28.5v4H69.9V63.3L69.9,63.3z M69.9,53.1H109v4H69.9V53.1L69.9,53.1z M92.1,35h5.6 c0.3,0,0.5,0.2,0.5,0.5v11c0,0.3-0.2,0.5-0.5,0.5h-5.6c-0.3,0-0.5-0.2-0.5-0.5v-11C91.6,35.3,91.8,35,92.1,35L92.1,35L92.1,35z M70.5,28.3h5.6c0.3,0,0.5,0.2,0.5,0.5v17.8c0,0.3-0.2,0.5-0.5,0.5h-5.6c-0.3,0-0.5-0.2-0.5-0.5V28.8 C69.9,28.5,70.2,28.3,70.5,28.3L70.5,28.3L70.5,28.3L70.5,28.3z M81.3,24.5h5.6c0.3,0,0.5,0.2,0.5,0.5v21.6c0,0.3-0.2,0.5-0.5,0.5 h-5.6c-0.3,0-0.5-0.2-0.5-0.5V25C80.8,24.7,81,24.5,81.3,24.5L81.3,24.5L81.3,24.5z M39.3,48.2l17,0.3c0,6.1-3,11.7-8,15.1 L39.3,48.2L39.3,48.2L39.3,48.2z M37.6,45.3l-0.2-19.8l0-1.3l1.3,0.1h0h0c1.6,0.1,3.2,0.4,4.7,0.8c1.5,0.4,2.9,1,4.3,1.7 c6.9,3.6,11.7,10.8,12.1,19l0.1,1.3l-1.3,0l-19.7-0.6l-1.1,0L37.6,45.3L37.6,45.3L37.6,45.3z M39.8,26.7L40,44.1l17.3,0.5 c-0.7-6.8-4.9-12.7-10.7-15.8c-1.2-0.6-2.5-1.1-3.8-1.5C41.7,27.1,40.8,26.9,39.8,26.7L39.8,26.7L39.8,26.7z M35.9,47.2L45.6,64 c-3,1.7-6.3,2.6-9.7,2.6c-10.7,0-19.4-8.7-19.4-19.4c0-10.4,8.2-19,18.6-19.4L35.9,47.2L35.9,47.2L35.9,47.2z M115.6,14.1H7.2v64.4 h108.4V14.1L115.6,14.1L115.6,14.1z" />
		</svg>
	);
};

export const AffiliateProgramIcon: React.FC<IconSvgProps> = ({ size = 24, fill = 'white' }) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			width={size}
			height={size}
			fill={fill}
			viewBox="0 0 640 640"
		>
			<path
				fill={fill}
				d="M244.633 367.493c5.17329,4.74809 7.99616,11.1025 8.43317,17.6459 0.437013,6.62607 -1.61813,13.4411 -6.20086,18.9333 4.41738,-3.51973 9.77965,-5.33865 15.2128,-5.49219 6.50795,-0.188979 13.134,2.01971 18.3191,6.53158 0.070867,0.0590558 0.30709,0.283468 0.685048,0.637803l-4.35832 4.67722 4.38194 -4.68903c0.188979,0.177168 0.354335,0.366146 0.519691,0.555125 4.93707,4.87801 7.46466,11.197 7.67726,17.6459 0.20079,5.84653 -1.51183,11.7757 -5.06699,16.7955 3.09453,-1.60632 6.49614,-2.50397 9.93319,-2.66932 6.03551,-0.30709 12.2482,1.61813 17.1734,5.72842 0.248035,0.20079 0.472447,0.40158 0.673237,0.578747 5.03156,4.50006 7.68907,10.5474 8.03159,16.76 0.342524,6.2363 -1.65356,12.6498 -5.96464,17.8112l-11.5868 13.9017c2.24412,1.35828 4.51187,2.752 6.79142,4.15753 23.6105,14.5514 50.0439,30.839 69.8158,7.21662l-47.6226 -35.8823 11.5749 -15.3427 51.2014 38.5871 4.59454 -1.07482 0.90946 -0.153545c6.48433,-0.874027 11.445,-3.33075 15.0238,-7.22844 2.91736,-3.18902 5.11424,-7.47647 6.63788,-12.7797l-64.737 -43.6423 10.76 -15.9451 67.6544 45.6147c7.97254,-2.05514 14.2206,-5.00794 18.7443,-8.85838 3.37799,-2.87011 5.84653,-6.3426 7.41741,-10.4293l-81.3435 -58.1936 11.1261 -15.638 84.8396 60.6976c5.31503,-0.0944894 9.87414,-1.67719 13.5238,-4.2284 3.25988,-2.27956 5.90558,-5.37408 7.81899,-8.92924 1.94884,-3.61422 3.11815,-7.66545 3.40162,-11.7875 0.389769,-5.69298 -0.897649,-11.445 -4.15753,-16.264 -1.16931,-0.425202 -2.26774,-1.08663 -3.21264,-1.96065 -27.461,-22.7483 -54.3432,-43.1934 -82.3947,-60.8984 -27.6145,-17.4097 -56.5164,-32.2917 -88.4775,-44.2683 -4.98431,0.165356 -9.29539,0.129923 -13.0277,0.0944894 -12.319,-0.118112 -15.1892,-0.141734 -24.3546,17.8231 -6.82686,13.3584 -15.7679,23.3625 -27.1893,28.8547 -10.8308,5.20873 -23.4334,6.18905 -38.0202,1.94884 -21.5908,-6.27173 -26.646,-11.4332 -22.8428,-24.5082 1.03938,-3.5906 3.00004,-7.09851 5.57487,-11.7049 0.673237,-1.20474 1.39372,-2.51578 3.1772,-5.85834 11.1852,-21.0593 20.953,-38.0202 30.5082,-51.603 -9.96863,-1.14568 -17.9884,-2.76381 -25.8546,-5.16148 -8.70483,-2.6457 -16.76,-6.09456 -26.7169,-10.8308l-68.5166 123.344 27.0594 27.9925 10.2993 -11.882c4.70085,-5.40952 11.1261,-8.55129 17.764,-9.17728 6.62607,-0.625992 13.4529,1.22836 18.9687,5.79928 0.106301,0.0826782 0.0472447,0.0472447 0.448824,0.40158 5.25597,4.55911 8.31506,10.8545 8.95287,17.3506 0.448824,4.52368 -0.271657,9.15366 -2.23231,13.3584l10.4647 -12.26 -0.0118112 -0.0118112c0.0590558,-0.070867 0.129923,-0.141734 0.188979,-0.212601l0 -0.0118112c0.141734,-0.165356 0.295279,-0.318902 0.460636,-0.472447 4.70085,-5.11424 11.0316,-7.90167 17.516,-8.31506 6.64969,-0.425202 13.4884,1.64175 18.9097,6.27173l0.0118112 -0.0118112c0.0826782,0.070867 0.153545,0.141734 0.224412,0.20079l0 0c0.177168,0.141734 0.330713,0.295279 0.484258,0.460636zm45.4376 140.258l-11.9765 14.3624c-0.188979,0.224412 -0.366146,0.437013 -0.543314,0.625992 -4.37013,4.88982 -10.3702,7.47647 -16.512,7.78356 -6.0237,0.295279 -12.2364,-1.61813 -17.1734,-5.74023 -0.236223,-0.20079 -0.460636,-0.389769 -0.661425,-0.566936 -5.03156,-4.51187 -7.70088,-10.5474 -8.04341,-16.7719 -0.259846,-4.72447 0.826782,-9.55524 3.25988,-13.8663l-1.27561 1.45277c-0.153545,0.188979 -0.0354335,0.0236223 -0.614181,0.649614 -4.80715,5.12605 -11.2797,7.75994 -17.8585,7.93711 -6.50795,0.188979 -13.1458,-2.01971 -18.3309,-6.53158l0.0118112 -0.0118112 0 0 -0.0118112 0.0118112c-0.129923,-0.118112 -0.259846,-0.236223 -0.389769,-0.366146 -0.118112,-0.106301 -0.212601,-0.20079 -0.30709,-0.283468l-0.0118112 0.0118112c-0.177168,-0.165356 -0.354335,-0.354335 -0.50788,-0.543314 -4.93707,-4.87801 -7.47647,-11.197 -7.68907,-17.6459 -0.141734,-4.45281 0.814971,-8.96468 2.8583,-13.075 -4.39375,3.76776 -9.80327,5.84653 -15.3309,6.20086 -6.63788,0.425202 -13.4765,-1.65356 -18.9097,-6.28354l-0.0118112 0.0118112c-0.070867,-0.0590558 -0.153545,-0.129923 -0.224412,-0.20079l0 0c-0.165356,-0.141734 -0.330713,-0.295279 -0.484258,-0.448824 -5.16148,-4.74809 -7.99616,-11.1143 -8.42136,-17.6459 -0.448824,-6.72056 1.65356,-13.6301 6.37803,-19.1577l3.64965 -4.28745c-4.08666,3.1772 -8.92924,5.04337 -13.8781,5.51582 -6.62607,0.625992 -13.4529,-1.24017 -18.9687,-5.79928l-0.0118112 0c-0.141734,-0.118112 -0.283468,-0.248035 -0.425202,-0.377957l-0.0236223 -0.0236223c-5.24416,-4.55911 -8.30325,-10.8545 -8.94105,-17.3506 -0.649614,-6.5552 1.15749,-13.323 5.63393,-18.7443l0 0c0.118112,-0.153545 0.248035,-0.295279 0.377957,-0.425202l0.0118112 -0.0118112 4.84258 -5.58668 -29.0319 -30.0476 -11.8112 20.2916 -0.0354335 0.070867 -0.165356 0.271657 -0.070867 0.0826782c-3.9095,6.42528 -10.0749,10.6419 -16.8782,12.2836 -6.74418,1.62994 -14.138,0.755915 -20.516,-2.9646l-0.070867 -0.070867 -0.283468 -0.165356 -0.0826782 -0.0472447 -37.3587 -22.6538c-6.51977,-3.95674 -10.7718,-10.1576 -12.449,-17.0081 -1.67719,-6.82686 -0.779537,-14.3151 3.01185,-20.8231l118.395 -203.341 0.0354335 -0.0826782 0.165356 -0.271657 0.070867 -0.070867c3.89769,-6.43709 10.0749,-10.6419 16.8782,-12.2836 6.74418,-1.62994 14.1262,-0.755915 20.516,2.9646l0.070867 0.0354335 0.283468 0.165356 0.070867 0.070867 37.3705 22.6656c6.50795,3.94493 10.7718,10.1458 12.449,16.9963 1.66537,6.83867 0.767726,14.3151 -3.02366,20.8349l-16.89 29.02c10.1458,4.8662 17.953,8.30325 25.7129,10.6655 9.33082,2.84649 19.3585,4.42919 34.3469,5.49219l-0.0354335 0.377957c35.7997,-38.8587 76.7017,-35.8233 184.526,-27.8153l5.16148 0.389769 -7.01583 -12.0592c-3.79139,-6.50795 -4.68903,-13.9962 -3.02366,-20.8231 1.67719,-6.85048 5.94102,-13.0513 12.449,-17.0081l37.3705 -22.6538 0.070867 -0.0826782 0.283468 -0.165356 0.070867 -0.0354335c6.38984,-3.72052 13.7718,-4.59454 20.516,-2.9646 6.80323,1.64175 12.9805,5.85834 16.8782,12.2836l0.070867 0.0826782 0.165356 0.271657 0.0354335 0.070867 118.395 203.341c3.79139,6.51977 4.68903,13.9962 3.01185,20.8231 -1.67719,6.86229 -5.92921,13.0513 -12.449,17.0081l-37.3587 22.6656 -0.0826782 0.0354335 -0.283468 0.165356 -0.070867 0.070867c-6.37803,3.72052 -13.7718,4.59454 -20.516,2.9646 -6.80323,-1.64175 -12.9687,-5.84653 -16.8782,-12.2836l-0.070867 -0.070867 -0.165356 -0.271657 -0.0354335 -0.0826782 -12.1655 -20.894 -24.9806 18.8979c4.48824,7.88986 6.25992,16.76 5.65755,25.4058 -0.460636,6.83867 -2.40948,13.5828 -5.65755,19.5947 -3.2835,6.08275 -7.92529,11.4686 -13.7482,15.5435 -5.90558,4.1221 -12.9214,6.85048 -20.8585,7.55915 -2.66932,7.47647 -7.09851,13.8427 -13.2758,19.1105 -6.75599,5.75204 -15.4844,10.0277 -26.1972,12.8151 -2.33861,8.4568 -6.0237,15.5435 -11.1497,21.142 -6.54339,7.14576 -15.1301,11.634 -25.9373,13.2285l-4.50006 1.05119c-29.8468,45.7447 -66.615,23.0908 -99.2611,2.97641 -3.10634,-1.91341 -6.17724,-3.8032 -9.14184,-5.56306zm-217.928 -146.636l118.336 -203.27c1.22836,-2.1142 1.52364,-4.51187 0.992138,-6.69693 -0.50788,-2.06695 -1.78349,-3.94493 -3.72052,-5.11424l-37.2879 -22.6184 -0.0118112 0.0236223 -0.283468 -0.165356 0.0118112 0c-1.98428,-1.12206 -4.25202,-1.39372 -6.31898,-0.885838 -2.0079,0.484258 -3.81501,1.67719 -4.93707,3.47248l0.0236223 0.0118112 -0.165356 0.271657 0 0 -118.348 203.258c-1.22836,2.1142 -1.52364,4.52368 -0.992138,6.69693 0.50788,2.07877 1.78349,3.94493 3.73233,5.12605l37.3705 22.6656 -0.0118112 0 0.129923 0.070867 0.0118112 -0.0236223c1.98428,1.16931 4.28745,1.44096 6.37803,0.933082 2.0079,-0.484258 3.81501,-1.67719 4.93707,-3.47248l-0.0118112 -0.0118112 0.153545 -0.271657 0.0118112 0zm392.485 -171.238c0.732292,0.755915 1.32285,1.62994 1.77168,2.58665l9.42531 16.6301 92.0326 158.092 0.0118112 0 0.153545 0.271657 -0.0118112 0.0118112c1.12206,1.7953 2.92917,2.98823 4.93707,3.47248 2.09058,0.50788 4.39375,0.236223 6.37803,-0.921271l0.0118112 0.0236223 0.129923 -0.0826782 -0.0118112 0 37.3705 -22.6656c1.94884,-1.16931 3.22445,-3.04728 3.73233,-5.11424 0.531503,-2.18507 0.236223,-4.59454 -0.992138,-6.69693l-118.348 -203.27 0 0 -0.165356 -0.271657 0.0236223 -0.0118112c-1.12206,-1.7953 -2.92917,-2.98823 -4.93707,-3.47248 -2.06695,-0.496069 -4.3347,-0.236223 -6.31898,0.885838l0.0118112 0 -0.283468 0.177168 -0.0118112 -0.0236223 -37.2879 22.6066c-1.93703,1.18112 -3.21264,3.04728 -3.72052,5.12605 -0.531503,2.17326 -0.236223,4.58273 0.992138,6.69693l15.1065 25.9491zm-335.366 200.802l0 0 0 0zm58.7724 -7.52371l0.0118112 0 -0.0118112 0z"
			/>
		</svg>
	);
};

export const MailIcon: React.FC<IconSvgProps> = ({ size = 24, fill, ...props }: IconSvgProps) => (
	<svg
		aria-hidden="true"
		fill="none"
		focusable="false"
		height="1em"
		role="presentation"
		viewBox="0 0 24 24"
		width="1em"
		{...props}
	>
		<path
			d="M17 3.5H7C4 3.5 2 5 2 8.5V15.5C2 19 4 20.5 7 20.5H17C20 20.5 22 19 22 15.5V8.5C22 5 20 3.5 17 3.5ZM17.47 9.59L14.34 12.09C13.68 12.62 12.84 12.88 12 12.88C11.16 12.88 10.31 12.62 9.66 12.09L6.53 9.59C6.21 9.33 6.16 8.85 6.41 8.53C6.67 8.21 7.14 8.15 7.46 8.41L10.59 10.91C11.35 11.52 12.64 11.52 13.4 10.91L16.53 8.41C16.85 8.15 17.33 8.2 17.58 8.53C17.84 8.85 17.79 9.33 17.47 9.59Z"
			fill="currentColor"
		/>
	</svg>
);

export const HamburgerMenuIcon: React.FC<IconSvgProps> = ({ fill = 'white', size = 24 }) => (
	<svg
		className="h-6 w-6 text-white"
		fill={fill}
		strokeLinecap="round"
		strokeLinejoin="round"
		strokeWidth="2"
		viewBox="0 0 24 24"
		stroke="currentColor"
		height={size}
		width={size}
	>
		<path d="M4 6h16M4 12h16m-7 6h7"></path>
	</svg>
);

export const CheckIcon: React.FC<IconSvgProps> = ({ size = 24, fill = 'white' }: IconSvgProps) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			height={size}
			viewBox="0 -960 960 960"
			width={size}
			fill={fill}
		>
			<path d="M382-240 154-468l57-57 171 171 367-367 57 57-424 424Z" />
		</svg>
	);
};

export const PlusIcon: React.FC<IconSvgProps> = ({ size = 24, fill = 'white' }: IconSvgProps) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			height={size}
			viewBox="0 -960 960 960"
			width={size}
			fill={fill}
		>
			<path d="M440-440H200v-80h240v-240h80v240h240v80H520v240h-80v-240Z" />
		</svg>
	);
};

export const CrossIcon: React.FC<IconSvgProps> = ({ size = 24, fill = 'white' }: IconSvgProps) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			viewBox="0 -960 960 960"
			width={size}
			height={size}
			fill={fill}
		>
			<path
				d="M440,-440h-240v-80h240v-240h80v240h240v80h-240v240h-80v-240Z"
				transform="matrix(.707107 0.707107-.707107 0.707107-198.82251-480)"
			/>
		</svg>
	);
};

export const DropdownIcon: React.FC<IconSvgProps> = ({ color = 'white', size = 48, ...props }) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			height={size}
			width={size}
			fill={color}
			viewBox="0 -960 960 960"
		>
			<path d="M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z" />
		</svg>
	);
};

export const BoltIcon: React.FC<IconSvgProps> = ({ size = 48, color = 'white', ...props }) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			height={size}
			viewBox="0 -960 960 960"
			width={size}
			fill={color}
		>
			<path d="m456-200 174-340H510v-220L330-420h126v220Zm24 120q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z" />
		</svg>
	);
};

export const ShareIcon: React.FC<IconSvgProps> = ({ size = 48, color = 'white', ...props }) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			height={size}
			fill={color}
			viewBox="0 -960 960 960"
			width={size}
		>
			<path d="M240-40q-33 0-56.5-23.5T160-120v-440q0-33 23.5-56.5T240-640h120v80H240v440h480v-440H600v-80h120q33 0 56.5 23.5T800-560v440q0 33-23.5 56.5T720-40H240Zm200-280v-447l-64 64-56-57 160-160 160 160-56 57-64-64v447h-80Z" />
		</svg>
	);
};

export const SettingsIcon: React.FC<IconSvgProps> = ({ size = 48, color = 'white', ...props }) => {
	return (
		<svg
			fill="none"
			viewBox="0 0 24 24"
			strokeWidth={1.5}
			stroke={color}
			height={size}
			width={size}
		>
			<path
				strokeLinecap="round"
				strokeLinejoin="round"
				d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"
			/>
			<path
				strokeLinecap="round"
				strokeLinejoin="round"
				d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"
			/>
		</svg>
	);
};

export const NewChatIcon: React.FC<IconSvgProps> = ({
	size = 48,
	stroke = 'white',
	className = '',
	...props
}) => {
	return (
		<svg
			height={size}
			width={size}
			viewBox="0 0 16 16"
			fill="none"
			stroke={stroke}
			className={className}
		>
			<path d="M13.488 2.513a1.75 1.75 0 0 0-2.475 0L6.75 6.774a2.75 2.75 0 0 0-.596.892l-.848 2.047a.75.75 0 0 0 .98.98l2.047-.848a2.75 2.75 0 0 0 .892-.596l4.261-4.262a1.75 1.75 0 0 0 0-2.474Z" />
			<path
				fill={stroke}
				stroke="none"
				d="M4.75 3.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h6.5c.69 0 1.25-.56 1.25-1.25V9A.75.75 0 0 1 14 9v2.25A2.75 2.75 0 0 1 11.25 14h-6.5A2.75 2.75 0 0 1 2 11.25v-6.5A2.75 2.75 0 0 1 4.75 2H7a.75.75 0 0 1 0 1.5H4.75Z"
			/>
		</svg>
	);
};

export const EditIcon: React.FC<IconSvgProps> = ({
	stroke = 'white',
	size = 48,
	className = '',
}) => {
	return (
		<svg
			height={size}
			width={size}
			viewBox="0 0 16 16"
			fill="none"
			stroke={stroke}
			className={className}
		>
			<path
				fillRule="evenodd"
				d="M11.013 2.513a1.75 1.75 0 0 1 2.475 2.474L6.226 12.25a2.751 2.751 0 0 1-.892.596l-2.047.848a.75.75 0 0 1-.98-.98l.848-2.047a2.75 2.75 0 0 1 .596-.892l7.262-7.261Z"
				clipRule="evenodd"
			/>
		</svg>
	);
};

export const DeleteIcon: React.FC<IconSvgProps> = ({ size = 48, color = 'white', ...props }) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			height={size}
			viewBox="0 -960 960 960"
			width={size}
			fill={color}
		>
			<path d="M280-120q-33 0-56.5-23.5T200-200v-520h-40v-80h200v-40h240v40h200v80h-40v520q0 33-23.5 56.5T680-120H280Zm400-600H280v520h400v-520ZM360-280h80v-360h-80v360Zm160 0h80v-360h-80v360ZM280-720v520-520Z" />
		</svg>
	);
};

export const MoreDetailsIcon: React.FC<IconSvgProps> = ({
	size = 48,
	color = 'white',
	...props
}) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			height={size}
			viewBox="0 -960 960 960"
			width={size}
			fill={color}
		>
			<path d="M240-400q-33 0-56.5-23.5T160-480q0-33 23.5-56.5T240-560q33 0 56.5 23.5T320-480q0 33-23.5 56.5T240-400Zm240 0q-33 0-56.5-23.5T400-480q0-33 23.5-56.5T480-560q33 0 56.5 23.5T560-480q0 33-23.5 56.5T480-400Zm240 0q-33 0-56.5-23.5T640-480q0-33 23.5-56.5T720-560q33 0 56.5 23.5T800-480q0 33-23.5 56.5T720-400Z" />
		</svg>
	);
};

export const SendQueryIcon: React.FC<IconSvgProps> = ({ size = 48, color = 'white', ...props }) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			height={size}
			viewBox="0 -960 960 960"
			width={size}
			fill={color}
		>
			<path d="M120-160v-640l760 320-760 320Zm80-120 474-200-474-200v140l240 60-240 60v140Zm0 0v-400 400Z" />
		</svg>
	);
};

export const AttachmentIcon: React.FC<IconSvgProps> = ({
	size = 48,
	color = 'white',
	...props
}) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			height={size}
			viewBox="0 -960 960 960"
			width={size}
			fill={color}
		>
			<path d="M460-80q-92 0-156-64t-64-156v-420q0-66 47-113t113-47q66 0 113 47t47 113v380q0 42-29 71t-71 29q-42 0-71-29t-29-71v-380h60v380q0 17 11.5 28.5T460-300q17 0 28.5-11.5T500-340v-380q0-42-29-71t-71-29q-42 0-71 29t-29 71v420q0 66 47 113t113 47q66 0 113-47t47-113v-420h60v420q0 92-64 156T460-80Z" />
		</svg>
	);
};

export const RegenerateAnswerIcon: React.FC<IconSvgProps> = ({
	size = 48,
	stroke = 'white',
	className = '',
	...props
}) => {
	return (
		<svg
			height={size}
			width={size}
			fill="none"
			viewBox="0 0 24 24"
			strokeWidth={1.5}
			stroke={stroke}
			className={className}
		>
			<path
				strokeLinecap="round"
				strokeLinejoin="round"
				d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99"
			/>
		</svg>
	);
};

export const CopyIcon: React.FC<IconSvgProps> = ({
	size = 48,
	stroke = 'white',
	className = '',
}) => {
	return (
		<svg
			height={size}
			width={size}
			fill="none"
			viewBox="0 0 24 24"
			strokeWidth={1.5}
			stroke={stroke}
			className={className}
		>
			<path
				strokeLinecap="round"
				strokeLinejoin="round"
				d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 0 1-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H9.75"
			/>
		</svg>
	);
};

export const MinimizeSidebarIcon: React.FC<IconSvgProps> = ({ size = 48, color = 'white' }) => {
	return (
		<svg
			id="elyzFUbDpHT1"
			xmlns="http://www.w3.org/2000/svg"
			viewBox="0 -960 960 960"
			shapeRendering="geometricPrecision"
			textRendering="geometricPrecision"
			height={size}
			width={size}
			fill={color}
		>
			<path
				d="M224.782,-194.5L428.369,-480L224.782,-765.5h111.631L540,-480L336.413,-194.5h-111.631Zm251.631,0L680,-480L476.413,-765.5h111.392L791.631,-480L587.805,-194.5h-111.392Z"
				transform="matrix(-1 0 0-1 1016.412994-960)"
				fill={color}
				stroke={color}
			/>
		</svg>
	);
};

export const ExtendSidebarIcon: React.FC<IconSvgProps> = ({ size = 48, color = 'white' }) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			height={size}
			viewBox="0 -960 960 960"
			width={size}
			fill={color}
		>
			<path d="M224.782-194.5 428.369-480 224.782-765.5h111.631L540-480 336.413-194.5H224.782Zm251.631 0L680-480 476.413-765.5h111.392L791.631-480 587.805-194.5H476.413Z" />
		</svg>
	);
};

export const ExpandMoreIcon: React.FC<IconSvgProps> = ({ size = 48, color = 'white' }) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			height={size}
			viewBox="0 -960 960 960"
			width={size}
			fill={color}
			stroke={color}
		>
			<path d="M480-346.463 253.847-572.615 291-609.768l189 189 189-189 37.153 37.153L480-346.463Z" />
		</svg>
	);
};

export const ExpandLessIcon: React.FC<IconSvgProps> = ({ size = 48, color = 'white' }) => {
	return (
		<svg
			xmlns="http://www.w3.org/2000/svg"
			height={size}
			viewBox="0 -960 960 960"
			width={size}
			fill={color}
			stroke={color}
		>
			<path d="m296-358.463-42.153-42.152L480-626.768l226.153 226.153L664-358.463l-184-184-184 184Z" />
		</svg>
	);
};

export const EyeSlashFilledIcon: React.FC<IconSvgProps> = (props) => (
	<svg
		aria-hidden="true"
		fill="none"
		focusable="false"
		height="1em"
		role="presentation"
		viewBox="0 0 24 24"
		width="1em"
		{...props}
	>
		<path
			d="M21.2714 9.17834C20.9814 8.71834 20.6714 8.28834 20.3514 7.88834C19.9814 7.41834 19.2814 7.37834 18.8614 7.79834L15.8614 10.7983C16.0814 11.4583 16.1214 12.2183 15.9214 13.0083C15.5714 14.4183 14.4314 15.5583 13.0214 15.9083C12.2314 16.1083 11.4714 16.0683 10.8114 15.8483C10.8114 15.8483 9.38141 17.2783 8.35141 18.3083C7.85141 18.8083 8.01141 19.6883 8.68141 19.9483C9.75141 20.3583 10.8614 20.5683 12.0014 20.5683C13.7814 20.5683 15.5114 20.0483 17.0914 19.0783C18.7014 18.0783 20.1514 16.6083 21.3214 14.7383C22.2714 13.2283 22.2214 10.6883 21.2714 9.17834Z"
			fill="currentColor"
		/>
		<path
			d="M14.0206 9.98062L9.98062 14.0206C9.47062 13.5006 9.14062 12.7806 9.14062 12.0006C9.14062 10.4306 10.4206 9.14062 12.0006 9.14062C12.7806 9.14062 13.5006 9.47062 14.0206 9.98062Z"
			fill="currentColor"
		/>
		<path
			d="M18.25 5.74969L14.86 9.13969C14.13 8.39969 13.12 7.95969 12 7.95969C9.76 7.95969 7.96 9.76969 7.96 11.9997C7.96 13.1197 8.41 14.1297 9.14 14.8597L5.76 18.2497H5.75C4.64 17.3497 3.62 16.1997 2.75 14.8397C1.75 13.2697 1.75 10.7197 2.75 9.14969C3.91 7.32969 5.33 5.89969 6.91 4.91969C8.49 3.95969 10.22 3.42969 12 3.42969C14.23 3.42969 16.39 4.24969 18.25 5.74969Z"
			fill="currentColor"
		/>
		<path
			d="M14.8581 11.9981C14.8581 13.5681 13.5781 14.8581 11.9981 14.8581C11.9381 14.8581 11.8881 14.8581 11.8281 14.8381L14.8381 11.8281C14.8581 11.8881 14.8581 11.9381 14.8581 11.9981Z"
			fill="currentColor"
		/>
		<path
			d="M21.7689 2.22891C21.4689 1.92891 20.9789 1.92891 20.6789 2.22891L2.22891 20.6889C1.92891 20.9889 1.92891 21.4789 2.22891 21.7789C2.37891 21.9189 2.56891 21.9989 2.76891 21.9989C2.96891 21.9989 3.15891 21.9189 3.30891 21.7689L21.7689 3.30891C22.0789 3.00891 22.0789 2.52891 21.7689 2.22891Z"
			fill="currentColor"
		/>
	</svg>
);

export const EyeFilledIcon: React.FC<IconSvgProps> = (props) => (
	<svg
		aria-hidden="true"
		fill="none"
		focusable="false"
		height="1em"
		role="presentation"
		viewBox="0 0 24 24"
		width="1em"
		{...props}
	>
		<path
			d="M21.25 9.14969C18.94 5.51969 15.56 3.42969 12 3.42969C10.22 3.42969 8.49 3.94969 6.91 4.91969C5.33 5.89969 3.91 7.32969 2.75 9.14969C1.75 10.7197 1.75 13.2697 2.75 14.8397C5.06 18.4797 8.44 20.5597 12 20.5597C13.78 20.5597 15.51 20.0397 17.09 19.0697C18.67 18.0897 20.09 16.6597 21.25 14.8397C22.25 13.2797 22.25 10.7197 21.25 9.14969ZM12 16.0397C9.76 16.0397 7.96 14.2297 7.96 11.9997C7.96 9.76969 9.76 7.95969 12 7.95969C14.24 7.95969 16.04 9.76969 16.04 11.9997C16.04 14.2297 14.24 16.0397 12 16.0397Z"
			fill="currentColor"
		/>
		<path
			d="M11.9984 9.14062C10.4284 9.14062 9.14844 10.4206 9.14844 12.0006C9.14844 13.5706 10.4284 14.8506 11.9984 14.8506C13.5684 14.8506 14.8584 13.5706 14.8584 12.0006C14.8584 10.4306 13.5684 9.14062 11.9984 9.14062Z"
			fill="currentColor"
		/>
	</svg>
);

export const TripleDotIcon: React.FC<IconSvgProps> = ({
	size = 24,
	fill = 'white',
	className = '',
}) => {
	return (
		<svg
			height={size}
			width={size}
			fill="none"
			viewBox="0 0 24 24"
			strokeWidth={1.5}
			stroke="currentColor"
			className={`size-6 ${className}`}
		>
			<path
				strokeLinecap="round"
				strokeLinejoin="round"
				d="M6.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM12.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0ZM18.75 12a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z"
			/>
		</svg>
	);
};

export const ArrowUpIcon: React.FC<IconSvgProps> = ({
	size = 24,
	stroke = 'white',
	className = '',
}) => {
	return (
		<svg
			height={size}
			width={size}
			fill="none"
			viewBox="0 0 24 24"
			strokeWidth={1.5}
			stroke={stroke}
			className={className}
		>
			<path
				strokeLinecap="round"
				strokeLinejoin="round"
				d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"
			/>
		</svg>
	);
};

export const ChartIcon: React.FC<IconSvgProps> = ({
	size = 24,
	stroke = 'white',
	className = '',
}) => {
	return (
		<svg
			height={size}
			width={size}
			fill="none"
			viewBox="0 0 24 24"
			strokeWidth={1.5}
			stroke={stroke}
			className={className}
		>
			<path
				strokeLinecap="round"
				strokeLinejoin="round"
				d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"
			/>
		</svg>
	);
};

export const SparklesIcon: React.FC<IconSvgProps> = ({
	size = 24,
	stroke = 'white',
	className = '',
}) => {
	return (
		<svg
			height={size}
			width={size}
			fill="none"
			viewBox="0 0 24 24"
			strokeWidth={1.5}
			stroke={stroke}
			className={className}
		>
			<path
				strokeLinecap="round"
				strokeLinejoin="round"
				d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"
			/>
		</svg>
	);
};

export const Spinner: React.FC<IconSvgProps> = () => {
	return (
		<svg
			fill="none"
			stroke="currentColor"
			strokeWidth="1.5"
			viewBox="0 0 24 24"
			strokeLinecap="round"
			strokeLinejoin="round"
			xmlns="http://www.w3.org/2000/svg"
			className="mx-1 my-4 size-6 animate-spin stroke-zinc-400"
		>
			<path d="M12 3v3m6.366-.366-2.12 2.12M21 12h-3m.366 6.366-2.12-2.12M12 21v-3m-6.366.366 2.12-2.12M3 12h3m-.366-6.366 2.12 2.12"></path>
		</svg>
	);
};

export const ImageIcon: React.FC<IconSvgProps> = ({ size = 48 }) => {
	return (
		<svg
			width={size}
			height={size}
			viewBox="0 0 24.00 24.00"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			stroke="#ffffff"
			transform="matrix(1, 0, 0, 1, 0, 0)rotate(0)"
		>
			<g
				id="SVGRepo_bgCarrier"
				strokeWidth="0"
			></g>
			<g
				id="SVGRepo_tracerCarrier"
				strokeLinecap="round"
				strokeLinejoin="round"
				stroke="#CCCCCC"
				strokeWidth="0.768"
			></g>
			<g id="SVGRepo_iconCarrier">
				{' '}
				<path
					d="M14.2639 15.9375L12.5958 14.2834C11.7909 13.4851 11.3884 13.086 10.9266 12.9401C10.5204 12.8118 10.0838 12.8165 9.68048 12.9536C9.22188 13.1095 8.82814 13.5172 8.04068 14.3326L4.04409 18.2801M14.2639 15.9375L14.6053 15.599C15.4112 14.7998 15.8141 14.4002 16.2765 14.2543C16.6831 14.126 17.12 14.1311 17.5236 14.2687C17.9824 14.4251 18.3761 14.8339 19.1634 15.6514L20 16.4934M14.2639 15.9375L18.275 19.9565M18.275 19.9565C17.9176 20 17.4543 20 16.8 20H7.2C6.07989 20 5.51984 20 5.09202 19.782C4.71569 19.5903 4.40973 19.2843 4.21799 18.908C4.12796 18.7313 4.07512 18.5321 4.04409 18.2801M18.275 19.9565C18.5293 19.9256 18.7301 19.8727 18.908 19.782C19.2843 19.5903 19.5903 19.2843 19.782 18.908C20 18.4802 20 17.9201 20 16.8V16.4934M4.04409 18.2801C4 17.9221 4 17.4575 4 16.8V7.2C4 6.0799 4 5.51984 4.21799 5.09202C4.40973 4.71569 4.71569 4.40973 5.09202 4.21799C5.51984 4 6.07989 4 7.2 4H16.8C17.9201 4 18.4802 4 18.908 4.21799C19.2843 4.40973 19.5903 4.71569 19.782 5.09202C20 5.51984 20 6.0799 20 7.2V16.4934M17 8.99989C17 10.1045 16.1046 10.9999 15 10.9999C13.8954 10.9999 13 10.1045 13 8.99989C13 7.89532 13.8954 6.99989 15 6.99989C16.1046 6.99989 17 7.89532 17 8.99989Z"
					stroke="#ffffff"
					strokeWidth="1.8"
					strokeLinecap="round"
					strokeLinejoin="round"
				></path>{' '}
			</g>
		</svg>
	);
};

export const ModifySettingsIcon: React.FC<IconSvgProps> = ({ size = 48 }) => {
	return (
		<svg
			width={size}
			height={size}
			viewBox="0 0 24.00 24.00"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			transform="matrix(1, 0, 0, 1, 0, 0)rotate(90)"
		>
			<g
				id="SVGRepo_bgCarrier"
				strokeWidth="0"
			></g>
			<g
				id="SVGRepo_tracerCarrier"
				strokeLinecap="round"
				strokeLinejoin="round"
				stroke="#da3434"
				strokeWidth="0.672"
			></g>
			<g id="SVGRepo_iconCarrier">
				{' '}
				<path
					d="M19 22V11"
					stroke="#ffffff"
					strokeWidth="1.5"
					strokeMiterlimit="10"
					strokeLinecap="round"
					strokeLinejoin="round"
				></path>{' '}
				<path
					d="M19 7V2"
					stroke="#ffffff"
					strokeWidth="1.5"
					strokeMiterlimit="10"
					strokeLinecap="round"
					strokeLinejoin="round"
				></path>{' '}
				<path
					d="M12 22V17"
					stroke="#ffffff"
					strokeWidth="1.5"
					strokeMiterlimit="10"
					strokeLinecap="round"
					strokeLinejoin="round"
				></path>{' '}
				<path
					d="M12 13V2"
					stroke="#ffffff"
					strokeWidth="1.5"
					strokeMiterlimit="10"
					strokeLinecap="round"
					strokeLinejoin="round"
				></path>{' '}
				<path
					d="M5 22V11"
					stroke="#ffffff"
					strokeWidth="1.5"
					strokeMiterlimit="10"
					strokeLinecap="round"
					strokeLinejoin="round"
				></path>{' '}
				<path
					d="M5 7V2"
					stroke="#ffffff"
					strokeWidth="1.5"
					strokeMiterlimit="10"
					strokeLinecap="round"
					strokeLinejoin="round"
				></path>{' '}
				<path
					d="M3 11H7"
					stroke="#ffffff"
					strokeWidth="1.5"
					strokeMiterlimit="10"
					strokeLinecap="round"
					strokeLinejoin="round"
				></path>{' '}
				<path
					d="M17 11H21"
					stroke="#ffffff"
					strokeWidth="1.5"
					strokeMiterlimit="10"
					strokeLinecap="round"
					strokeLinejoin="round"
				></path>{' '}
				<path
					d="M10 13H14"
					stroke="#ffffff"
					strokeWidth="1.5"
					strokeMiterlimit="10"
					strokeLinecap="round"
					strokeLinejoin="round"
				></path>{' '}
			</g>
		</svg>
	);
};

/**
 * Company Icons
 */

export const PwCLogo: React.FC<IconSvgProps> = ({ size = 24, color = '#ffffff', ...props }) => (
	<svg
		xmlns="http://www.w3.org/2000/svg"
		viewBox="0 0 72 34.9"
		width={size}
		height={size}
		color={color}
		{...props}
	>
		<g fill="currentColor">
			<path d="M53.3,14.7c-2.5.4-3.8,2.3-3.8,5.6s1.7,5.5,4.4,5.5,2.3-.4,4.7-1.6v2.7c-2.8,1.3-4.5,1.7-6.8,1.7s-4.2-.7-5.6-2.1c-1.4-1.4-2.2-3.4-2.2-5.5,0-4.7,3.5-8,8.6-8s5.7,1.6,5.7,3.8-1.1,2.5-2.7,2.5-1.5-.2-2.4-.7v-4ZM40.8,21c2.3-2.9,3.1-4,3.1-5.4s-1.1-2.5-2.6-2.5-1.7.4-2.1.9v5.8l-3.7,5v-11.3h-3.5l-5.9,9.8v-9.8h-2l-5.3,1.3v1.4l2.9.3v12h3.8l5.6-9.3v9.3h4.1s5.7-7.3,5.7-7.3ZM7.4,16.5c.9-.1,1.3-.2,1.7-.2,2.5,0,3.8,1.6,3.8,4.7s-1.6,5.5-4.7,5.5-.4,0-.8,0c0,0,0-10,0-10ZM7.4,28.2c1,.1,1.9.1,2.5.1,5,0,8.2-3.2,8.2-8s-2.4-7.1-5.6-7.1-2.4.3-5.1,1.9v-2h-1.5L0,14.9v1.4h2.4v16.7l-2.2.5v1.4h9.6v-1.4l-2.4-.5v-4.8s0,0,0,0Z" />
			<path d="M50.6,9h-16l2.7-4.5h16l-2.7,4.5ZM72,0h-16l-2.7,4.5h16s2.7-4.5,2.7-4.5Z" />
		</g>
	</svg>
);

export const UdaanLogo: React.FC<IconSvgProps> = ({ size = 24, color = '#ffffff', ...props }) => (
	<svg
		width={size}
		height={size}
		viewBox="0 0 271 263"
		xmlns="http://www.w3.org/2000/svg"
		color={color}
		{...props}
	>
		<g fill="currentColor">
			<path
				d="M262.108 131.061C262.108 203.44 203.433 262.115 131.054 262.115C58.675 262.115 0 203.44 0 131.061C0 58.6816 58.675 0.00663757 131.054 0.00663757C203.433 0.00663757 262.108 58.6816 262.108 131.061Z"
				fill="#525252"
			/>
			<path
				d="M164.605 79.8388L182.194 79.7614C188.991 79.7316 194.462 74.1967 194.304 67.4013C194.146 60.5848 188.594 55.0733 181.776 55.0733H81.0655C74.2233 55.0733 68.6766 60.9839 68.6766 67.826C68.6765 74.668 74.2231 79.8506 81.0651 79.8506H128.574C134.866 79.8506 141.669 82.1065 143.495 88.1274C144.055 89.9766 144.335 91.9371 144.335 94.009C144.335 102.504 137.921 109.977 125.093 116.427C123.23 117.401 120.834 118.36 118.416 119.22C112.532 121.312 108.949 127.518 110.686 133.517C112.571 140.025 118.895 144.196 125.63 143.448C128.96 143.078 132.153 142.745 133.883 142.62C140.059 142.62 145.048 144.193 148.849 147.339C152.808 150.485 154.788 154.812 154.788 160.318C154.788 165.824 152.65 170.465 148.374 174.24C144.256 178.016 138.713 179.904 131.745 179.904C113.042 179.904 101.438 170.419 85.699 139.052C83.2904 134.251 77.8579 131.752 73.0935 134.231C68.9219 136.401 67.964 141.123 69.65 145.513C75.4006 160.486 78.3637 168.771 88.0347 180.847C94.3695 188.87 101.892 195.242 110.602 199.961C119.313 204.681 128.578 207.04 138.397 207.04C151.7 207.04 162.311 203.108 170.229 195.242C178.148 187.219 182.107 177.78 182.107 166.925C182.107 155.913 179.256 147.182 173.555 140.732C168.667 135.044 162.424 131.008 154.827 128.623C153.612 128.242 153.298 126.592 154.297 125.802C164.443 117.785 169.516 108.761 169.516 98.7285C169.516 92.513 167.563 86.9038 163.656 81.901C163.021 81.0877 163.574 79.8433 164.605 79.8388Z"
				fill="#a3a3a3"
			/>
		</g>
	</svg>
);

export const GoogleWorkMarkLogo: React.FC<IconSvgProps> = ({
	size = 24,
	color = '#ffffff',
	...props
}) => (
	<svg
		viewBox="0 0 139.9 44"
		width={size}
		height={size}
		color={color}
		{...props}
	>
		<g fill="currentColor">
			<path d="M17.5,15.7v4.7h11.3c-0.3,2.6-1.2,4.6-2.6,5.9c-1.6,1.6-4.2,3.4-8.7,3.4c-6.9,0-12.3-5.6-12.3-12.5S10.5,4.7,17.5,4.7c3.7,0,6.5,1.5,8.5,3.4l3.3-3.3C26.5,2.1,22.7,0,17.5,0C8,0,0,7.7,0,17.2s8,17.2,17.5,17.2c5.1,0,9-1.7,12-4.8c3.1-3.1,4.1-7.5,4.1-11c0-1.1-0.1-2.1-0.2-2.9H17.5z" />
			<path d="M47.6,11.8c-6.1,0-11.2,4.7-11.2,11.1c0,6.4,5,11.1,11.2,11.1S58.8,29.4,58.8,23C58.8,16.5,53.8,11.8,47.6,11.8z M47.6,29.7c-3.4,0-6.3-2.8-6.3-6.7c0-4,2.9-6.7,6.3-6.7c3.4,0,6.3,2.7,6.3,6.7C53.9,26.9,51,29.7,47.6,29.7z" />
			<path d="M102.3,14.3h-0.2c-1.1-1.3-3.2-2.5-5.9-2.5c-5.6,0-10.4,4.8-10.4,11.1c0,6.2,4.8,11.1,10.4,11.1c2.7,0,4.8-1.2,5.9-2.5h0.2v1.6c0,4.2-2.3,6.5-5.9,6.5c-3,0-4.8-2.1-5.6-4l-4.2,1.8c1.2,2.9,4.5,6.6,9.9,6.6c5.7,0,10.6-3.4,10.6-11.6v-20h-4.6V14.3z M96.7,29.7c-3.4,0-5.9-2.9-5.9-6.7c0-3.9,2.6-6.7,5.9-6.7c3.3,0,5.9,2.9,5.9,6.8C102.7,26.9,100.1,29.7,96.7,29.7z" />
			<path d="M72.5,11.8c-6.1,0-11.2,4.7-11.2,11.1c0,6.4,5,11.1,11.2,11.1S83.7,29.4,83.7,23C83.7,16.5,78.7,11.8,72.5,11.8z M72.5,29.7c-3.4,0-6.3-2.8-6.3-6.7c0-4,2.9-6.7,6.3-6.7s6.3,2.7,6.3,6.7C78.8,26.9,75.9,29.7,72.5,29.7z" />
			<path d="M110.8,0.5h4.8v33.6h-4.8V0.5z" />
			<path d="M130.4,29.7c-2.5,0-4.2-1.1-5.4-3.4l14.9-6.1l-0.5-1.3c-0.9-2.5-3.8-7.1-9.5-7.1c-5.7,0-10.5,4.5-10.5,11.1c0,6.2,4.7,11.1,11,11.1c5.1,0,8-3.1,9.3-4.9l-3.8-2.5C134.6,28.5,132.9,29.7,130.4,29.7L130.4,29.7z M130.1,16c2,0,3.7,1,4.2,2.4l-10,4.2C124.2,18,127.6,16,130.1,16z" />
		</g>
	</svg>
);

export const SwiggyWorkMarkLogo: React.FC<IconSvgProps> = ({
	size = 24,
	color = '#ffffff',
	...props
}) => (
	<svg
		viewBox="-126.3 -45 544.5 158.5"
		width={size}
		height={size}
		color={color}
		{...props}
	>
		<g fill="currentColor">
			<path d="M-72.6,113.5c-0.1-0.1-0.2-0.2-0.3-0.3c-1.9-2.4-13.7-17.2-25.8-36.1c-3.6-6-6-10.8-5.5-12 c1.2-3.2,22.2-4.9,28.6-2c2,0.9,1.9,2,1.9,2.7c0,2.9-0.1,10.7-0.1,10.7c0,1.6,1.3,2.9,2.9,2.9c1.6,0,2.9-1.3,2.9-2.9l0-19.4h0 c0-1.7-1.8-2.1-2.2-2.2c-3.4,0-10.2,0-17.6,0c-16.2,0-19.9,0.7-22.6-1.1c-6-3.9-15.7-30.2-16-45c-0.3-20.9,12.1-39,29.5-47.9 c7.3-3.7,15.5-5.7,24.2-5.7C-45-45-22.3-24.2-19.3,2.6c0,0,0,0,0,0.1c0.6,6.5-35.1,7.9-42.2,6c-1.1-0.3-1.4-1.4-1.4-1.9 c0-4.9,0-18.8,0-18.8c0-1.6-1.3-2.9-2.9-2.9h0c-1.6,0-2.9,1.3-2.9,2.9l0.1,25.5c0,1.6,1.4,2,1.8,2.1c4,0,13.4,0,22.2,0 c11.8,0,16.8,1.4,20.1,3.9c2.2,1.7,3,4.9,2.3,9C-29,65.7-71,111.6-72.6,113.5z M41.3,27c10.6,4.5,17.2,9.6,17.2,20.6 c0,11.3-8.6,18.6-21.8,18.6c-10.7,0-19.3-4.9-23.6-13.3l-0.7-1.4L25,44.2l0.8,1.4c2.8,4.6,6,6.6,10.6,6.6c4,0,6.6-1.8,6.6-4.4 c0-2.9-1.9-4-7.9-6.6l-3-1.3c-8.1-3.5-15.6-8.2-15.6-19.9c0-10.4,8-17.7,19.5-17.7c8.7,0,14.6,3.4,18.7,10.6l0.8,1.3l-12.2,7.9 l-0.8-1.5c-2-3.7-4-4.4-6.4-4.4c-2.6,0-4.2,1.4-4.2,3.6c0,2.5,1.2,3.6,6.5,5.9L41.3,27z M130.6,29.1l8.9-24.8h15.3l-22.7,63h-3.4 L115,37.7c-0.6-1.4-1.3-3-1.9-4.6c-0.6,1.5-1.3,3.2-1.9,4.6L97,67.2h-3.4l-23-63h16.3l9,24.8c0.6,1.6,1.2,3.5,1.7,5.3 c0.6-1.8,1.3-3.8,2.1-5.5l11.8-25.2h3.3l12,25.2c0.8,1.6,1.5,3.6,2.1,5.5C129.5,32.6,130.1,30.7,130.6,29.1z M172.7,65.4V3h15.4 v62.4H172.7z M238.7,44.4v-14h28.6v28l-0.6,0.5c-3.3,2.7-12,7.2-23.7,7.2c-19.9,0-33.7-13.2-33.7-32c0-18.5,13.4-31.9,31.8-31.9 c10.1,0,17.6,2.9,22.7,8.7l1,1.1l-10.5,10.4l-1.1-1.2c-2.9-3-6-4.9-12.1-4.9c-9.4,0-16,7.3-16,17.7c0,10.9,7,17.9,17.9,17.9 c3.6,0,7.4-0.6,9.9-1.6v-6H238.7z M316.9,44.4v-14h28.6v28l-0.6,0.5c-3.3,2.7-12,7.2-23.7,7.2c-19.9,0-33.7-13.2-33.7-32 c0-18.5,13.4-31.9,31.8-31.9c10.1,0,17.6,2.9,22.7,8.7l1,1.1l-10.5,10.4l-1.1-1.2c-2.9-3-6-4.9-12.1-4.9c-9.4,0-16,7.3-16,17.7 c0,10.9,7,17.9,18,17.9c3.6,0,7.4-0.6,9.9-1.6v-6H316.9z M402.1,3h16.1L396,44.5v20.9h-15.5v-20L357,3h17.2l10.9,19.9 c1.2,2.2,2.4,5,3.3,7.4c0.9-2.3,2.1-5.1,3.2-7.3L402.1,3z" />
		</g>
	</svg>
);

export const AmazonWorkMarkLogo: React.FC<IconSvgProps> = ({
	size = 24,
	color = '#fff',
	...props
}) => (
	<svg
		viewBox="0 0 398.6 133.5"
		width={size}
		height={size}
		color={color}
		{...props}
	>
		<g
			transform="translate(128.24732,-81.330339)"
			fill="currentColor"
		>
			<path d="m -108.757,152.11534 c -3.86001,0 -7.25789,-0.84271 -10.19368,-2.52813 -2.93579,-1.73959 -5.21918,-4.13178 -6.85016,-7.17658 -1.63099,-3.09845 -2.44648,-6.6594 -2.44648,-10.68285 0,-4.83855 1.19606,-9.07919 3.58819,-12.7214 2.44646,-3.64271 5.89871,-6.44272 10.35675,-8.40001 4.51241,-1.95679 9.78596,-2.93543 15.820617,-2.93543 2.772665,0 5.02887,0.1901 6.768614,0.57083 v -1.7125 c 0,-4.13199 -0.761148,-7.20372 -2.283392,-9.215238 -1.522244,-2.065943 -3.832824,-3.098914 -6.931739,-3.098914 -5.00168,0 -8.2093,2.636778 -9.62279,7.910282 -0.43495,1.5223 -1.33198,2.1747 -2.69115,1.9572 l -11.09071,-1.9572 c -0.81553,-0.16307 -1.38636,-0.51646 -1.71256,-1.06011 -0.27182,-0.54369 -0.29901,-1.277604 -0.0815,-2.201826 1.41352,-5.54538 4.40369,-9.840341 8.97042,-12.884829 4.62116,-3.098864 10.41118,-4.648296 17.370052,-4.648296 8.26367,0 14.54296,2.229017 18.837921,6.687051 4.349386,4.458034 6.523875,10.954773 6.523875,19.49052 v 41.0194 c 0,0.59792 -0.217194,1.11407 -0.652091,1.54897 -0.380728,0.38072 -0.870307,0.57083 -1.468227,0.57083 h -10.601336 c -1.304795,0 -2.120267,-0.70677 -2.446466,-2.1198 l -1.630994,-7.66565 h -0.652398 c -1.467869,3.58803 -3.669699,6.36044 -6.605489,8.31774 -2.881417,1.95729 -6.306477,2.93594 -10.275237,2.93594 z m -1.71256,-22.34485 c 0,2.88177 0.73396,5.1922 2.20183,6.93179 1.5223,1.68542 3.561,2.52813 6.11622,2.52813 3.370736,0 5.953139,-1.49531 7.747208,-4.48543 1.794068,-3.04428 2.691153,-7.39377 2.691153,-13.04744 v -4.3224 c -1.522295,-0.27188 -2.962977,-0.40782 -4.322148,-0.40782 -4.458033,0 -7.991843,1.16876 -10.601433,3.50678 -2.55522,2.28334 -3.83283,5.38231 -3.83283,9.29639 z m 46.156809,-44.852015 c 0,-0.598022 0.190619,-1.087295 0.570836,-1.467869 0.434897,-0.434949 0.951562,-0.652398 1.549483,-0.652398 h 10.764613 c 0.597921,0 1.0875,0.190262 1.467716,0.570836 0.435409,0.380573 0.706773,0.897034 0.815625,1.549431 l 1.63125,7.991847 h 0.652091 c 1.468227,-3.751261 3.560949,-6.605489 6.279699,-8.562682 2.772409,-2.011568 6.034398,-3.017301 9.785455,-3.017301 4.023443,0 7.47554,1.060108 10.356801,3.180426 2.935943,2.120267 5.192199,5.13762 6.768767,9.051955 h 0.652602 c 2.392194,-4.131835 5.083347,-7.203512 8.0734608,-9.21508 2.9901137,-2.011568 6.3333582,-3.017301 10.0302446,-3.017301 4.0234431,0 7.5026256,1.087295 10.4385686,3.261938 2.990113,2.174693 5.273454,5.273556 6.850023,9.296642 1.576568,4.023136 2.365107,8.780181 2.365107,14.271081 v 40.36731 c 0,0.59792 -0.217704,1.11407 -0.652602,1.54897 -0.380727,0.38072 -0.869795,0.57083 -1.467716,0.57083 H 9.5713185 c -0.5979205,0 -1.1145852,-0.19011 -1.5494829,-0.57083 -0.3807272,-0.4349 -0.5708353,-0.95105 -0.5708353,-1.54897 V 109.4647 c 0,-3.96876 -0.7609433,-7.0131 -2.2833409,-9.13337 -1.5223977,-2.120263 -3.7239718,-3.180423 -6.6057443,-3.180423 -3.1531365,0 -5.572415,1.250421 -7.2578354,3.751263 -1.6307387,2.50084 -2.4463637,6.11616 -2.4463637,10.84587 v 36.77927 c 0,0.59792 -0.217704,1.11407 -0.652602,1.54897 -0.380216,0.38072 -0.869796,0.57083 -1.467716,0.57083 h -13.047955 c -0.597921,0 -1.114585,-0.19011 -1.549483,-0.57083 -0.380727,-0.4349 -0.570835,-0.95105 -0.570835,-1.54897 V 109.4647 c 0,-3.96876 -0.760943,-7.0131 -2.283341,-9.13337 -1.522398,-2.120263 -3.723972,-3.180423 -6.605233,-3.180423 -3.153648,0 -5.572926,1.250421 -7.258347,3.751263 -1.630739,2.50084 -2.446364,6.11616 -2.446364,10.84587 v 36.77927 c 0,0.59792 -0.217193,1.11407 -0.652091,1.54897 -0.380727,0.38072 -0.870306,0.57083 -1.468227,0.57083 h -13.047954 c -0.597921,0 -1.114586,-0.19011 -1.549483,-0.57083 -0.380217,-0.4349 -0.570836,-0.95105 -0.570836,-1.54897 z m 97.533104,46.809305 c 0,-4.83855 1.196352,-9.07919 3.588545,-12.7214 2.446364,-3.64271 5.898461,-6.44272 10.356802,-8.40001 4.512,-1.95679 9.785966,-2.93543 15.820364,-2.93543 2.77292,0 5.029176,0.1901 6.768767,0.57083 v -1.7125 c 0,-4.13199 -0.761455,-7.20372 -2.283341,-9.215238 -1.522398,-2.065943 -3.832824,-3.098914 -6.93179,-3.098914 -5.00158,0 -8.209398,2.636778 -9.622943,7.910282 -0.434898,1.5223 -1.331779,2.1747 -2.691154,1.9572 l -11.090659,-1.9572 c -0.815625,-0.16307 -1.38646,-0.51646 -1.712506,-1.06011 -0.271875,-0.54369 -0.29896,-1.277604 -0.08177,-2.201826 1.413546,-5.54538 4.40366,-9.840341 8.970853,-12.884829 4.620852,-3.098864 10.410971,-4.648296 17.369847,-4.648296 8.263568,0 14.542756,2.229017 18.838074,6.687051 4.348977,4.458034 6.523977,10.954773 6.523977,19.49052 v 41.0194 c 0,0.59792 -0.217704,1.11407 -0.652602,1.54897 -0.380727,0.38072 -0.869796,0.57083 -1.467716,0.57083 H 74.321513 c -1.304693,0 -2.120318,-0.70677 -2.446364,-2.1198 l -1.63125,-7.66565 h -0.652091 c -1.468227,3.58803 -3.669801,6.36044 -6.605744,8.31774 -2.881262,1.95729 -6.306273,2.93594 -10.275035,2.93594 -3.968761,0 -7.257835,-0.84271 -10.193778,-2.52813 -2.935943,-1.73959 -5.219284,-4.13178 -6.850023,-7.17658 -1.63125,-3.09845 -2.446875,-6.6594 -2.446875,-10.68285 z m 17.778171,-1.95729 c 0,2.88177 0.733858,5.1922 2.201574,6.93179 1.522397,1.68542 3.560949,2.52813 6.116165,2.52813 3.370841,0 5.953142,-1.49531 7.747414,-4.48543 1.793762,-3.04428 2.691154,-7.39377 2.691154,-13.04744 v -4.3224 c -1.522398,-0.27188 -2.963029,-0.40782 -4.322403,-0.40782 -4.45783,0 -7.991694,1.16876 -10.60108,3.50678 -2.555216,2.28334 -3.832824,5.38231 -3.832824,9.29639 z m 44.852216,8.88908 c 0,-1.19584 0.326046,-2.22917 0.978648,-3.09896 L 124.71906,96.49851 H 98.62315 c -0.597921,0 -1.114586,-0.190262 -1.549483,-0.570836 -0.380216,-0.434948 -0.570836,-0.951409 -0.570836,-1.549431 v -9.459768 c 0,-0.598022 0.19062,-1.087295 0.570836,-1.467869 0.434897,-0.434949 0.951562,-0.652398 1.549483,-0.652398 h 42.89492 c 0.59843,0 1.0875,0.217449 1.46823,0.652398 0.4349,0.380574 0.65209,0.869847 0.65209,1.467869 v 9.867478 c 0,1.196096 -0.32605,2.229017 -0.97865,3.098915 l -25.28028,34.739792 c 1.52239,-0.27136 3.07188,-0.4073 4.64845,-0.4073 3.91459,0 7.50262,0.35313 10.76461,1.05991 3.31616,0.6526 6.74117,1.73959 10.27503,3.26198 1.25053,0.54375 1.87553,1.46772 1.87553,2.77293 v 9.37815 c 0,0.86979 -0.27136,1.46771 -0.81511,1.79376 -0.54375,0.27188 -1.22344,0.24479 -2.03906,-0.0813 -4.07711,-1.52239 -7.69274,-2.5823 -10.84587,-3.18073 -3.09897,-0.65209 -6.4969,-0.97865 -10.19378,-0.97865 -3.69689,0 -7.23075,0.32656 -10.76462,0.97865 -3.47918,0.6526 -7.33909,1.71251 -11.579723,3.18073 -0.815625,0.27188 -1.495313,0.27188 -2.039063,0 -0.543239,-0.32655 -0.815114,-0.89739 -0.815114,-1.7125 z m 52.68089,-21.93704 c 0,-6.63232 1.27761,-12.63993 3.83282,-18.022191 2.55522,-5.43663 6.06148,-9.677216 10.51983,-12.721704 4.51251,-3.098864 9.51409,-4.648296 15.00473,-4.648296 5.49116,0 10.54743,1.549432 15.00525,4.648296 4.51252,3.044488 8.04638,7.285074 10.6016,12.721704 2.60938,5.382261 3.91459,11.389871 3.91459,18.022191 0,6.63283 -1.30521,12.66774 -3.91459,18.10422 -2.55522,5.38231 -6.08908,9.62294 -10.6016,12.72191 -4.45782,3.04428 -9.4594,4.56668 -15.00525,4.56668 -5.54533,0 -10.49222,-1.5224 -15.00473,-4.56668 -4.45835,-3.09897 -7.96461,-7.3396 -10.51983,-12.72191 -2.55521,-5.43648 -3.83282,-11.47139 -3.83282,-18.10422 z m 17.94068,0 c 0,6.90471 0.97865,12.23284 2.93594,15.9839 1.9573,3.69689 4.78388,5.54533 8.48076,5.54533 3.6974,0 6.55158,-1.84844 8.56304,-5.54533 2.01147,-3.75106 3.0172,-9.07919 3.0172,-15.9839 0,-6.90419 -1.00573,-12.20499 -3.0172,-15.90187 -2.01146,-3.751315 -4.86564,-5.626946 -8.56304,-5.626946 -3.69688,0 -6.52346,1.875631 -8.48076,5.626946 -1.95729,3.69688 -2.93594,8.99768 -2.93594,15.90187 z m 49.74494,-31.804055 c 0,-0.598022 0.19062,-1.087295 0.57135,-1.467869 0.4349,-0.434949 0.95105,-0.652398 1.54897,-0.652398 h 10.76462 c 0.59792,0 1.0875,0.190262 1.46822,0.570836 0.4349,0.380573 0.70678,0.897034 0.81512,1.549431 l 1.63125,7.991847 h 0.65209 c 1.46822,-3.696886 3.61563,-6.551114 6.44272,-8.562682 2.88126,-2.011568 6.2521,-3.017301 10.11201,-3.017301 4.13179,0 7.69273,1.087295 10.68285,3.261938 3.04479,2.174693 5.3823,5.273556 7.01355,9.296642 1.63074,4.023136 2.44637,8.780181 2.44637,14.271081 v 40.36731 c 0,0.59792 -0.21771,1.11407 -0.6526,1.54897 -0.38022,0.38072 -0.8698,0.57083 -1.46772,0.57083 H 255.1981 c -0.59793,0 -1.11459,-0.19011 -1.54949,-0.57083 -0.38021,-0.4349 -0.57083,-0.95105 -0.57083,-1.54897 V 109.4647 c 0,-3.96876 -0.81563,-7.0131 -2.44637,-9.13337 -1.63125,-2.120263 -3.94167,-3.180423 -6.93179,-3.180423 -3.31616,0 -5.84429,1.250421 -7.58388,3.751263 -1.7401,2.50084 -2.6099,6.11616 -2.6099,10.84587 v 36.77927 c 0,0.59792 -0.21719,1.11407 -0.65209,1.54897 -0.38072,0.38072 -0.86979,0.57083 -1.46822,0.57083 h -13.04796 c -0.59792,0 -1.11407,-0.19011 -1.54897,-0.57083 -0.38073,-0.4349 -0.57135,-0.95105 -0.57135,-1.54897 z" />
			<path d="m -82.904042,160.02322 c -1.032972,0.81563 -1.250472,2.17449 -0.652398,4.07762 0.543648,1.68542 1.821256,3.77814 3.832824,6.27918 8.100392,10.00367 17.750421,18.32141 28.949933,24.95424 11.199511,6.63283 23.187562,11.52556 35.963642,14.6787 12.8302503,3.20781 25.932376,4.81147 39.306376,4.81147 17.125568,0 33.435512,-2.50054 48.92983,-7.50263 15.494319,-4.94741 28.433425,-11.90628 38.817815,-20.87662 3.64221,-3.20731 5.46357,-5.76253 5.46357,-7.66565 0,-0.70677 -0.29896,-1.41355 -0.89688,-2.12032 -0.81563,-0.81511 -1.79428,-1.08699 -2.93595,-0.81511 -1.14167,0.27136 -2.74532,0.95105 -4.81147,2.03855 -9.948988,5.21928 -22.072465,9.37815 -36.370943,12.47712 -14.243796,3.15313 -29.113108,4.7297 -44.607427,4.7297 -19.1912047,0 -37.7303186,-2.60938 -55.61683,-7.82867 -17.886512,-5.21928 -33.734472,-13.374 -47.54337,-24.46466 -2.011466,-1.57657 -3.642716,-2.58281 -4.892983,-3.01771 -1.196046,-0.4349 -2.174642,-0.35313 -2.935739,0.24479 z" />
			<path d="m 98.295571,164.10544 c -1.141671,1.0875 -1.440631,2.22917 -0.896881,3.42552 0.380727,0.86979 1.114585,1.35886 2.202085,1.46771 1.086985,0.16303 2.636475,0.0542 4.647935,-0.32604 3.26199,-0.70677 6.65992,-1.19635 10.19378,-1.46823 3.53387,-0.21719 6.60575,-0.16302 9.21513,0.16302 2.6099,0.38073 4.24064,1.03333 4.89324,1.9573 0.97865,1.46823 0.62501,4.56668 -1.06041,9.2969 -1.63074,4.7297 -3.85991,9.32347 -6.687,13.78181 -1.0875,1.79376 -1.73959,3.20731 -1.9573,4.24064 -0.21719,1.03281 0.0547,1.90261 0.81563,2.60938 0.48906,0.48907 1.03281,0.73386 1.63073,0.73386 1.57708,0 3.86042,-1.38646 6.85054,-4.15887 5.38231,-4.67553 9.16045,-10.22086 11.33545,-16.63599 1.19584,-3.31667 1.9297,-6.79585 2.20157,-10.43857 0.27188,-3.6422 -0.13594,-6.2521 -1.22344,-7.82867 -1.03281,-1.52239 -3.31616,-2.77292 -6.85002,-3.75105 -3.47918,-0.97865 -6.98596,-1.46823 -10.51982,-1.46823 -6.90471,0 -13.45577,1.63125 -19.65319,4.89324 -2.28334,1.25052 -3.995848,2.41928 -5.138029,3.50627 z" />
		</g>
	</svg>
);

export const MicrosoftWordmarkLogo: React.FC<IconSvgProps> = ({
	size = 24,
	color = '#fff',
	...props
}) => (
	<svg
		viewBox="0 0 604 129"
		width={size}
		height={size}
		color={color}
		{...props}
	>
		<g fill="currentColor">
			<path d="M213.2 74.3l-3.6 10.2h-.3c-.6-2.3-1.7-5.8-3.5-10l-19.3-48.5h-18.9v77.3h12.5v-47.7c0-3 0-6.4-.1-10.6-.1-2.1-.3-3.7-.4-4.9h.3c.6 3 1.3 5.2 1.8 6.6l23.2 56.4h8.8l23-56.9c.5-1.3 1-3.9 1.5-6.1h.3c-.3 5.7-.5 10.8-.6 13.9v49h13.3v-77.2h-18.2l-19.8 48.5zM263.8 47.6h13v55.4h-13zM270.4 24.2c-2.2 0-4 .8-5.5 2.2-1.5 1.4-2.3 3.2-2.3 5.4 0 2.1.8 3.9 2.3 5.3 1.5 1.4 3.3 2.1 5.5 2.1s4.1-.8 5.5-2.1c1.5-1.4 2.3-3.2 2.3-5.3s-.8-3.9-2.3-5.4c-1.3-1.4-3.2-2.2-5.5-2.2M322.9 47.1c-2.4-.5-4.9-.8-7.3-.8-5.9 0-11.3 1.3-15.8 3.9-4.5 2.6-8.1 6.2-10.4 10.7-2.4 4.6-3.6 9.9-3.6 16 0 5.3 1.2 10 3.5 14.3 2.3 4.2 5.5 7.6 9.8 9.9 4.1 2.3 8.9 3.5 14.3 3.5 6.2 0 11.5-1.3 15.7-3.7l.1-.1v-12l-.5.4c-1.9 1.4-4.1 2.6-6.3 3.3-2.3.8-4.4 1.2-6.2 1.2-5.2 0-9.3-1.5-12.2-4.8-3-3.2-4.5-7.6-4.5-13.1 0-5.7 1.5-10.2 4.6-13.5 3.1-3.3 7.2-5 12.2-5 4.2 0 8.5 1.4 12.4 4.2l.5.4v-12.7l-.1-.1c-1.7-.7-3.6-1.5-6.2-2M365.8 46.7c-3.2 0-6.2 1-8.8 3.1-2.2 1.8-3.7 4.4-5 7.5h-.1v-9.7h-13v55.4h13v-28.3c0-4.8 1-8.8 3.2-11.7 2.2-3 5-4.5 8.4-4.5 1.2 0 2.4.3 3.9.5 1.4.4 2.4.8 3.1 1.3l.5.4v-13l-.3-.1c-.9-.6-2.7-.9-4.9-.9M401.2 46.4c-9.1 0-16.4 2.7-21.5 8-5.2 5.3-7.7 12.6-7.7 21.8 0 8.6 2.6 15.6 7.6 20.7 5 5 11.8 7.6 20.3 7.6 8.9 0 16-2.7 21.1-8.1 5.2-5.4 7.7-12.6 7.7-21.5 0-8.8-2.4-15.8-7.3-20.9-4.7-5.1-11.6-7.6-20.2-7.6m10.4 42.6c-2.4 3.1-6.2 4.6-10.9 4.6s-8.5-1.5-11.2-4.8c-2.7-3.1-4-7.6-4-13.3 0-5.9 1.4-10.4 4-13.6 2.7-3.2 6.4-4.8 11.1-4.8 4.6 0 8.2 1.5 10.8 4.6 2.6 3.1 4 7.6 4 13.5-.2 6-1.3 10.7-3.8 13.8M457.7 70.6c-4.1-1.7-6.7-3-7.9-4.1-1-1-1.5-2.4-1.5-4.2 0-1.5.6-3 2.1-4s3.2-1.5 5.7-1.5c2.2 0 4.5.4 6.7 1s4.2 1.5 5.8 2.7l.5.4v-12.2l-.3-.1c-1.5-.6-3.5-1.2-5.9-1.7-2.4-.4-4.6-.6-6.4-.6-6.2 0-11.3 1.5-15.3 4.8-4 3.1-5.9 7.3-5.9 12.2 0 2.6.4 4.9 1.3 6.8.9 1.9 2.2 3.7 4 5.2 1.8 1.4 4.4 3 8 4.5 3 1.3 5.3 2.3 6.7 3.1 1.4.8 2.3 1.7 3 2.4.5.8.8 1.8.8 3.1 0 3.7-2.8 5.5-8.5 5.5-2.2 0-4.5-.4-7.2-1.3s-5.2-2.2-7.3-3.7l-.5-.4v12.7l.3.1c1.9.9 4.2 1.5 7 2.2 2.8.5 5.3.9 7.5.9 6.7 0 12.2-1.5 16.1-4.8 4-3.2 6.1-7.3 6.1-12.6 0-3.7-1-7-3.2-9.5-2.9-2.4-6.5-4.9-11.7-6.9M506.9 46.4c-9.1 0-16.4 2.7-21.5 8s-7.7 12.6-7.7 21.8c0 8.6 2.6 15.6 7.6 20.7 5 5 11.8 7.6 20.3 7.6 8.9 0 16-2.7 21.1-8.1 5.2-5.4 7.7-12.6 7.7-21.5 0-8.8-2.4-15.8-7.3-20.9-4.7-5.1-11.6-7.6-20.2-7.6m10.3 42.6c-2.4 3.1-6.2 4.6-10.9 4.6-4.8 0-8.5-1.5-11.2-4.8-2.7-3.1-4-7.6-4-13.3 0-5.9 1.4-10.4 4-13.6 2.7-3.2 6.4-4.8 11.1-4.8 4.5 0 8.2 1.5 10.8 4.6 2.6 3.1 4 7.6 4 13.5 0 6-1.3 10.7-3.8 13.8M603.9 58.3v-10.7h-13.1v-16.4l-.4.1-12.4 3.7-.3.1v12.5h-19.6v-7c0-3.2.8-5.7 2.2-7.3s3.5-2.4 6.1-2.4c1.8 0 3.7.4 5.8 1.3l.5.3v-11.3l-.3-.1c-1.8-.6-4.2-1-7.3-1-3.9 0-7.3.9-10.4 2.4-3.1 1.7-5.4 4-7.1 7.1-1.7 3-2.6 6.4-2.6 10.3v7.7h-9.1v10.6h9.1v44.8h13.1v-44.7h19.6v28.5c0 11.7 5.5 17.6 16.5 17.6 1.8 0 3.7-.3 5.5-.6 1.9-.4 3.3-.9 4.1-1.3l.1-.1v-10.7l-.5.4c-.8.5-1.5.9-2.7 1.2-1 .3-1.9.4-2.6.4-2.6 0-4.4-.6-5.7-2.1-1.2-1.4-1.8-3.7-1.8-7.1v-26.2h13.3z" />
			<path d="M0 0h61.3v61.3h-61.3z" />
			<path d="M67.7 0h61.3v61.3h-61.3z" />
			<path d="M0 67.7h61.3v61.3h-61.3z" />
			<path d="M67.7 67.7h61.3v61.3h-61.3z" />
		</g>
	</svg>
);

export const DeloitteLogo: React.FC<IconSvgProps> = ({ size = 24, color = '#fff', ...props }) => (
	<svg
		viewBox="0 0 182 34"
		width={size}
		height={size}
		color={color}
		{...props}
	>
		<g fill="currentColor">
			<path d="M171.8,29c0-2.7,2.2-4.8,4.8-4.8c2.7,0,4.8,2.2,4.8,4.8s-2.2,4.8-4.8,4.8S171.8,31.7,171.8,29" />
			<path d="M27.6,16.1c0,5.6-1.4,9.8-4.4,12.8s-7.2,4.5-12.6,4.5H0V0.1h11.2c5.3,0,9.3,1.3,12.1,4.1 C26.2,6.9,27.6,10.9,27.6,16.1 M18.4,16.4c0-3.1-0.6-5.3-1.8-6.8c-1.1-1.4-3-2.2-5.4-2.2H8.8v18.6h2c2.7,0,4.6-0.8,5.9-2.4 C17.8,22,18.4,19.6,18.4,16.4" />
			<rect
				x="56.7"
				width="8.3"
				height="33.4"
			/>
			<path d="M92.4,20.9c0,4-1,7.2-3.2,9.5s-5.2,3.4-9,3.4c-3.7,0-6.6-1.1-8.8-3.5c-2.2-2.4-3.3-5.5-3.3-9.4 c0-4,1-7.2,3.2-9.4c2.2-2.3,5.2-3.4,9-3.4c2.4,0,4.5,0.5,6.3,1.5c1.9,1,3.2,2.5,4.2,4.4C92,16.1,92.4,18.3,92.4,20.9 M76.7,20.9 c0,2.2,0.3,3.7,0.8,4.8c0.5,1.1,1.4,1.6,2.8,1.6s2.2-0.5,2.8-1.6c0.5-1.1,0.8-2.8,0.8-4.8c0-2.2-0.3-3.7-0.8-4.8 c-0.5-1-1.4-1.6-2.8-1.6c-1.2,0-2.2,0.5-2.8,1.6C77,17.2,76.7,18.7,76.7,20.9" />
			<rect
				x="95.8"
				y="8.5"
				width="8.3"
				height="24.8"
			/>
			<rect
				x="95.8"
				width="8.3"
				height="5.6"
			/>
			<path d="M121,27c1.1,0,2.5-0.3,4-0.8v6.3c-1.1,0.5-2.2,0.8-3.2,1c-1,0.2-2.2,0.3-3.6,0.3c-2.8,0-4.8-0.7-6.1-2.2 c-1.2-1.4-1.9-3.6-1.9-6.5V14.9h-2.9V8.5h2.9V2.3l8.4-1.4v7.8h5.4V15h-5.4v9.6C118.8,26.3,119.5,27,121,27" />
			<path d="M140.4,27c1.1,0,2.5-0.3,4-0.8v6.3c-1.1,0.5-2.2,0.8-3.2,1c-1,0.2-2.2,0.3-3.6,0.3c-2.8,0-4.8-0.7-6.1-2.2 c-1.2-1.4-1.9-3.6-1.9-6.5V14.9h-2.9V8.5h2.9V2.2l8.4-1.3v7.8h5.4V15h-5.4v9.6C138.1,26.3,138.9,27,140.4,27" />
			<path d="M166.8,11c-2-2-4.8-2.9-8.4-2.9c-3.8,0-6.8,1.1-8.9,3.4c-2.1,2.3-3.1,5.5-3.1,9.7c0,4,1.1,7.2,3.3,9.4 c2.3,2.2,5.4,3.3,9.4,3.3c2,0,3.6-0.1,5-0.4c1.3-0.3,2.8-0.7,4-1.4l-1.2-5.6c-0.9,0.4-1.9,0.7-2.7,0.9c-1.2,0.3-2.6,0.4-4,0.4 c-1.6,0-2.9-0.4-3.8-1.1c-0.9-0.8-1.4-1.9-1.4-3.3h14.9v-3.9C169.8,15.8,168.7,13,166.8,11 M155,17.9c0.1-1.3,0.5-2.4,1.1-3 c0.6-0.6,1.4-0.9,2.5-0.9c1,0,2,0.3,2.6,1c0.6,0.7,0.9,1.6,1,2.9H155z" />
			<path d="M50.5,11c-2.1-2-4.8-2.9-8.4-2.9c-3.8,0-6.8,1.1-8.9,3.4s-3.1,5.5-3.1,9.7c0,4,1.1,7.2,3.3,9.4 c2.3,2.2,5.4,3.3,9.4,3.3c2,0,3.6-0.1,5-0.4c1.3-0.3,2.8-0.7,4-1.4l-1.2-5.7c-0.9,0.4-1.9,0.7-2.7,0.9c-1.2,0.3-2.6,0.4-4,0.4 c-1.6,0-2.9-0.4-3.8-1.1c-0.9-0.8-1.4-1.9-1.4-3.3h14.9v-3.8C53.5,15.8,52.4,13,50.5,11 M38.6,17.9c0.1-1.3,0.5-2.4,1.1-3 s1.4-0.9,2.5-0.9s2,0.3,2.6,1c0.6,0.7,0.9,1.6,1,2.9H38.6z" />
		</g>
	</svg>
);
