'use client';

import { useState, useEffect, useCallback, memo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@heroui/button';
import { Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from '@heroui/dropdown';
import { IconTrash, IconShare2, IconMessage, IconPhoto, IconVideo } from '@tabler/icons-react';
import { TripleDotIcon } from '@/components/icons';
import { ChatHistory, ChatType } from '@/types/chat';
import { useSidebarVisibility } from '@/app/(post-auth)/providers';
import { addToast } from '@heroui/toast';

interface ChatHistoryItemProps {
	chat: ChatHistory;
	onDelete: (chatType: ChatType, chatId: string) => void;
}

const ChatHistoryItem = memo(({ chat, onDelete }: ChatHistoryItemProps) => {
	const [isDeleting, setIsDeleting] = useState(false);
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);
	const disabledKeys = isDeleting ? ['share', 'delete'] : [];
	const isSidebarOpen = useSidebarVisibility();

	// Close dropdown when sidebar closes (only for desktop)
	useEffect(() => {
		// On desktop, close dropdown when sidebar closes
		// On mobile, this component is unmounted when mobile sidebar closes
		if (
			typeof window !== 'undefined' &&
			window.innerWidth >= 768 &&
			!isSidebarOpen &&
			isDropdownOpen
		) {
			setIsDropdownOpen(false);
		}
	}, [isDropdownOpen, isSidebarOpen]);

	const pathName = usePathname();
	const chatPath = `/${chat.chatType}/${chat.chatId}`;
	const isActive = pathName === chatPath;

	const handleDelete = useCallback(async () => {
		try {
			setIsDeleting(true);
			const queryParams = new URLSearchParams({
				id: chat.chatId,
				type: chat.chatType,
			});
			const response = await fetch(`/api/chat/delete?${queryParams}`, {
				method: 'DELETE',
			});
			if (!response.ok) {
				throw new Error(`Failed to delete chat: ${response.statusText}`);
			}
			onDelete(chat.chatType, chat.chatId);
		} catch (error) {
			console.error('Error deleting chat:', error);
			addToast({
				title: 'Error deleting chat',
				description:
					(error as Error).message || 'An unexpected error occurred. Please try again.',
				color: 'danger',
			});
		} finally {
			setIsDeleting(false);
		}
	}, [chat.chatId, chat.chatType, onDelete]);

	// Function to get appropriate icon based on chat type
	const getChatTypeIcon = useCallback(() => {
		switch (chat.chatType) {
			case 'image':
				return (
					<IconPhoto
						size={16}
						className="text-purple-400"
					/>
				);
			case 'video':
				return (
					<IconVideo
						size={16}
						className="text-orange-400"
					/>
				);
			case 'chat':
			default:
				return (
					<IconMessage
						size={16}
						className="text-blue-400"
					/>
				);
		}
	}, [chat.chatType]);

	return (
		<div
			className={`group relative flex items-center rounded-xl px-2 py-1.5 ${isActive ? 'bg-sidebar-hover shadow-xs' : 'hover:bg-sidebar-hover/50'} ${isDeleting ? 'pointer-events-none opacity-50' : ''}`}
		>
			<div className="mr-0.5 ml-1 shrink-0">{getChatTypeIcon()}</div>
			<Link
				href={chatPath}
				className={`min-w-0 flex-1 py-1 pr-2 pl-1 ${isDeleting ? 'cursor-not-allowed' : ''}`}
			>
				<div className="relative w-full overflow-hidden">
					<span
						className={`text-fade-right block overflow-hidden pr-5 text-sm whitespace-nowrap ${
							isActive
								? 'text-primary-text'
								: 'text-secondary-text hover:text-primary-text'
						}`}
						title={chat.title}
					>
						{chat.title}
					</span>
				</div>
			</Link>
			<Dropdown
				placement="bottom-start"
				isOpen={isDropdownOpen}
				onOpenChange={setIsDropdownOpen}
				classNames={{
					trigger: 'opacity-0 group-hover:opacity-100 transition-opacity',
				}}
			>
				<DropdownTrigger>
					<Button
						isIconOnly
						disableRipple
						variant="light"
						size="sm"
						className="absolute top-1/2 right-1.5 z-10 h-6 w-6 -translate-y-1/2 transform bg-transparent hover:bg-transparent"
					>
						<TripleDotIcon className="h-3.5 w-3.5" />
					</Button>
				</DropdownTrigger>
				<DropdownMenu
					disabledKeys={disabledKeys}
					aria-label="Chat actions"
				>
					<DropdownItem
						key="delete"
						startContent={<IconTrash size={16} />}
						className="text-danger text-xs"
						onPress={handleDelete}
					>
						{isDeleting ? 'Deleting...' : 'Delete'}
					</DropdownItem>
				</DropdownMenu>
			</Dropdown>
		</div>
	);
});

ChatHistoryItem.displayName = 'ChatHistoryItem';
export default ChatHistoryItem;
