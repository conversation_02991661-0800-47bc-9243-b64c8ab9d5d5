import Link from 'next/link';
import { But<PERSON> } from '@heroui/button';
import {
	IconCameraAi,
	IconMessageChatbot,
	IconVideo,
	IconCameraSearch,
	IconCrown,
	IconPhoto,
	IconPhotoEdit,
} from '@tabler/icons-react';
import { UserInSidebar } from './UserInSidebar';
import ChatHistory from './ChatHistory';
import UpgradeToProModal from '../chat/model-selection/UpgradeToProModal';
import ModelSelectionDialog from '../chat/model-selection/ModelSelectionDialog';
import { useDisclosure } from '@heroui/modal';
import { useMediaGenerationModeUpdateContext, useUserContext } from '@/app/(post-auth)/providers';
import { usePathname } from 'next/navigation';
import { ImageModelsList } from '@/models/image/image-generation-models';
import { VideoModelsList } from '@/models/video/video-generation-models';
import { ConversationalModelsList } from '@/models/conversational/conversational-models';
import { ChatType } from '@/types/chat';
import { motion, AnimatePresence } from 'framer-motion';

interface MobileSidebarProps {
	isOpen: boolean;
	onClose: () => void;
}

const MobileSidebar = ({ isOpen, onClose }: MobileSidebarProps) => {
	const user = useUserContext();
	const updateMediaGenerationMode = useMediaGenerationModeUpdateContext();
	const isPro = user?.user_metadata?.is_pro_user || false;
	const pathname = usePathname();

	let currentType: ChatType = 'chat';
	let models: any[] = ConversationalModelsList;
	if (pathname === '/image') {
		currentType = 'image';
		models = ImageModelsList;
	} else if (pathname === '/video') {
		currentType = 'video';
		models = VideoModelsList;
	}

	const {
		isOpen: isExploreOpen,
		onOpen: onExploreOpen,
		onOpenChange: onExploreOpenChange,
	} = useDisclosure();

	const {
		isOpen: isUpgradeModalOpen,
		onOpen: onUpgradeModalOpen,
		onOpenChange: onUpgradeModalOpenChange,
	} = useDisclosure();

	return (
		<AnimatePresence>
			{isOpen && (
				<div className="md:hidden">
					{/* Overlay with fade-in */}
					<motion.div
						className="fixed inset-0 bg-black/50 z-40"
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						exit={{ opacity: 0 }}
						transition={{ duration: 0.2 }}
						onClick={onClose}
					/>
					{/* Sidebar with slide-in */}
					<motion.aside
						className="fixed left-0 top-0 h-full w-[280px] z-50 bg-[#090909] flex flex-col py-4 overflow-hidden"
						initial={{ x: -280 }}
						animate={{ x: 0 }}
						exit={{ x: -280 }}
						transition={{ type: 'tween', duration: 0.3, ease: 'easeInOut' }}
					>
						<div className="flex flex-col gap-1 px-2 h-full">
							<Link
								href="/chat"
								onClick={onClose}
							>
								<Button
									disableRipple
									size="sm"
									variant="light"
									fullWidth
									className="justify-start"
									startContent={
										<IconMessageChatbot
											className="text-blue-500"
											size={16}
										/>
									}
								>
									Chat
								</Button>
							</Link>
							<Link
								href="/image"
								onClick={onClose}
							>
								<Button
									disableRipple
									size="sm"
									variant="light"
									fullWidth
									className="justify-start"
									startContent={
										<IconCameraAi
											className="text-purple-500"
											size={16}
										/>
									}
								>
									Images
								</Button>
							</Link>
							<Link
								href="/video"
								onClick={onClose}
							>
								<Button
									disableRipple
									size="sm"
									variant="light"
									fullWidth
									className="justify-start"
									startContent={
										<IconVideo
											className="text-orange-500"
											size={16}
										/>
									}
								>
									Videos
								</Button>
							</Link>
							{currentType === 'image' && (
								<>
									<Button
										disableRipple
										size="sm"
										variant="light"
										fullWidth
										startContent={
											<IconPhoto
												size={16}
												className="text-purple-500"
											/>
										}
										onPress={() => {
											onClose();
											updateMediaGenerationMode('CREATE');
										}}
										className="justify-start text-white/70 hover:text-white/90 hover:bg-white/5"
									>
										Create Image
									</Button>
									<Button
										disableRipple
										size="sm"
										variant="light"
										fullWidth
										className="justify-start text-white/70 hover:text-white/90 hover:bg-white/5"
										onPress={() => {
											if (!isPro) {
												onUpgradeModalOpen();
											} else {
												onClose();
												updateMediaGenerationMode('EDIT');
											}
										}}
										startContent={
											<IconPhotoEdit
												size={16}
												className="text-purple-500"
											/>
										}
										endContent={
											!isPro ? (
												<IconCrown
													size={14}
													color="#9455D3"
													fill="#9455D3"
												/>
											) : undefined
										}
									>
										Edit Image
									</Button>
								</>
							)}
							{/* Explore Ideas for mobile (only for image or video) */}
							{(currentType === 'image' || currentType === 'video') && (
								<Button
									disableRipple
									size="sm"
									variant="light"
									fullWidth
									className="justify-start text-white/70 hover:text-white/90 hover:bg-white/5 mb-2"
									onPress={onExploreOpen}
									startContent={<IconCameraSearch size={16} />}
								>
									Explore Ideas
								</Button>
							)}
							<ChatHistory />
							<UserInSidebar />
						</div>
					</motion.aside>
					{/* Explore Modal */}
					<ModelSelectionDialog
						isOpen={isExploreOpen}
						onOpenChange={onExploreOpenChange}
						type={currentType}
						models={models}
						selectedModel={''}
						onModelSelect={() => {}}
					/>
					{/* Upgrade to Pro Modal for Edit Feature */}
					<UpgradeToProModal
						isOpen={isUpgradeModalOpen}
						onOpenChange={onUpgradeModalOpenChange}
						modelName="Flux 1.1 Pro"
						modelType="image"
					/>
				</div>
			)}
		</AnimatePresence>
	);
};

export default MobileSidebar;
