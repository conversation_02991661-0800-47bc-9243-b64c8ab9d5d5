import { useState } from 'react';
import { IconMessageCircle, IconCheck } from '@tabler/icons-react';
import { Button } from '@heroui/button';
import { Textarea } from '@heroui/input';
import {
	Modal,
	ModalBody,
	ModalContent,
	Mo<PERSON>Footer,
	ModalHeader,
	useDisclosure,
} from '@heroui/modal';
import { Tooltip } from '@heroui/tooltip';
import { submitFeedback } from '@/app/db-actions';

type FeedbackState = 'none' | 'submitting' | 'success' | 'error';

const MIN_FEEDBACK_LENGTH = 10;
const MAX_FEEDBACK_LENGTH = 1000;

export const FeedbackButton = () => {
	const { isOpen, onOpen, onOpenChange } = useDisclosure();
	const [feedbackState, setFeedbackState] = useState<FeedbackState>('none');
	const [feedbackText, setFeedbackText] = useState<string>('');
	const [errorMessage, setErrorMessage] = useState<string>('');

	const validateFeedbackText = (text: string): string => {
		const trimmedText = text.trim();

		if (trimmedText.length === 0) {
			return 'Please enter your feedback before submitting.';
		}

		if (trimmedText.length < MIN_FEEDBACK_LENGTH) {
			return `Feedback must be at least ${MIN_FEEDBACK_LENGTH} characters long`;
		}

		if (trimmedText.length > MAX_FEEDBACK_LENGTH) {
			return `Feedback cannot exceed ${MAX_FEEDBACK_LENGTH} characters`;
		}

		return '';
	};

	const isValidFeedback = (text: string): boolean => {
		const trimmedText = text.trim();
		return (
			trimmedText.length >= MIN_FEEDBACK_LENGTH && trimmedText.length <= MAX_FEEDBACK_LENGTH
		);
	};

	const openFeedbackModal = () => {
		onOpen();
		setFeedbackState('none');
		setFeedbackText('');
		setErrorMessage('');
	};

	const handleFeedbackTextChange = (value: string) => {
		setFeedbackText(value);

		// Real-time validation
		if (value.trim().length > 0) {
			const validationError = validateFeedbackText(value);
			setErrorMessage(validationError);
		} else {
			setErrorMessage('');
		}
	};

	const handleSubmitFeedback = async () => {
		const validationError = validateFeedbackText(feedbackText);
		if (validationError) {
			setErrorMessage(validationError);
			return;
		}

		try {
			setFeedbackState('submitting');
			setErrorMessage('');

			await submitFeedback(feedbackText.trim());

			setFeedbackState('success');
			setTimeout(() => {
				onOpenChange();
				setFeedbackState('none');
				setFeedbackText('');
			}, 2000);
		} catch (error) {
			console.error('Failed to submit feedback:', error);
			setFeedbackState('error');
			setErrorMessage('Failed to submit feedback. Please try again.');
		}
	};

	const handleModalClose = () => {
		if (feedbackState !== 'submitting') {
			setFeedbackState('none');
			setFeedbackText('');
			setErrorMessage('');
		}
	};

	return (
		<>
			<Tooltip
				content="Send Feedback"
				placement="bottom"
				size="sm"
				radius="sm"
				className="bg-gray-50 text-[#090909]"
				delay={500}
				closeDelay={0}
			>
				<Button
					isIconOnly
					disableRipple
					size="sm"
					variant="light"
					onPress={openFeedbackModal}
				>
					<IconMessageCircle size={18} />
				</Button>
			</Tooltip>
			<Modal
				isOpen={isOpen}
				onOpenChange={onOpenChange}
				onClose={handleModalClose}
				classNames={{
					backdrop: 'backdrop-blur-md',
					base: 'bg-[#090909]',
					header: 'p-6 border-b border-default-50',
					body: 'flex flex-col p-6 text-sm text-white/60 gap-6',
					footer: 'flex  pt-0 gap-4',
				}}
			>
				<ModalContent>
					<>
						<ModalHeader>
							{feedbackState === 'success'
								? 'Thank you for your feedback!'
								: 'Send us your feedback'}
						</ModalHeader>
						<ModalBody>
							{feedbackState === 'success' ? (
								<div className="flex flex-col items-center gap-4">
									<IconCheck
										size={48}
										className="text-green-500"
									/>
									<span className="text-center">
										Your feedback has been submitted successfully. We appreciate
										your input!
									</span>
								</div>
							) : (
								<>
									<span>
										Help us improve by sharing your thoughts, suggestions, or
										reporting any issues you&apos;ve encountered.
									</span>
									<div className="space-y-2">
										<Textarea
											placeholder="Enter your feedback here..."
											value={feedbackText}
											onChange={(e) =>
												handleFeedbackTextChange(e.target.value)
											}
											minRows={4}
											maxRows={8}
											isDisabled={feedbackState === 'submitting'}
											isInvalid={!!errorMessage}
											errorMessage={errorMessage}
											classNames={{
												base: 'w-full',
												inputWrapper: 'bg-default-50',
											}}
										/>
										<div className="flex justify-end">
											<span
												className={`text-xs ${
													feedbackText.length > MAX_FEEDBACK_LENGTH
														? 'text-red-400'
														: 'text-white/40'
												}`}
											>
												{feedbackText.length}/{MAX_FEEDBACK_LENGTH}
											</span>
										</div>
									</div>
								</>
							)}
						</ModalBody>
						{feedbackState !== 'success' && (
							<ModalFooter>
								<Button
									variant="flat"
									onPress={() => onOpenChange()}
									isDisabled={feedbackState === 'submitting'}
								>
									Cancel
								</Button>
								<Button
									color="primary"
									variant="flat"
									onPress={handleSubmitFeedback}
									isLoading={feedbackState === 'submitting'}
									isDisabled={
										!isValidFeedback(feedbackText) ||
										feedbackState === 'submitting'
									}
								>
									{feedbackState === 'submitting'
										? 'Submitting...'
										: 'Submit Feedback'}
								</Button>
							</ModalFooter>
						)}
					</>
				</ModalContent>
			</Modal>
		</>
	);
};
