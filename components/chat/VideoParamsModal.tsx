import { useEffect, useState } from 'react';
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON>dal<PERSON>eader,
	<PERSON>dalBody,
	ModalFooter,
	useDisclosure,
} from '@heroui/modal';
import { Button } from '@heroui/button';
import { Select, SelectItem } from '@heroui/select';
import {
	VIDEO_MODEL_CONSTRAINTS,
	VideoGenerationParams,
} from '@/models/video/video-generation-constraints';
import { VideoModelDisplayName } from '@/models/video/video-generation-models';
import { IconInfoCircle } from '@tabler/icons-react';
import ModelParamsInfo from './ModelParamsInfo';

interface VideoParamsModalProps {
	model: VideoModelDisplayName;
	isOpen: boolean;
	onOpenChange: () => void;
	onApply: (params: VideoGenerationParams) => void;
	onReset?: () => void;
	defaultParams?: VideoGenerationParams;
}

const VideoParamsModal = ({
	model,
	isOpen,
	onOpenChange,
	onApply,
	onReset,
	defaultParams,
}: VideoParamsModalProps) => {
	const modelConstraints = VIDEO_MODEL_CONSTRAINTS[model];
	const infoDrawerDisclosure = useDisclosure();

	const [aspectRatio, setAspectRatio] = useState<`${number}:${number}`>();
	const [duration, setDuration] = useState<string>();
	const [resolution, setResolution] = useState<`${number}p`>();

	const resetValues = () => {
		setAspectRatio(undefined);
		setDuration(undefined);
		setResolution(undefined);
		onReset?.();
	};

	const handleApply = () => {
		const params: VideoGenerationParams = {
			aspectRatio,
			duration,
			resolution,
			generateAudio: false,
		};
		onApply(params);
	};

	useEffect(() => {
		if (defaultParams) {
			setAspectRatio(defaultParams.aspectRatio);
			setDuration(defaultParams.duration);
			setResolution(defaultParams.resolution);
		}
	}, [defaultParams]);

	return (
		<>
			<Modal
				isOpen={isOpen}
				onOpenChange={onOpenChange}
				isDismissable={false}
				scrollBehavior="outside"
				size="md"
				classNames={{
					header: 'flex flex-row gap-1 items-center justify-center',
					body: 'flex flex-col',
				}}
			>
				<ModalContent>
					{(onClose) => (
						<>
							<ModalHeader>
								<span>Video Generation Parameters</span>
								<IconInfoCircle
									size={20}
									className="cursor-pointer"
									onClick={infoDrawerDisclosure.onOpen}
								/>
							</ModalHeader>
							<ModalBody>
								{modelConstraints.aspectRatio && (
									<Select
										className="max-w-md"
										label="Aspect Ratio"
										variant="bordered"
										selectedKeys={[aspectRatio || '']}
										onChange={(e) =>
											setAspectRatio(e.target.value as `${number}:${number}`)
										}
									>
										{modelConstraints.aspectRatio.map((value) => (
											<SelectItem key={value}>{value}</SelectItem>
										))}
									</Select>
								)}

								{modelConstraints.duration && (
									<Select
										className="max-w-md"
										label="Duration"
										variant="bordered"
										selectedKeys={[duration || '']}
										onChange={(e) => setDuration(e.target.value as `${number}`)}
									>
										{modelConstraints.duration.map((value) => (
											<SelectItem
												key={value}
											>{`${value} seconds`}</SelectItem>
										))}
									</Select>
								)}

								{modelConstraints.resolution && (
									<Select
										className="max-w-md"
										label="Resolution"
										variant="bordered"
										selectedKeys={[resolution || '']}
										onChange={(e) =>
											setResolution(e.target.value as `${number}p`)
										}
									>
										{modelConstraints.resolution.map((value) => (
											<SelectItem key={value}>{value}</SelectItem>
										))}
									</Select>
								)}
							</ModalBody>
							<ModalFooter>
								<Button
									disableRipple
									variant="flat"
									onPress={onClose}
								>
									Cancel
								</Button>
								<Button
									disableRipple
									variant="flat"
									onPress={() => {
										resetValues();
									}}
								>
									Reset
								</Button>
								<Button
									disableRipple
									color="primary"
									variant="flat"
									onPress={() => {
										handleApply();
										onClose();
									}}
								>
									Apply
								</Button>
							</ModalFooter>
						</>
					)}
				</ModalContent>
			</Modal>
			<ModelParamsInfo
				type="video"
				isOpen={infoDrawerDisclosure.isOpen}
				onClose={infoDrawerDisclosure.onClose}
			/>
		</>
	);
};

export default VideoParamsModal;
