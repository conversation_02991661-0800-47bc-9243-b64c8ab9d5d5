import { Attachment } from 'ai';
import { useState, useCallback, useEffect, Dispatch, SetStateAction } from 'react';
import { Modal, ModalContent, ModalBody, ModalFooter, ModalHeader } from '@heroui/modal';
import { Button } from '@heroui/button';
import { Tooltip } from '@heroui/tooltip';
import { IconChevronLeft, IconChevronRight, IconDownload, IconX } from '@tabler/icons-react';
import { getProviderIcon } from '../../utils/model-selection-utils';
import { cn } from '@/utils/cn';

interface VideoGalleryModalProps {
	isOpen: boolean;
	onOpenChange: (isOpen: boolean) => void;
	videos: Attachment[];
	selectedVideoIndex: number;
	setSelectedVideoIndex: Dispatch<SetStateAction<number>>;
	prompt?: string;
	modelProvider?: string;
}

const VideoGalleryModal = ({
	isOpen,
	onOpenChange,
	videos,
	selectedVideoIndex,
	setSelectedVideoIndex,
	prompt,
	modelProvider,
}: VideoGalleryModalProps) => {
	const handlePrevious = useCallback(() => {
		setSelectedVideoIndex((prevIndex) => (prevIndex - 1 + videos.length) % videos.length);
	}, [videos.length, setSelectedVideoIndex]);

	const handleNext = useCallback(() => {
		setSelectedVideoIndex((prevIndex) => (prevIndex + 1) % videos.length);
	}, [videos.length, setSelectedVideoIndex]);

	const [isDownloading, setIsDownloading] = useState(false);
	const handleDownload = useCallback(async () => {
		if (!videos[selectedVideoIndex]) return;

		try {
			setIsDownloading(true);
			const videoUrl = videos[selectedVideoIndex].url;
			const videoName =
				videos[selectedVideoIndex].name || `generated-video-${selectedVideoIndex + 1}.mp4`;

			const response = await fetch(videoUrl);
			if (!response.ok) {
				throw new Error(`Failed to fetch video: ${response.statusText}`);
			}

			const blob = await response.blob();
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.style.display = 'none';
			a.href = url;
			a.download = videoName;
			document.body.appendChild(a);
			a.click();
			window.URL.revokeObjectURL(url);
			document.body.removeChild(a);
		} catch (err) {
			console.error('Failed to download video:', err);
		} finally {
			setIsDownloading(false);
		}
	}, [videos, selectedVideoIndex]);

	// Handle keyboard navigation
	useEffect(() => {
		const handleKeyDown = (e: KeyboardEvent) => {
			if (!isOpen) {
				return;
			}
			if (e.key === 'ArrowRight') {
				handleNext();
			} else if (e.key === 'ArrowLeft') {
				handlePrevious();
			}
		};

		window.addEventListener('keydown', handleKeyDown);

		return () => {
			window.removeEventListener('keydown', handleKeyDown);
		};
	}, [isOpen, handleNext, handlePrevious]);

	if (videos.length === 0) {
		return null;
	}

	const currentVideo = videos[selectedVideoIndex];

	return (
		<Modal
			hideCloseButton
			isOpen={isOpen}
			onOpenChange={onOpenChange}
			size="full"
			classNames={{
				base: 'bg-black',
				body: 'p-0 relative flex grow flex-col items-center justify-center',
				header: 'px-4 py-3 flex justify-between items-center',
				footer: 'flex flex-col items-center p-6 gap-6',
			}}
		>
			<ModalContent>
				{(onClose) => (
					<>
						<ModalHeader>
							{/* Left section with model provider */}
							<div className="flex items-center">
								{modelProvider && (
									<div className="rounded-full bg-black/50 p-2">
										{getProviderIcon(modelProvider, 'video', 20)}
									</div>
								)}
							</div>

							{/* Right section with action buttons */}
							<div className="flex gap-2">
								<Tooltip
									content="Download video"
									placement="bottom"
									size="sm"
									radius="sm"
									className="bg-gray-50 text-[#090909]"
									delay={500}
									closeDelay={0}
								>
									<Button
										isIconOnly
										variant="flat"
										size="sm"
										radius="full"
										className="bg-black/50 text-white"
										onPress={handleDownload}
										isLoading={isDownloading}
									>
										<IconDownload size={18} />
									</Button>
								</Tooltip>
								<Button
									isIconOnly
									variant="flat"
									size="sm"
									radius="full"
									className="bg-black/50 text-white"
									onPress={onClose}
								>
									<IconX size={18} />
								</Button>
							</div>
						</ModalHeader>

						<ModalBody>
							{videos.length > 1 && (
								<div className="absolute inset-0 z-10 flex items-center justify-between px-4">
									<div className="pointer-events-auto">
										<Button
											isIconOnly
											radius="full"
											variant="flat"
											className="bg-black/50 text-white hover:bg-black/70"
											onPress={handlePrevious}
										>
											<IconChevronLeft size={24} />
										</Button>
									</div>
									<div className="pointer-events-auto">
										<Button
											isIconOnly
											radius="full"
											variant="flat"
											className="bg-black/50 text-white hover:bg-black/70"
											onPress={handleNext}
										>
											<IconChevronRight size={24} />
										</Button>
									</div>
								</div>
							)}

							<video
								src={currentVideo.url}
								controls
								autoPlay
								playsInline
								className="max-h-[calc(100vh-200px)] max-w-full object-contain"
								aria-label="Video player"
							>
								Your browser does not support the video tag.
							</video>
						</ModalBody>

						<ModalFooter>
							<Tooltip
								content={prompt}
								placement="top"
								size="md"
								radius="sm"
								className="max-w-lg bg-black/90 text-white/90"
								delay={500}
								closeDelay={0}
							>
								<div className="w-full max-w-3xl truncate text-center text-sm text-white/80">
									{prompt}
								</div>
							</Tooltip>

							{/* Thumbnail dots for multiple videos */}
							{videos.length > 1 && (
								<div className="flex justify-center">
									<div className="flex gap-2 rounded-full bg-black/50 px-4 py-1.5">
										{videos.map((_, index) => (
											<div
												key={index}
												className={cn(
													'h-2.5 w-2.5 cursor-pointer rounded-full transition-all',
													index === selectedVideoIndex
														? 'scale-110 bg-white'
														: 'scale-90 bg-white/50 hover:bg-white/80'
												)}
												onClick={() => {
													setSelectedVideoIndex(index);
												}}
											/>
										))}
									</div>
								</div>
							)}
						</ModalFooter>
					</>
				)}
			</ModalContent>
		</Modal>
	);
};

export default VideoGalleryModal;
