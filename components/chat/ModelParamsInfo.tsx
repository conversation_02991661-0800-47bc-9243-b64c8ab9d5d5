import { memo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@heroui/drawer';
import { IconAdjustmentsExclamation } from '@tabler/icons-react';
import { ChatType } from '@/types/chat';

const CONVERSATIONAL_PARAMS_INFO = {
	temperature:
		'Controls how creative or predictable the responses are. 0.2 = very predictable, 0.8 = more creative and varied, 1.0 = maximum creativity.',
	topP: 'Helps control variety in a different way than temperature. Lower = more focused, Higher = more variety. Most users should just use temperature instead.',
	topK: 'Limits the words the AI can choose from. Lower numbers = more focused and predictable responses, Higher numbers = more creative responses.',
	presence_Penalty:
		'Makes the AI talk about new things instead of repeating itself. Higher values encourage the AI to change topics more often.',
	frequency_Penalty:
		'Prevents the AI from using the same words too often. Higher values help avoid repetitive text.',
	reasoning_Effort:
		'Controls how much the AI thinks before responding. Higher = more detailed and thought-out responses, Lower = quicker, simpler responses.',
};

const IMAGE_PARAMS_INFO = {
	number_Of_Images: 'How many different images you want to create at once',
	quality: 'How good-looking you want the images to be. Better quality takes longer to create.',
	style: 'The overall look and feel you want for your images (like cartoon, realistic, painting, etc.)',
	background:
		'A transparent background means the background pixels are rendered as see-through, allowing any underlying background to be visible. An opaque background, on the other hand, is solid and does not allow the underlying background to show through.',
	aspect_Ratio: 'The shape of your image (square, wide, tall, etc.)',
	size: 'How big you want your image to be in pixels (larger = more detailed but takes longer)',
	number_Of_Inference_Steps:
		'How many times the AI refines the image. More steps = prettier images but takes longer to create.',
	guidance_Scale:
		'How closely the AI should follow your description. Higher numbers make the AI stick more strictly to what you asked for.',
};

const VIDEO_PARAMS_INFO = {
	aspect_Ratio: 'The shape of your video (square, wide, tall, etc.)',
	duration: 'How long the generated video will be in seconds',
	resolution:
		'The quality and clarity of your video (higher resolution = more detailed but may take longer to generate)',
};

interface IModelParamsInfo {
	type: ChatType;
	isOpen: boolean;
	onClose: () => void;
}

const ModelParamsInfo = memo(({ type, isOpen, onClose }: IModelParamsInfo) => {
	let paramsInfo;
	if (type === 'chat') {
		paramsInfo = CONVERSATIONAL_PARAMS_INFO;
	} else if (type === 'image') {
		paramsInfo = IMAGE_PARAMS_INFO;
	} else {
		paramsInfo = VIDEO_PARAMS_INFO;
	}

	return (
		<Drawer
			isOpen={isOpen}
			onClose={onClose}
			size="xs"
			classNames={{
				header: 'flex flex-row items-center gap-1',
				body: 'flex flex-col gap-5',
			}}
		>
			<DrawerContent>
				<DrawerHeader>Parameter Guide</DrawerHeader>
				<DrawerBody>
					{Object.entries(paramsInfo).map(([key, description]) => (
						<div
							key={key}
							className="flex flex-col gap-1"
						>
							<h3 className="flex flex-row items-center gap-1 font-semibold text-primary-600">
								<IconAdjustmentsExclamation size={16} />
								{key.charAt(0).toUpperCase() + key.split('_').join(' ').slice(1)}
							</h3>
							<p className="text-sm text-foreground/80">{description}</p>
						</div>
					))}
				</DrawerBody>
			</DrawerContent>
		</Drawer>
	);
});

ModelParamsInfo.displayName = 'ModelParamsInfo';
export default ModelParamsInfo;
