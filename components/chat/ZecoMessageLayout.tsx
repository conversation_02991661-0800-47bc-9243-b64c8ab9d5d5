import { memo } from 'react';
import { ToolInvocation } from 'ai';
import { motion } from 'framer-motion';
import {
	IconFileText,
	IconPhoto,
	IconVideo,
	IconCalendarEvent,
	IconNews,
	IconShoppingCart,
	IconMapPin,
	IconMessageCircle,
	IconSchool,
	IconLink,
} from '@tabler/icons-react';
import { useDisclosure } from '@heroui/modal';
import { Tabs, Tab } from '@heroui/tabs';
import { Button } from '@heroui/button';
import { Drawer, DrawerContent, DrawerHeader, DrawerBody } from '@heroui/drawer';
import Link from 'next/link';
import { Markdown } from './Markdown';
import { SerpApiResponse, OrganicResult } from '@/types/web-search-results';
import { OnlineDiscussions } from './web-search/OnlineDiscussions';
import { OnlineEventsFound } from './web-search/OnlineEventsFound';
import { OnlineImagesFound } from './web-search/OnlineImagesFound';
import { OnlineVideosFound } from './web-search/OnlineVideosFound';
import { OnlineNearbyPlacesFound } from './web-search/OnlineNearbyPlacesFound';
import { OnlineNews } from './web-search/OnlineNews';
import { OnlineArticles } from './web-search/OnlineArticles';
import { OnlineShoppingResults } from './web-search/OnlineShoppingResults';
import { OnlineNewsFeed } from './web-search/OnlineNewsFeed';

interface SourcesDrawerProps {
	isOpen: boolean;
	onClose: () => void;
	results?: OrganicResult[];
}

const SourcesDrawer: React.FC<SourcesDrawerProps> = ({ isOpen, onClose, results }) => {
	if (!results || results.length === 0) {
		return null;
	}

	return (
		<Drawer
			isOpen={isOpen}
			onClose={onClose}
			placement="right"
			size="md"
			classNames={{
				base: 'bg-[#090909] text-white',
				header: 'flex flex-row items-center pb-0 gap-2',
				body: 'p-4 space-y-4 overflow-y-auto',
			}}
		>
			<DrawerContent>
				<DrawerHeader>
					<IconLink
						size={18}
						className="text-zeco-purple"
					/>
					Sources
				</DrawerHeader>
				<DrawerBody>
					{results.map((result, index) => (
						<div
							key={index}
							className="p-3 rounded-md bg-[#101010] hover:bg-sidebar-hover/50 transition-colors"
						>
							<Link
								href={result.link}
								target="_blank"
								rel="noopener noreferrer"
								className="block mb-1 text-sm font-medium text-gray-100 hover:underline"
							>
								{result.title}
							</Link>
							<Link
								href={result.link}
								target="_blank"
								rel="noopener noreferrer"
								className="text-xs text-zeco-purple hover:underline block truncate"
							>
								{result.displayed_link || result.link}
							</Link>
							{result.snippet && (
								<p className="mt-2 text-xs text-zinc-400">{result.snippet}</p>
							)}
						</div>
					))}
				</DrawerBody>
			</DrawerContent>
		</Drawer>
	);
};

interface ZecoMessageLayoutProps {
	content?: string;
	toolInvocations?: Array<ToolInvocation>;
}

export const ZecoMessageLayout = memo<ZecoMessageLayoutProps>(({ content, toolInvocations }) => {
	const {
		isOpen: isSourcesDrawerOpen,
		onOpen: openSourcesDrawer,
		onClose: closeSourcesDrawer,
	} = useDisclosure();

	if (!content) {
		return null;
	}

	const webSearchToolInvocation = toolInvocations?.find(
		(invocation) => invocation.toolName === 'useWebSearch'
	);
	const webSearchResults =
		webSearchToolInvocation?.state === 'result'
			? (webSearchToolInvocation.result as SerpApiResponse)
			: null;

	const {
		discussions_and_forums,
		events_results,
		inline_images,
		inline_videos,
		latest_posts,
		local_news,
		local_results,
		news_results,
		shopping_results,
		scholarly_articles,
		organic_results,
	} = webSearchResults || {};

	const hasEvents = (events_results?.length ?? 0) > 0;
	const hasLocalResults = !!local_results;
	const hasShopping = (shopping_results?.length ?? 0) > 0;
	const hasNews = (news_results?.length ?? 0) > 0 || (local_news?.length ?? 0) > 0;
	const hasAcademic = !!scholarly_articles;
	const hasDiscussions =
		(discussions_and_forums?.length ?? 0) > 0 || (latest_posts?.length ?? 0) > 0;
	const hasImages = (inline_images?.length ?? 0) > 0;
	const hasVideos = (inline_videos?.length ?? 0) > 0;
	const hasOrganicResults = (organic_results?.length ?? 0) > 0;

	const hasAnyTabContent =
		hasEvents ||
		hasLocalResults ||
		hasShopping ||
		hasNews ||
		hasAcademic ||
		hasDiscussions ||
		hasImages ||
		hasVideos;

	// If no tab content is available, render just the markdown and potentially the sources button
	if (!hasAnyTabContent) {
		return (
			<div className="px-3 prose prose-invert max-w-none">
				<Markdown content={content} />
				{hasOrganicResults && (
					<div className="mt-4">
						<Button
							disableRipple
							size="sm"
							variant="flat"
							onPress={openSourcesDrawer}
							startContent={
								<IconLink
									size={14}
									className="text-zeco-purple"
								/>
							}
							className="bg-[#101010] hover:bg-sidebar-hover/50 hover:translate-y-[-2px] transition-all duration-300"
						>
							Sources ({organic_results?.length})
						</Button>
					</div>
				)}
				<SourcesDrawer
					isOpen={isSourcesDrawerOpen}
					onClose={closeSourcesDrawer}
					results={organic_results}
				/>
			</div>
		);
	}

	// If there are search results, show the tabbed interface
	return (
		<>
			<Tabs
				aria-label="Search Results"
				classNames={{
					cursor: 'bg-zeco-purple/80',
					tabList: 'bg-transparent px-0',
					tabContent: 'group-data-[selected=true]:text-zeco-purple/80',
				}}
				size="md"
				variant="underlined"
			>
				{/* Summary Tab */}
				<Tab
					key="summary"
					title={
						<div className="flex flex-row items-center gap-1">
							<IconFileText size={16} />
							<span>Summary</span>
						</div>
					}
				>
					{!!content && (
						<motion.div
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.3 }}
							className="px-3 prose prose-invert max-w-none"
						>
							<Markdown content={content} />
							{hasOrganicResults && (
								<div className="mt-4 not-prose">
									<Button
										disableRipple
										size="sm"
										variant="flat"
										onPress={openSourcesDrawer}
										startContent={
											<IconLink
												size={14}
												className="text-zeco-purple"
											/>
										}
										className="bg-[#101010] hover:bg-sidebar-hover/50 hover:translate-y-[-2px] transition-all duration-300"
									>
										Sources ({organic_results?.length})
									</Button>
								</div>
							)}
						</motion.div>
					)}
				</Tab>

				{/* Events Tab */}
				{hasEvents && (
					<Tab
						key="events"
						title={
							<div className="flex flex-row items-center gap-1">
								<IconCalendarEvent size={16} />
								<span>Events</span>
							</div>
						}
					>
						<OnlineEventsFound results={events_results!} />
					</Tab>
				)}

				{/* Nearby (Local Results) Tab */}
				{hasLocalResults && (
					<Tab
						key="local"
						title={
							<div className="flex flex-row items-center gap-1">
								<IconMapPin size={16} />
								<span>Nearby</span>
							</div>
						}
					>
						<OnlineNearbyPlacesFound results={local_results} />
					</Tab>
				)}

				{/* Shopping Tab */}
				{hasShopping && (
					<Tab
						key="shopping"
						title={
							<div className="flex flex-row items-center gap-1">
								<IconShoppingCart size={16} />
								<span>Shopping</span>
							</div>
						}
					>
						<div className="space-y-4">
							{shopping_results && (
								<OnlineShoppingResults results={shopping_results} />
							)}
						</div>
					</Tab>
				)}

				{/* News Tab */}
				{hasNews && (
					<Tab
						key="news"
						title={
							<div className="flex flex-row items-center gap-1">
								<IconNews size={16} />
								<span>News</span>
							</div>
						}
					>
						<div className="space-y-4">
							{news_results && <OnlineNews results={news_results} />}
							{local_news && <OnlineNewsFeed results={local_news} />}
						</div>
					</Tab>
				)}

				{/* Academic Tab */}
				{hasAcademic && (
					<Tab
						key="academic"
						title={
							<div className="flex flex-row items-center gap-1">
								<IconSchool size={16} />
								<span>Articles</span>
							</div>
						}
					>
						<OnlineArticles results={scholarly_articles} />
					</Tab>
				)}

				{/* Discussions Tab */}
				{hasDiscussions && (
					<Tab
						key="discussions"
						title={
							<div className="flex flex-row items-center gap-1">
								<IconMessageCircle size={16} />
								<span>Discussions</span>
							</div>
						}
					>
						<div className="space-y-4">
							{discussions_and_forums && (
								<OnlineDiscussions results={discussions_and_forums} />
							)}
						</div>
					</Tab>
				)}

				{/* Images Tab */}
				{hasImages && (
					<Tab
						key="images"
						title={
							<div className="flex flex-row items-center gap-1">
								<IconPhoto size={16} />
								<span>Images</span>
							</div>
						}
					>
						<OnlineImagesFound results={inline_images!} />
					</Tab>
				)}

				{/* Videos Tab */}
				{hasVideos && (
					<Tab
						key="videos"
						title={
							<div className="flex flex-row items-center gap-1">
								<IconVideo size={16} />
								<span>Videos</span>
							</div>
						}
					>
						<OnlineVideosFound results={inline_videos!} />
					</Tab>
				)}
			</Tabs>
			<SourcesDrawer
				isOpen={isSourcesDrawerOpen}
				onClose={closeSourcesDrawer}
				results={organic_results}
			/>
		</>
	);
});

ZecoMessageLayout.displayName = 'ZecoMessageLayout';
