import { Attachment } from 'ai';
import { useState } from 'react';
import { Button } from '@heroui/button';
import { Tooltip } from '@heroui/tooltip';
import { useDisclosure } from '@heroui/modal';
import { CheckIcon, CopyIcon } from '../icons';
import ImageGalleryModal from './ImageGalleryModal';
import { cn } from '@/utils/cn';
import { ProgressiveImage } from '../ui/ProgressiveImage';

interface ImageLayoutProps {
	images: Attachment[];
	prompt?: string;
	modelProvider?: string;
}

export const ImageLayout = ({ images, prompt, modelProvider }: ImageLayoutProps) => {
	const {
		isOpen: isGalleryOpen,
		onOpen: onGalleryOpen,
		onOpenChange: onGalleryOpenChange,
	} = useDisclosure();
	const [selectedImageIndex, setSelectedImageIndex] = useState<number>(0);

	const [isCopied, setIsCopied] = useState(false);
	const handleImageCopyToClipboard = async (imageUrl: string) => {
		try {
			const response = await fetch(imageUrl);
			if (!response.ok) {
				throw new Error(`Failed to fetch image: ${response.statusText}`);
			}

			const blob = await response.blob();

			// Convert the blob to an image/png blob using a canvas
			const img = await createImageBitmap(blob);
			const canvas = document.createElement('canvas');
			canvas.width = img.width;
			canvas.height = img.height;
			const ctx = canvas.getContext('2d');
			if (!ctx) {
				throw new Error('Could not get canvas context');
			}
			ctx.drawImage(img, 0, 0);

			canvas.toBlob(async (pngBlob) => {
				if (!pngBlob) {
					throw new Error('Failed to convert canvas to blob');
				}
				await navigator.clipboard.write([
					new ClipboardItem({
						'image/png': pngBlob,
					}),
				]);
				setIsCopied(true);
				setTimeout(() => {
					setIsCopied(false);
				}, 2000);
			}, 'image/png');
		} catch (err) {
			console.error('Failed to copy image:', err);
		}
	};

	if (images.length === 0) {
		return null;
	}

	return (
		<>
			<div className="grid grid-cols-1 gap-4 px-3 sm:grid-cols-2 lg:grid-cols-3">
				{images.map((image, index) => (
					<div
						key={image.url}
						className="group/image relative"
					>
						<div
							onClick={() => {
								setSelectedImageIndex(index);
								onGalleryOpen();
							}}
							className={cn(
								'relative block aspect-square cursor-pointer overflow-hidden rounded-lg',
								'transition-all duration-200 hover:scale-[1.02]'
							)}
						>
							<ProgressiveImage
								src={image.url}
								alt={image.name || `Generated image ${index + 1}`}
								className="h-full w-full"
							/>
						</div>
						<Tooltip
							content="Copy image"
							placement="bottom"
							size="sm"
							radius="sm"
							className="bg-gray-50 text-[#090909]"
							delay={500}
							closeDelay={0}
						>
							<Button
								isIconOnly
								onPress={() => handleImageCopyToClipboard(image.url)}
								size="sm"
								variant="flat"
								radius="full"
								className="absolute top-2 right-2 bg-black/50 opacity-0 transition-opacity group-hover/image:opacity-100"
							>
								{isCopied ? (
									<CheckIcon
										size={16}
										className="text-success"
									/>
								) : (
									<CopyIcon
										size={16}
										className="text-secondary-text"
									/>
								)}
							</Button>
						</Tooltip>
					</div>
				))}
			</div>

			<ImageGalleryModal
				isOpen={isGalleryOpen}
				onOpenChange={onGalleryOpenChange}
				images={images}
				selectedImageIndex={selectedImageIndex}
				setSelectedImageIndex={setSelectedImageIndex}
				prompt={prompt}
				modelProvider={modelProvider}
			/>
		</>
	);
};
