import { useState } from 'react';
import { IconCheck, IconCopy, IconLink, IconShare, IconTrash } from '@tabler/icons-react';
import { Button } from '@heroui/button';
import { Input } from '@heroui/input';
import { Spinner } from '@heroui/spinner';
import {
	Modal,
	ModalBody,
	ModalContent,
	ModalFooter,
	ModalHeader,
	useDisclosure,
} from '@heroui/modal';
import { Tooltip } from '@heroui/tooltip';
import { givePublicAccessToChat, revokePublicAccessToChat } from '@/app/db-actions';
import { useSharedChatContext } from '@/app/(post-auth)/providers';

type LinkProcessingState = 'none' | 'create' | 'update' | 'revoke';

export const ShareChatButton = () => {
	const { isOpen, onOpen, onOpenChange } = useDisclosure();
	const { type, chatId, isShareable } = useSharedChatContext();
	const [isModalLoading, setIsModalLoading] = useState<boolean>(false);
	const [linkProcessingState, setLinkProcessingState] = useState<LinkProcessingState>('none');
	const [isLinkAvailable, setIsLinkAvailable] = useState<boolean>(false);
	const [isLinkUpdated, setIsLinkUpdated] = useState<boolean>(false);
	const [isLinkCopied, setIsLinkCopied] = useState<boolean>(false);

	const baseUrl =
		process.env.NODE_ENV === 'development' ? 'http://localhost:3000' : 'https://zeco.ai';

	const handleCopyToClipboard = () => {
		navigator.clipboard.writeText(`${baseUrl}/${type}?shareId=${chatId}`).then(() => {
			setIsLinkCopied(true);
			setTimeout(() => {
				setIsLinkCopied(false);
			}, 5000);
		});
	};

	const openShareLinkModal = async () => {
		try {
			onOpen();
			setIsModalLoading(true);
			const response = await fetch(`/api/chat/share?chatId=${chatId}`);
			if (!response.ok) {
				throw new Error('Failed to fetch share status');
			}
			const { lastSharedAt } = await response.json();
			setIsLinkAvailable(!!lastSharedAt);
			setIsLinkUpdated(false);
		} catch (error) {
			console.error('Failed to check link status.');
			alert('Failed to check link status. Please try again.');
		} finally {
			setIsModalLoading(false);
		}
	};

	const processLink = async (state: LinkProcessingState) => {
		try {
			setLinkProcessingState(state);
			if (state === 'create') {
				await givePublicAccessToChat(chatId!);
				setIsLinkAvailable(true);
			} else if (state === 'update') {
				await givePublicAccessToChat(chatId!);
				setIsLinkUpdated(true);
			} else if (state === 'revoke') {
				await revokePublicAccessToChat(chatId!);
				setIsLinkAvailable(false);
			}
		} catch (error) {
			console.error('Error processing link:', error);
			alert('Error processing link. Please try again.');
		} finally {
			setLinkProcessingState('none');
		}
	};

	return (
		<>
			{isShareable ? (
				<Tooltip
					content="Share Chat"
					placement="bottom"
					size="sm"
					radius="sm"
					className="bg-gray-50 text-[#090909]"
					delay={500}
					closeDelay={0}
				>
					<Button
						isIconOnly
						disableRipple
						size="sm"
						variant="light"
						onPress={openShareLinkModal}
					>
						<IconShare size={18} />
					</Button>
				</Tooltip>
			) : null}
			<Modal
				isOpen={isOpen}
				onOpenChange={onOpenChange}
				classNames={{
					backdrop: 'backdrop-blur-md',
					base: 'bg-[#090909]',
					header: 'p-6 border-b border-default-50',
					body: 'flex flex-col p-6 text-sm text-white/60 gap-6',
					footer: 'flex justify-center pt-0 gap-4',
				}}
			>
				<ModalContent>
					{isModalLoading ? (
						<div className="flex h-40 items-center justify-center">
							<Spinner />
						</div>
					) : (
						<>
							<ModalHeader>
								{isLinkAvailable
									? 'You are ready to collaborate!'
									: 'Share a public link to your chat'}
							</ModalHeader>
							<ModalBody>
								<span>
									{isLinkAvailable
										? 'A public link to your chat is available for sharing. You can update it to include the latest chats.'
										: 'All the messages until now will be accessible by the recipient. Anything you add after creating the link stays private.'}
								</span>
								<div className="flex flex-row justify-between rounded-full bg-default-50 p-2">
									<Input
										isDisabled={!isLinkAvailable}
										isReadOnly
										radius="full"
										value={
											isLinkAvailable
												? `${baseUrl}/${type}?shareId=${chatId}`
												: `${baseUrl}/${type}?shareId=...`
										}
										classNames={{
											base: 'w-2/3 bg-transparent hover:bg-transparent',
											inputWrapper: 'bg-transparent hover:bg-transparent',
										}}
									/>
									<Button
										disableRipple
										isLoading={linkProcessingState === 'create'}
										radius="full"
										variant="flat"
										color="primary"
										onPress={
											isLinkAvailable
												? handleCopyToClipboard
												: async () => {
														await processLink('create');
													}
										}
									>
										<div className="flex flex-row items-center gap-1">
											{isLinkAvailable ? (
												isLinkCopied ? (
													<IconCheck size={16} />
												) : (
													<IconCopy size={16} />
												)
											) : linkProcessingState === 'create' ? null : (
												<IconLink size={16} />
											)}
											<span className="font-semibold">
												{isLinkAvailable
													? isLinkCopied
														? 'Copied'
														: 'Copy Link'
													: 'Create Link'}
											</span>
										</div>
									</Button>
								</div>
							</ModalBody>
							{isLinkAvailable && (
								<ModalFooter>
									<Button
										disableRipple
										isLoading={linkProcessingState === 'update'}
										variant="flat"
										color="primary"
										onPress={async () => {
											await processLink('update');
										}}
									>
										<div className="flex flex-row items-center gap-1">
											{linkProcessingState === 'update' ? null : (
												<IconLink size={16} />
											)}
											<span className="font-semibold">
												{isLinkUpdated ? 'Updated' : 'Update Link'}
											</span>
										</div>
									</Button>
									<Button
										disableRipple
										isLoading={linkProcessingState === 'revoke'}
										variant="flat"
										color="danger"
										onPress={async () => {
											await processLink('revoke');
										}}
									>
										<div className="flex flex-row items-center gap-1">
											{linkProcessingState === 'revoke' ? null : (
												<IconTrash size={16} />
											)}
											<span className="font-semibold">Delete Link</span>
										</div>
									</Button>
								</ModalFooter>
							)}
						</>
					)}
				</ModalContent>
			</Modal>
		</>
	);
};
