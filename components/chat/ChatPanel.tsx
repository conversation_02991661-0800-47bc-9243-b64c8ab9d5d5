import { Message } from 'ai';
import { useMemo, memo } from 'react';
import UserMessageComponent from '@/components/chat/UserMessageComponent';
import ZecoMessageComponent from '@/components/chat/ZecoMessageComponent';
import { ModelDisplayName } from '@/models/conversational/conversational-models';
import EmptyChatScreen from './EmptyChatScreen';
import { useScrollAnchor } from '@/lib/hooks/use-scroll-anchors';
import { SubmitData } from './PromptSection';
import { Spinner } from '@heroui/spinner';

export interface ChatProps extends React.ComponentProps<'div'> {
	messages: Message[];
	responseMap: Record<string, Message[]>;
	isLoading: boolean;
	isSharedChat?: boolean;
	onEdit: (model: ModelDisplayName, promptId: string, input: string, data: SubmitData) => void;
	onRegenerate: (model: ModelDisplayName, data: SubmitData) => void;
	onNavigate: (promptId: string, responseId: string, requestedResponseIndex: number) => void;
}

const ChatPanel = memo(
	({
		messages,
		responseMap,
		isLoading,
		isSharedChat,
		onEdit,
		onRegenerate,
		onNavigate,
	}: ChatProps) => {
		const { scrollRef, messagesRef } = useScrollAnchor();

		const messageGroups = useMemo(() => {
			return messages.reduce(
				(acc, message) => {
					if (message.role === 'user') {
						acc.push({
							promptId: message.id,
							userMessage: message,
							assistantMessage: messages.find(
								(m) =>
									m.role === 'assistant' &&
									messages.indexOf(m) > messages.indexOf(message) &&
									!acc.some((g) => g.assistantMessage?.id === m.id)
							),
							responseCount: responseMap[message.id]?.length || 0,
						});
					}
					return acc;
				},
				[] as Array<{
					promptId: string;
					userMessage: Message;
					assistantMessage?: Message;
					responseCount: number;
				}>
			);
		}, [messages, responseMap]);

		if (messageGroups.length === 0) {
			return <EmptyChatScreen />;
		}

		return (
			<div className="flex flex-col flex-1">
				<div
					ref={scrollRef}
					className="px-6 overflow-y-auto space-y-6 flex-1"
				>
					{messageGroups.map(
						({ promptId, userMessage, assistantMessage, responseCount }, index) => {
							const isLatestResponse = index === messageGroups.length - 1;

							return (
								<div
									key={promptId}
									className={isLatestResponse && isLoading ? 'min-h-[50vh]' : ''}
								>
									<UserMessageComponent
										prompt={userMessage.content}
										attachments={userMessage.experimental_attachments}
										onEdit={(model, modifiedPrompt) =>
											onEdit(
												model as ModelDisplayName,
												promptId,
												modifiedPrompt,
												{
													existingAttachments:
														userMessage.experimental_attachments,
												}
											)
										}
										isEditAvailable={
											!isLoading && !isSharedChat && isLatestResponse
										}
									/>
									{isLoading && isLatestResponse && (
										<Spinner
											variant="wave"
											color="secondary"
											size="sm"
											className="px-3"
										/>
									)}
									{assistantMessage && (
										<ZecoMessageComponent
											id={assistantMessage.id}
											modelName={(assistantMessage.data as any)?.modelName}
											content={assistantMessage.content}
											reasoning={assistantMessage.reasoning}
											attachments={assistantMessage.experimental_attachments}
											toolInvocations={assistantMessage.toolInvocations}
											responseCount={responseCount}
											isNewResponseInProgress={isLoading}
											isLatestResponse={isLatestResponse}
											isSharedResponse={isSharedChat}
											onRegenerate={(model, data) => {
												data.existingAttachments =
													userMessage.experimental_attachments;
												onRegenerate(model as ModelDisplayName, data);
											}}
											onNavigate={(targetIndex) => {
												onNavigate(
													promptId,
													assistantMessage.id,
													targetIndex
												);
											}}
											type="chat"
										/>
									)}
								</div>
							);
						}
					)}
					<div ref={messagesRef} />
				</div>
			</div>
		);
	}
);

ChatPanel.displayName = 'ChatPanel';
export default ChatPanel;
