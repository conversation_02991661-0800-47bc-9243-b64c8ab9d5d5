import { Attachment } from 'ai';
import { useCallback, useEffect, Dispatch, SetStateAction } from 'react';
import Image from 'next/image';
import { Modal, ModalContent, ModalBody, ModalFooter, ModalHeader } from '@heroui/modal';
import { Button } from '@heroui/button';
import { Tooltip } from '@heroui/tooltip';
import { IconChevronLeft, IconChevronRight, IconX, IconDownload } from '@tabler/icons-react';
import { cn } from '@/utils/cn';
import { getProviderIcon } from '../../utils/model-selection-utils';

interface ImageGalleryModalProps {
	isOpen: boolean;
	onOpenChange: () => void;
	images: Attachment[];
	selectedImageIndex: number;
	setSelectedImageIndex: Dispatch<SetStateAction<number>>;
	prompt?: string;
	modelProvider?: string;
}

const ImageGalleryModal: React.FC<ImageGalleryModalProps> = ({
	isOpen,
	onOpenChange,
	images,
	selectedImageIndex,
	setSelectedImageIndex,
	prompt,
	modelProvider,
}) => {
	const handleNext = useCallback(() => {
		setSelectedImageIndex((prevIndex) => (prevIndex + 1) % images.length);
	}, [images.length, setSelectedImageIndex]);

	const handlePrevious = useCallback(() => {
		setSelectedImageIndex((prevIndex) => (prevIndex - 1 + images.length) % images.length);
	}, [images.length, setSelectedImageIndex]);

	const handleDownload = useCallback(async () => {
		if (images.length === 0) {
			return;
		}
		const currentImage = images[selectedImageIndex];
		try {
			const response = await fetch(currentImage.url);
			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}
			const blob = await response.blob();
			const blobUrl = URL.createObjectURL(blob);

			const link = document.createElement('a');
			link.href = blobUrl;
			link.download = currentImage.name || `image-${selectedImageIndex + 1}.png`;
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);

			// Revoke the object URL to free up resources
			URL.revokeObjectURL(blobUrl);
		} catch (error) {
			console.error('Error downloading image:', error);
		}
	}, [images, selectedImageIndex]);

	// Handle keyboard navigation
	useEffect(() => {
		const handleKeyDown = (e: KeyboardEvent) => {
			if (!isOpen) {
				return;
			}
			if (e.key === 'ArrowRight') {
				handleNext();
			} else if (e.key === 'ArrowLeft') {
				handlePrevious();
			}
		};

		window.addEventListener('keydown', handleKeyDown);

		return () => {
			window.removeEventListener('keydown', handleKeyDown);
		};
	}, [isOpen, handleNext, handlePrevious]);

	if (images.length === 0) {
		return null;
	}

	const currentImage = images[selectedImageIndex];

	return (
		<Modal
			hideCloseButton
			isOpen={isOpen}
			onOpenChange={onOpenChange}
			size="full"
			classNames={{
				base: 'bg-black',
				body: 'p-0 relative flex grow flex-col items-center justify-center',
				header: 'px-4 py-3 flex justify-between items-center',
				footer: 'flex flex-col items-center p-6 gap-6',
			}}
		>
			<ModalContent>
				{(onClose) => (
					<>
						<ModalHeader>
							{/* Left section with model provider */}
							<div className="flex items-center">
								{modelProvider && (
									<div className="rounded-full bg-black/50 p-2">
										{getProviderIcon(modelProvider, 'image', 20)}
									</div>
								)}
							</div>

							{/* Right section with action buttons */}
							<div className="flex gap-2">
								<Tooltip
									content="Download image"
									placement="bottom"
									size="sm"
									radius="sm"
									className="bg-gray-50 text-[#090909]"
									delay={500}
									closeDelay={0}
								>
									<Button
										isIconOnly
										variant="flat"
										size="sm"
										radius="full"
										className="bg-black/50 text-white"
										onPress={handleDownload}
									>
										<IconDownload size={18} />
									</Button>
								</Tooltip>
								<Button
									isIconOnly
									variant="flat"
									size="sm"
									radius="full"
									className="bg-black/50 text-white"
									onPress={onClose}
								>
									<IconX size={18} />
								</Button>
							</div>
						</ModalHeader>

						<ModalBody>
							{images.length > 1 && (
								<div className="absolute inset-0 z-10 flex items-center justify-between px-4">
									<div className="pointer-events-auto">
										<Button
											isIconOnly
											radius="full"
											variant="flat"
											className="bg-black/50 text-white hover:bg-black/70"
											onPress={handlePrevious}
										>
											<IconChevronLeft size={24} />
										</Button>
									</div>
									<div className="pointer-events-auto">
										<Button
											isIconOnly
											radius="full"
											variant="flat"
											className="bg-black/50 text-white hover:bg-black/70"
											onPress={handleNext}
										>
											<IconChevronRight size={24} />
										</Button>
									</div>
								</div>
							)}

							<Image
								src={currentImage.url}
								alt={currentImage.name || `Image ${selectedImageIndex + 1}`}
								className="max-h-full max-w-full object-contain"
								fill
								priority
							/>
						</ModalBody>

						<ModalFooter>
							<Tooltip
								content={prompt}
								placement="top"
								size="md"
								radius="sm"
								className="max-w-lg bg-black/90 text-white/90"
								delay={500}
								closeDelay={0}
							>
								<div className="w-full max-w-3xl truncate text-center text-sm text-white/80">
									{prompt}
								</div>
							</Tooltip>

							{/* Enhanced thumbnail dots */}
							{images.length > 1 && (
								<div className="flex justify-center">
									<div className="flex gap-2 rounded-full bg-black/50 px-4 py-1.5">
										{images.map((_, index) => (
											<div
												key={index}
												className={cn(
													'h-2.5 w-2.5 cursor-pointer rounded-full transition-all',
													index === selectedImageIndex
														? 'scale-110 bg-white'
														: 'scale-90 bg-white/50 hover:bg-white/80'
												)}
												onClick={() => {
													setSelectedImageIndex(index);
												}}
											/>
										))}
									</div>
								</div>
							)}
						</ModalFooter>
					</>
				)}
			</ModalContent>
		</Modal>
	);
};

export default ImageGalleryModal;
