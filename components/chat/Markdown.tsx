import { memo, useState, ReactNode, Fragment } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import rehypePrism from 'rehype-prism';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import { Button } from '@heroui/button';
import { Code } from '@heroui/code';
import { CheckIcon, CopyIcon } from '@/components/icons';

// Function to render text with timestamp links
const renderTextWithTimestamps = (
	text: string,
	seekToTimestamp?: (seconds: number) => void
): ReactNode[] => {
	// Regex to match timestamps in HH:MM:SS or MM:SS format
	const timestampRegex = /(\d+:)?(\d{1,2}):(\d{2})/g;

	// If no timestamps, just return the text
	if (!timestampRegex.test(text)) {
		return [text];
	}

	// Reset the regex since we used it in the test
	timestampRegex.lastIndex = 0;

	const segments: ReactNode[] = [];
	let lastIndex = 0;
	let match;

	while ((match = timestampRegex.exec(text)) !== null) {
		// Add the text before the timestamp
		if (match.index > lastIndex) {
			segments.push(text.substring(lastIndex, match.index));
		}

		// Calculate seconds from timestamp
		const timestamp = match[0];
		const hours = match[1] ? parseInt(match[1].replace(':', '')) : 0;
		const minutes = parseInt(match[2]);
		const seconds = parseInt(match[3]);
		const totalSeconds = hours * 3600 + minutes * 60 + seconds;

		// Add the timestamp as a clickable element
		segments.push(
			<span
				key={`timestamp-${match.index}`}
				className="timestamp cursor-pointer text-blue-400 hover:underline"
				data-seconds={totalSeconds}
				onClick={() => seekToTimestamp?.(totalSeconds)}
			>
				{timestamp}
			</span>
		);

		lastIndex = match.index + match[0].length;
	}

	// Add any remaining text
	if (lastIndex < text.length) {
		segments.push(text.substring(lastIndex));
	}

	return segments;
};

// Helper function to process children for timestamps
const processChildren = (
	children: ReactNode,
	seekToTimestamp?: (seconds: number) => void
): ReactNode => {
	// If it's a string, apply timestamp rendering
	if (typeof children === 'string') {
		return renderTextWithTimestamps(children, seekToTimestamp);
	}

	// If it's an array, process each item
	if (Array.isArray(children)) {
		return children.map((child, index) => {
			if (typeof child === 'string') {
				return (
					<Fragment key={index}>
						{renderTextWithTimestamps(child, seekToTimestamp)}
					</Fragment>
				);
			}
			return child;
		});
	}

	// Otherwise, return as is
	return children;
};

// Function to extract code content from children
const getCodeContent = (children: any): string => {
	if (typeof children === 'string') {
		return children;
	}

	if (Array.isArray(children)) {
		return children.map((child) => getCodeContent(child)).join('');
	}

	if (children && typeof children === 'object' && 'props' in children) {
		return getCodeContent(children.props.children);
	}

	return '';
};

const CodeBlock = ({ language, codeContent }: { language: string; codeContent: string }) => {
	const [isCopied, setIsCopied] = useState(false);

	const handleCopy = (text: string) => {
		navigator.clipboard.writeText(text).then(() => {
			setIsCopied(true);
			setTimeout(() => setIsCopied(false), 2000);
		});
	};

	return (
		<div className="group relative mb-6">
			<div className="flex items-center justify-between bg-sidebar-hover px-4 py-2 rounded-t-lg border-b border-gray-700/50">
				<span className="text-xs font-medium text-primary-text uppercase tracking-wide">
					{language}
				</span>
				<Button
					isIconOnly
					onPress={() => handleCopy(codeContent)}
					size="sm"
					variant="light"
					radius="full"
				>
					{isCopied ? (
						<CheckIcon
							size={16}
							className="text-green-500"
						/>
					) : (
						<CopyIcon size={16} />
					)}
				</Button>
			</div>
			<SyntaxHighlighter
				style={vscDarkPlus}
				language={language}
				PreTag="div"
				customStyle={{
					backgroundColor: 'rgb(09, 09, 09)',
					borderRadius: '0 0 6px 6px',
					margin: '0',
					padding: '1rem',
				}}
			>
				{codeContent.replace(/\n$/, '')}
			</SyntaxHighlighter>
		</div>
	);
};

export const Markdown = memo(
	({ content, isReasoning = false }: { content: string; isReasoning?: boolean }) => {
		return (
			<ReactMarkdown
				remarkPlugins={[remarkGfm, remarkMath]}
				rehypePlugins={[rehypeKatex, rehypePrism]}
				components={{
					code({ node, inline, className, children, ...props }: any) {
						const match = /language-(\w+)/.exec(className || '');
						if (!inline && match) {
							const codeContent = getCodeContent(children);
							return (
								<CodeBlock
									language={match[1]}
									codeContent={codeContent}
								/>
							);
						}
						return (
							<Code
								{...props}
								className="whitespace-normal"
							>
								{children}
							</Code>
						);
					},
					table: (props) => (
						<div className="overflow-x-auto mb-4">
							<table className="min-w-full divide-y divide-gray-700">
								{props.children}
							</table>
						</div>
					),
					th: (props) => (
						<th className="px-6 py-3 bg-gray-800 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
							{processChildren(props.children)}
						</th>
					),
					td: (props) => (
						<td className="px-6 py-4 text-sm text-gray-100">
							{processChildren(props.children)}
						</td>
					),
					p: ({ children }) => (
						<p
							className={`mb-4 leading-7 ${isReasoning ? 'text-white/60' : 'text-gray-100'}`}
						>
							{processChildren(children)}
						</p>
					),
					// Handle text content outside paragraphs
					text: ({ children }) => <>{processChildren(children)}</>,
					ul: ({ children }) => (
						<ul className="mb-6 pl-6 list-disc space-y-2">{children}</ul>
					),
					ol: ({ children }) => (
						<ol className="mb-6 pl-6 list-decimal space-y-2">{children}</ol>
					),
					li: ({ children }) => (
						<li className="leading-7">{processChildren(children)}</li>
					),
					h1: ({ children }) => (
						<h1 className="text-3xl font-bold mb-6 mt-8 text-white">
							{processChildren(children)}
						</h1>
					),
					h2: ({ children }) => (
						<h2 className="text-2xl font-semibold mb-4 mt-6 text-gray-50">
							{processChildren(children)}
						</h2>
					),
					h3: ({ children }) => (
						<h3 className="text-xl font-medium mb-4 mt-5 text-gray-100">
							{processChildren(children)}
						</h3>
					),
					blockquote: ({ children }) => (
						<blockquote className="border-l-4 border-gray-600 pl-4 py-1 mb-4 italic text-gray-300 bg-gray-800/50">
							{processChildren(children)}
						</blockquote>
					),
					hr: () => <hr className="my-8 border-gray-700" />,
					a: ({ href, children }) => (
						<a
							href={href}
							className="text-blue-400 hover:text-blue-300 underline-offset-2 hover:underline transition-colors"
							target="_blank"
							rel="noopener noreferrer"
						>
							{processChildren(children)}
						</a>
					),
				}}
			>
				{content}
			</ReactMarkdown>
		);
	}
);

Markdown.displayName = 'Markdown';
