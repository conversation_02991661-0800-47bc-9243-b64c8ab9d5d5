import { useState } from 'react';
import { Attachment, ToolInvocation } from 'ai';
import {
	IconThumbDown,
	IconThumbUp,
	IconThumbUpFilled,
	IconThumbDownFilled,
	IconChevronDown,
} from '@tabler/icons-react';
import { Button } from '@heroui/button';
import { Tooltip } from '@heroui/tooltip';
import { CheckIcon, CopyIcon } from '@/components/icons';
import { ModelDisplayName } from '@/models/conversational/conversational-models';
import { Markdown } from './Markdown';
import RegenerateResponseButton from './RegenerateResponseButton';
import { SubmitData } from './PromptSection';
import ResponseNavigation from './ResponseNavigation';
import { cn } from '@/utils/cn';
import { ImageModelDisplayName } from '@/models/image/image-generation-models';
import { VideoModelDisplayName } from '@/models/video/video-generation-models';
import ToolCallIndicator from './ToolCallIndicator';
import { ZecoMessageLayout } from './ZecoMessageLayout';
import { ImageLayout } from './ImageLayout';
import { VideoLayout } from './VideoLayout';
import { ChatType } from '@/types/chat';
import { getProviderIcon, getTechnicalNameMetadata } from '../../utils/model-selection-utils';

interface ZecoMessageProps {
	id: string;
	prompt?: string;
	content?: string;
	reasoning?: string;
	modelName?: string;
	attachments?: Attachment[];
	toolInvocations?: Array<ToolInvocation>;
	responseCount: number;
	isNewResponseInProgress: boolean;
	isLatestResponse: boolean;
	isSharedResponse?: boolean;
	onRegenerate: (
		model: ModelDisplayName | ImageModelDisplayName | VideoModelDisplayName,
		data: SubmitData
	) => void;
	onNavigate: (requestedResponseIndex: number) => void;
	type: ChatType;
}

const ZecoMessageComponent: React.FC<ZecoMessageProps> = ({
	id,
	prompt,
	content,
	reasoning,
	modelName,
	attachments,
	toolInvocations,
	responseCount,
	isNewResponseInProgress,
	isLatestResponse,
	isSharedResponse,
	onRegenerate,
	onNavigate,
	type = 'chat',
}) => {
	const { modelDisplayName, modelProvider } = getTechnicalNameMetadata(type, modelName);
	const isRegenerationAvailable =
		!isNewResponseInProgress &&
		isLatestResponse &&
		!isSharedResponse &&
		modelDisplayName &&
		modelProvider;
	const shouldShowReasoning = isNewResponseInProgress && isLatestResponse;
	const shouldShowFooter = !(isLatestResponse && isNewResponseInProgress);
	const [showReasoning, setShowReasoning] = useState<boolean>(shouldShowReasoning);
	const [isCopied, setIsCopied] = useState<boolean>(false);
	const [isLiked, setIsLiked] = useState<boolean | null>(null);

	if (
		!content &&
		(!attachments || attachments.length === 0) &&
		(!toolInvocations || toolInvocations.length === 0)
	) {
		return null;
	}

	const images =
		attachments?.filter(
			(att) =>
				att.contentType?.startsWith('image/') ||
				att.url.match(/\.(jpg|jpeg|png|gif|webp|avif)$/i)
		) ?? [];

	const videos =
		attachments?.filter(
			(att) =>
				att.contentType?.startsWith('video/') || att.url.match(/\.(mp4|webm|ogg|mov|avi)$/i)
		) ?? [];

	const handleCopyToClipboard = () => {
		navigator.clipboard.writeText(content || '').then(() => {
			setIsCopied(true);
			setTimeout(() => {
				setIsCopied(false);
			}, 2000);
		});
	};

	const ProviderIcon = !!modelProvider ? getProviderIcon(modelProvider, type, 16) : null;

	return (
		<div className="flex flex-col gap-2 py-2 group animate-in fade-in slide-in-from-bottom-5 duration-300">
			{!(isLatestResponse && isNewResponseInProgress) && ProviderIcon && (
				<div className="flex flex-row items-center gap-1 px-3 text-xs text-white/60">
					<span className="flex items-center gap-1.5">
						<span className="flex-shrink-0">{ProviderIcon}</span>
						<span className="font-medium">ZECO</span>
					</span>
				</div>
			)}

			{reasoning && (
				<div className="px-3">
					<Button
						disableRipple
						size="sm"
						variant="bordered"
						endContent={<IconChevronDown size={14} />}
						onPress={() => setShowReasoning((prev) => !prev)}
						className="p-0 border-transparent items-center gap-1 border-none"
					>
						<span
							className={`text-white/60 ${shouldShowReasoning ? 'animate-pulse' : 'hover:text-gray-100'}`}
						>
							Reasoning
						</span>
					</Button>
					{showReasoning && (
						<div className="flex ">
							<div className="border-l-1 border-zeco-purple/60" />
							<div className="text-sm pl-6">
								<Markdown
									content={reasoning}
									isReasoning={true}
								/>
							</div>
						</div>
					)}
				</div>
			)}

			{toolInvocations?.map((toolInvocation, index) => (
				<ToolCallIndicator
					key={`${toolInvocation.toolName}-${index}`}
					toolInvocation={toolInvocation}
				/>
			))}

			<ZecoMessageLayout
				content={content}
				toolInvocations={toolInvocations}
			/>

			<ImageLayout
				images={images}
				prompt={prompt}
				modelProvider={modelProvider}
			/>

			<VideoLayout
				videos={videos}
				prompt={prompt}
				modelProvider={modelProvider}
			/>

			{shouldShowFooter && (
				<div
					className={cn(
						'mt-2 px-3 flex items-center justify-start text-secondary-text',
						!isRegenerationAvailable &&
							'opacity-0 group-hover:opacity-100 transition-opacity'
					)}
				>
					{type === 'chat' && (
						<Tooltip
							content="Copy"
							placement="bottom"
							size="sm"
							radius="sm"
							className="bg-gray-50 text-[#090909]"
							delay={500}
							closeDelay={0}
						>
							<Button
								isIconOnly
								disableRipple
								onPress={handleCopyToClipboard}
								size="sm"
								variant="light"
								radius="full"
							>
								{isCopied ? (
									<CheckIcon
										size={16}
										className="text-success"
									/>
								) : (
									<CopyIcon
										size={16}
										className="text-secondary-text"
									/>
								)}
							</Button>
						</Tooltip>
					)}

					{(isLiked === null || isLiked === true) && (
						<Tooltip
							content="Good Response"
							placement="bottom"
							size="sm"
							radius="sm"
							className="bg-gray-50 text-[#090909]"
							delay={500}
							closeDelay={0}
						>
							<Button
								isIconOnly
								disableRipple
								size="sm"
								variant="light"
								radius="full"
								onPress={() => setIsLiked(true)}
							>
								{isLiked === true ? (
									<IconThumbUpFilled size={16} />
								) : (
									<IconThumbUp size={16} />
								)}
							</Button>
						</Tooltip>
					)}

					{(isLiked === null || isLiked === false) && (
						<Tooltip
							content="Bad response"
							placement="bottom"
							size="sm"
							radius="sm"
							className="bg-gray-50 text-[#090909]"
							delay={500}
							closeDelay={0}
						>
							<Button
								isIconOnly
								disableRipple
								size="sm"
								variant="light"
								radius="full"
								onPress={() => setIsLiked(false)}
							>
								{isLiked === false ? (
									<IconThumbDownFilled size={16} />
								) : (
									<IconThumbDown size={16} />
								)}
							</Button>
						</Tooltip>
					)}

					{isRegenerationAvailable && (
						<RegenerateResponseButton
							type={type}
							modelName={modelDisplayName}
							modelProvider={modelProvider}
							onRegenerate={onRegenerate}
						/>
					)}

					<ResponseNavigation
						currentIndex={parseInt(id.split(':').pop()!)}
						totalVersions={responseCount}
						onNavigate={onNavigate}
					/>
				</div>
			)}
		</div>
	);
};

export default ZecoMessageComponent;
