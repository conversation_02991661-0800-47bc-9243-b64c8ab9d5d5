import { Button } from '@heroui/button';
import { IconChevronLeft, IconChevronRight } from '@tabler/icons-react';

interface ResponseNavigationProps {
	currentIndex: number;
	totalVersions: number;
	onNavigate: (requestedResponseIndex: number) => void;
}

const ResponseNavigation = ({
	currentIndex,
	totalVersions,
	onNavigate,
}: ResponseNavigationProps) => {
	if (totalVersions <= 1) return null;

	return (
		<div className="flex items-center gap-2">
			<Button
				disableRipple
				isIconOnly
				size="sm"
				variant="light"
				radius="full"
				isDisabled={currentIndex === 0}
				onPress={() => onNavigate(currentIndex - 1)}
			>
				<IconChevronLeft size={16} />
			</Button>
			<span className="cursor-default text-xs text-secondary-text">
				{currentIndex + 1} / {totalVersions}
			</span>
			<Button
				disableRipple
				isIconOnly
				size="sm"
				variant="light"
				radius="full"
				isDisabled={currentIndex === totalVersions - 1}
				onPress={() => onNavigate(currentIndex + 1)}
			>
				<IconChevronRight size={16} />
			</Button>
		</div>
	);
};

export default ResponseNavigation;
