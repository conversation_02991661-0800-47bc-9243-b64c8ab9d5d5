import { memo } from 'react';
import { ChatMessage, Response } from '@/types/chat';
import { useScrollAnchor } from '@/lib/hooks/use-scroll-anchors';
import EmptyChatScreen from './EmptyChatScreen';
import UserMessageComponent from './UserMessageComponent';
import ZecoMessageComponent from './ZecoMessageComponent';
import { SubmitData } from './PromptSection';
import { VideoModelDisplayName } from '@/models/video/video-generation-models';
import { Spinner } from '@heroui/spinner';

interface VideoGenerationPanelProps {
	messages: ChatMessage[];
	isLoading: boolean;
	isVideoResponseLoading: boolean;
	isSharedChat?: boolean;
	onEdit: (
		model: VideoModelDisplayName,
		messageId: string,
		prompt: string,
		data: SubmitData
	) => void;
	onRegenerate: (messageId: string, model: VideoModelDisplayName, data: SubmitData) => void;
	onNavigate: (messageId: string, targetResponseIndex: number) => void;
}

const VideoGenerationPanel = memo(
	({
		messages,
		isLoading,
		isVideoResponseLoading,
		isSharedChat,
		onEdit,
		onRegenerate,
		onNavigate,
	}: VideoGenerationPanelProps) => {
		const { scrollRef, messagesRef } = useScrollAnchor();

		if (messages.length === 0) {
			return <EmptyChatScreen />;
		}

		return (
			<div className="flex flex-1 flex-col">
				<div className="flex-1 space-y-6 overflow-y-auto px-6">
					<div
						ref={scrollRef}
						className="space-y-4 py-6"
					>
						{messages.map((message, messageIndex) => {
							const isLatestResponse = messageIndex === messages.length - 1;
							const shouldHideResponseWhileLoading = isLoading && isLatestResponse;
							let activeResponse: Response | undefined;
							if (
								!shouldHideResponseWhileLoading &&
								message.response &&
								message.response.length > 0
							) {
								activeResponse = message.response.find((resp) => !!resp.isActive);
								if (!activeResponse) {
									activeResponse = message.response[message.response.length - 1];
								}
							}

							return (
								<div
									key={message.id}
									className={isLatestResponse && isLoading ? 'min-h-[50vh]' : ''}
								>
									<UserMessageComponent
										prompt={message.prompt.text}
										attachments={message.prompt.attachments}
										onEdit={(model, modifiedPrompt) =>
											onEdit(
												model as VideoModelDisplayName,
												message.id,
												modifiedPrompt,
												{
													existingAttachments: message.prompt.attachments,
												}
											)
										}
										isEditAvailable={
											!(isLoading || isVideoResponseLoading) &&
											!isSharedChat &&
											isLatestResponse
										}
									/>
									{(isLoading || isVideoResponseLoading) && isLatestResponse && (
										<Spinner
											variant="wave"
											color="secondary"
											size="sm"
											className="px-3"
										/>
									)}
									{activeResponse && (
										<ZecoMessageComponent
											id={`${message.id}:${message.response!.indexOf(activeResponse)}`}
											prompt={message.prompt.text}
											content={activeResponse.text}
											modelName={activeResponse.modelUsed.modelName}
											attachments={activeResponse.videos?.map(
												({ type, name, url }) => ({
													name,
													contentType: type,
													url,
												})
											)}
											responseCount={message.response!.length}
											isNewResponseInProgress={
												isLoading || isVideoResponseLoading
											}
											isLatestResponse={isLatestResponse}
											isSharedResponse={isSharedChat}
											onRegenerate={(model, data) => {
												data.existingAttachments =
													message.prompt.attachments;
												onRegenerate(
													message.id,
													model as VideoModelDisplayName,
													data
												);
											}}
											onNavigate={(targetIndex) => {
												onNavigate(message.id, targetIndex);
											}}
											type="video"
										/>
									)}
								</div>
							);
						})}
						<div ref={messagesRef} />
					</div>
				</div>
			</div>
		);
	}
);

VideoGenerationPanel.displayName = 'VideoGenerationPanel';
export default VideoGenerationPanel;
