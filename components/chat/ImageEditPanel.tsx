import Image from 'next/image';
import { useRef, useState, useCallback, useEffect } from 'react';
import { Button } from '@heroui/button';
import { Spinner } from '@heroui/spinner';
import { addToast } from '@heroui/toast';
import { Tooltip } from '@heroui/tooltip';
import {
	IconPhotoUp,
	IconUpload,
	IconX,
	IconChevronLeft,
	IconChevronRight,
	IconDownload,
} from '@tabler/icons-react';
import { getSupportedImageTypesForImageModels } from '@/models/image/image-generation-constraints';
import { ImageModelDisplayName } from '@/models/image/image-generation-models';
import { Attachment } from '@/types/chat';
import { cn } from '@/utils/cn';

interface ImageEditPanelProps {
	isImageEditInProgress: boolean;
	imagesAvailableForEdit: Attachment[];
	setImageToEdit: React.Dispatch<React.SetStateAction<Attachment | null>>;
	setImagesAvailableForEdit: React.Dispatch<React.SetStateAction<Attachment[]>>;
}

const ImageEditPanel = ({
	isImageEditInProgress,
	imagesAvailableForEdit,
	setImageToEdit,
	setImagesAvailableForEdit,
}: ImageEditPanelProps) => {
	const imageInputRef = useRef<HTMLInputElement>(null);
	const thumbnailContainerRef = useRef<HTMLDivElement>(null);
	const [isDragOver, setIsDragOver] = useState(false);
	const [isUploading, setIsUploading] = useState(false);
	const [selectedImageIndex, setSelectedImageIndex] = useState(-1);

	const processImageFiles = useCallback(
		(files: FileList) => {
			setIsUploading(true);
			let processedCount = 0;
			const nonImageFiles: string[] = [];

			const onFileProcessed = () => {
				processedCount++;
				if (processedCount === files.length) {
					setIsUploading(false);
					if (nonImageFiles.length) {
						addToast({
							title: 'Invalid file type',
							description: `Only image files are supported. Skipped: ${nonImageFiles.join(', ')}`,
							color: 'warning',
						});
					}
				}
			};

			Array.from(files).forEach((file) => {
				// Reject non-image files early
				if (!file.type.startsWith('image/')) {
					nonImageFiles.push(file.name);
					onFileProcessed();
					return;
				}

				const reader = new FileReader();
				reader.onload = (event) => {
					const base64Url = event.target?.result as string;
					const newImage: Attachment = {
						type: file.type,
						name: file.name,
						url: base64Url,
					};
					setImagesAvailableForEdit((prev) => [...prev, newImage]);
					onFileProcessed();
				};
				reader.readAsDataURL(file);
			});
		},
		[setImagesAvailableForEdit]
	);

	const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
		const imageFiles = e.target.files;
		if (imageFiles) {
			processImageFiles(imageFiles);
		}
	};

	const handleDragOver = useCallback(
		(e: React.DragEvent) => {
			e.preventDefault();
			e.stopPropagation();
			if (!isDragOver) {
				setIsDragOver(true);
			}
		},
		[isDragOver]
	);

	const handleDragLeave = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		e.stopPropagation();
		// Only set drag over to false if we're leaving the container entirely
		if (!e.currentTarget.contains(e.relatedTarget as Node)) {
			setIsDragOver(false);
		}
	}, []);

	const handleDrop = useCallback(
		(e: React.DragEvent) => {
			e.preventDefault();
			e.stopPropagation();
			setIsDragOver(false);

			const files = e.dataTransfer.files;
			if (files && files.length > 0) {
				processImageFiles(files);
			}
		},
		[processImageFiles]
	);

	const handleNext = useCallback(() => {
		if (imagesAvailableForEdit.length > 1) {
			const nextIndex = (selectedImageIndex + 1) % imagesAvailableForEdit.length;
			setSelectedImageIndex(nextIndex);
		}
	}, [imagesAvailableForEdit.length, selectedImageIndex]);

	const handlePrevious = useCallback(() => {
		if (imagesAvailableForEdit.length > 1) {
			const prevIndex =
				(selectedImageIndex - 1 + imagesAvailableForEdit.length) %
				imagesAvailableForEdit.length;
			setSelectedImageIndex(prevIndex);
		}
	}, [imagesAvailableForEdit.length, selectedImageIndex]);

	const handleThumbnailClick = useCallback((index: number) => {
		setSelectedImageIndex(index);
	}, []);

	const handleDownload = useCallback(async () => {
		if (imagesAvailableForEdit.length === 0) {
			return;
		}
		const currentImage = imagesAvailableForEdit[selectedImageIndex];
		try {
			const response = await fetch(currentImage.url);
			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}
			const blob = await response.blob();
			const blobUrl = URL.createObjectURL(blob);

			const link = document.createElement('a');
			link.href = blobUrl;
			link.download = currentImage.name || `image-${selectedImageIndex + 1}.png`;
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);

			// Revoke the object URL to free up resources
			URL.revokeObjectURL(blobUrl);
		} catch (error) {
			console.error('Error downloading image:', error);
		}
	}, [imagesAvailableForEdit, selectedImageIndex]);

	const scrollToSelectedThumbnail = useCallback(() => {
		if (thumbnailContainerRef.current && selectedImageIndex >= 0) {
			const container = thumbnailContainerRef.current;
			const thumbnailElements = container.children;

			if (thumbnailElements[selectedImageIndex]) {
				const selectedThumbnail = thumbnailElements[selectedImageIndex] as HTMLElement;
				const containerRect = container.getBoundingClientRect();
				const thumbnailRect = selectedThumbnail.getBoundingClientRect();

				// Check if thumbnail is outside the visible area
				if (
					thumbnailRect.left < containerRect.left ||
					thumbnailRect.right > containerRect.right
				) {
					selectedThumbnail.scrollIntoView({
						behavior: 'smooth',
						block: 'nearest',
						inline: 'center',
					});
				}
			}
		}
	}, [selectedImageIndex]);

	// Set selected image index to the latest image when new images are added
	useEffect(() => {
		setSelectedImageIndex(imagesAvailableForEdit.length - 1);
	}, [imagesAvailableForEdit.length]);

	// Update the image to be edited whenever selected image changes
	useEffect(() => {
		if (selectedImageIndex >= 0) {
			setImageToEdit(imagesAvailableForEdit[selectedImageIndex]);
		} else {
			setImageToEdit(null);
		}
	}, [imagesAvailableForEdit, selectedImageIndex, setImageToEdit]);

	// Scroll to selected thumbnail when selection changes
	useEffect(() => {
		if (selectedImageIndex >= 0 && imagesAvailableForEdit.length > 1) {
			// Use a small delay to ensure DOM is updated
			const timeoutId = setTimeout(scrollToSelectedThumbnail, 100);
			return () => clearTimeout(timeoutId);
		}
	}, [selectedImageIndex, imagesAvailableForEdit.length, scrollToSelectedThumbnail]);

	// Handle keyboard navigation
	useEffect(() => {
		const handleKeyDown = (e: KeyboardEvent) => {
			if (imagesAvailableForEdit.length <= 1) {
				return;
			}
			if (e.key === 'ArrowRight') {
				handleNext();
			} else if (e.key === 'ArrowLeft') {
				handlePrevious();
			}
		};

		window.addEventListener('keydown', handleKeyDown);

		return () => {
			window.removeEventListener('keydown', handleKeyDown);
		};
	}, [handleNext, handlePrevious, imagesAvailableForEdit.length]);

	const currentImage = imagesAvailableForEdit[selectedImageIndex];

	return (
		<div
			className={cn(
				'relative flex flex-col h-full w-full',
				imagesAvailableForEdit.length === 0 &&
					'items-center justify-center gap-5 border-2 border-dashed border-white/10 rounded-xl',
				isDragOver && 'border-zeco-purple bg-zeco-purple/5'
			)}
			onDragOver={handleDragOver}
			onDragLeave={handleDragLeave}
			onDrop={handleDrop}
			aria-label="Image upload dropzone"
		>
			{!!currentImage ? (
				<>
					{/* Main image display area */}
					<div className="relative flex-1 flex items-center justify-center">
						<Image
							fill
							src={currentImage.url}
							alt={currentImage.name || 'Uploaded preview'}
							className={cn(
								'object-contain transition-all duration-300',
								isImageEditInProgress && 'opacity-60 blur-sm'
							)}
						/>
						{isImageEditInProgress && (
							<div className="absolute inset-0 flex flex-col items-center justify-center z-10 gap-4">
								<Spinner
									size="lg"
									color="secondary"
								/>
								<div className="text-white/80 text-xs font-medium">
									Editing image...
								</div>
							</div>
						)}

						{/* Navigation arrows for multiple images - only show when not loading */}
						{imagesAvailableForEdit.length > 1 && !isImageEditInProgress && (
							<div className="absolute inset-0 flex items-center justify-between px-4 z-10">
								<Button
									isIconOnly
									radius="full"
									variant="flat"
									size="sm"
									className="bg-black/50 text-white hover:bg-black/70"
									onPress={handlePrevious}
								>
									<IconChevronLeft size={20} />
								</Button>
								<Button
									isIconOnly
									radius="full"
									variant="flat"
									size="sm"
									className="bg-black/50 text-white hover:bg-black/70"
									onPress={handleNext}
								>
									<IconChevronRight size={20} />
								</Button>
							</div>
						)}

						{/* Clear all button - only show when not loading */}
						{!isImageEditInProgress && (
							<div className="absolute top-0 right-2 z-10 flex gap-2">
								<Tooltip
									content="Download image"
									placement="bottom"
									size="sm"
									radius="sm"
									className="bg-gray-50 text-[#090909]"
									delay={500}
									closeDelay={0}
								>
									<Button
										isIconOnly
										color="secondary"
										radius="full"
										size="sm"
										variant="flat"
										onPress={handleDownload}
									>
										<IconDownload size={16} />
									</Button>
								</Tooltip>
								<Tooltip
									content="Clear all images"
									placement="bottom"
									size="sm"
									radius="sm"
									className="bg-gray-50 text-[#090909]"
									delay={500}
									closeDelay={0}
								>
									<Button
										isIconOnly
										color="secondary"
										radius="full"
										size="sm"
										variant="flat"
										onPress={() => setImagesAvailableForEdit([])}
									>
										<IconX size={16} />
									</Button>
								</Tooltip>
							</div>
						)}
					</div>

					{/* Thumbnail navigation - only show when not loading */}
					{!isImageEditInProgress && imagesAvailableForEdit.length > 1 && (
						<div
							ref={thumbnailContainerRef}
							className="flex overflow-x-auto max-w-full space-x-2 mt-4 px-2 mx-auto"
						>
							{imagesAvailableForEdit.map((image, index) => (
								<div
									key={index}
									className={cn(
										'relative flex-shrink-0 cursor-pointer transition-all duration-200',
										'w-16 h-16 rounded-lg overflow-hidden border-1.5',
										index === selectedImageIndex
											? 'border-zeco-purple'
											: 'border-white/20 hover:border-white/40'
									)}
									onClick={() => handleThumbnailClick(index)}
								>
									<Image
										src={image.url}
										alt={image.name || `Image ${index + 1}`}
										fill
										className="object-cover"
									/>
								</div>
							))}
						</div>
					)}
				</>
			) : (
				<>
					<div className="flex items-center justify-center w-12 h-12 rounded-full bg-zeco-purple/15">
						<IconPhotoUp color="#9555d3" />
					</div>
					<div className="flex flex-col items-center gap-1">
						<span className="text-md">Upload an image to edit</span>
						<span className="text-xs text-white/60">Drag & drop to upload</span>
					</div>
					<Button
						isLoading={isUploading}
						disableRipple
						color="secondary"
						size="md"
						variant="flat"
						startContent={isUploading ? null : <IconUpload size={18} />}
						onPress={() => imageInputRef.current?.click()}
					>
						{isUploading ? 'Uploading' : 'Select'}
					</Button>
					<input
						hidden
						type="file"
						ref={imageInputRef}
						accept={getSupportedImageTypesForImageModels(
							ImageModelDisplayName.FLUX_KONTEXT_PRO
						)
							.map((type) => type.extension)
							.join(',')}
						onChange={handleImageUpload}
						multiple={true}
					/>
				</>
			)}
		</div>
	);
};

export default ImageEditPanel;
