import { ToolInvocation } from 'ai';

interface ToolCallIndicatorProps {
	toolInvocation: ToolInvocation;
}

const getMessage = (toolName: string) => {
	switch (toolName) {
		case 'useWebSearch':
			return 'Searching the Internet';
		case 'scrapeWebpages':
			return 'Going through webpages';
		default:
			return 'Processing your request';
	}
};

const ToolCallIndicator: React.FC<ToolCallIndicatorProps> = ({ toolInvocation }) => {
	if (toolInvocation.state === 'result') {
		return null;
	}

	return (
		<div className="animate-pulse text-white/60 text-xs px-3 py-2">
			{getMessage(toolInvocation.toolName)}
		</div>
	);
};

export default ToolCallIndicator;
