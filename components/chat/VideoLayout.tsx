import { Attachment } from 'ai';
import { useState } from 'react';
import { IconCheck, IconDownload } from '@tabler/icons-react';
import { Button } from '@heroui/button';
import { Tooltip } from '@heroui/tooltip';
import { useDisclosure } from '@heroui/modal';
import { cn } from '@/utils/cn';
import VideoGalleryModal from './VideoGalleryModal';

interface VideoLayoutProps {
	videos: Attachment[];
	prompt?: string;
	modelProvider?: string;
}

export const VideoLayout = ({ videos, prompt, modelProvider }: VideoLayoutProps) => {
	const {
		isOpen: isGalleryOpen,
		onOpen: onGalleryOpen,
		onOpenChange: onGalleryOpenChange,
	} = useDisclosure();
	const [selectedVideoIndex, setSelectedVideoIndex] = useState<number>(0);

	const [isDownloaded, setIsDownloaded] = useState(false);
	const handleVideoDownload = async (videoUrl: string, videoName: string) => {
		try {
			const response = await fetch(videoUrl);
			if (!response.ok) {
				throw new Error(`Failed to fetch video: ${response.statusText}`);
			}

			const blob = await response.blob();
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.style.display = 'none';
			a.href = url;
			a.download = videoName || 'generated-video.mp4';
			document.body.appendChild(a);
			a.click();
			window.URL.revokeObjectURL(url);
			document.body.removeChild(a);

			setIsDownloaded(true);
			setTimeout(() => {
				setIsDownloaded(false);
			}, 2000);
		} catch (err) {
			console.error('Failed to download video:', err);
		}
	};

	if (videos.length === 0) {
		return null;
	}

	return (
		<>
			<div className="grid grid-cols-1 sm:grid-cols-2 gap-4 px-3">
				{videos.map((video, index) => (
					<div
						key={video.url}
						className="relative group/video"
					>
						<div
							onClick={() => {
								setSelectedVideoIndex(index);
								onGalleryOpen();
							}}
							className={cn(
								'relative aspect-video block rounded-lg overflow-hidden cursor-pointer',
								'transition-all duration-200 hover:scale-[1.02]'
							)}
						>
							<video
								src={video.url}
								controls
								className="w-full h-full object-cover"
								preload="metadata"
								playsInline
							>
								Your browser does not support the video tag.
							</video>
						</div>
						<Tooltip
							content="Download video"
							placement="bottom"
							size="sm"
							radius="sm"
							className="bg-gray-50 text-[#090909]"
							delay={500}
							closeDelay={0}
						>
							<Button
								isIconOnly
								onPress={() =>
									handleVideoDownload(
										video.url,
										video.name || `Generated video ${index + 1}`
									)
								}
								size="sm"
								variant="flat"
								radius="full"
								className="absolute top-2 right-2 bg-black/50 opacity-0 group-hover/video:opacity-100 transition-opacity"
							>
								{isDownloaded ? (
									<IconCheck
										size={16}
										className="text-success"
									/>
								) : (
									<IconDownload
										size={16}
										className="text-secondary-text"
									/>
								)}
							</Button>
						</Tooltip>
					</div>
				))}
			</div>

			{isGalleryOpen && (
				<VideoGalleryModal
					isOpen={isGalleryOpen}
					onOpenChange={onGalleryOpenChange}
					videos={videos}
					selectedVideoIndex={selectedVideoIndex}
					setSelectedVideoIndex={setSelectedVideoIndex}
					prompt={prompt}
					modelProvider={modelProvider}
				/>
			)}
		</>
	);
};
