import { memo } from 'react';
import { ChatMessage, Response } from '@/types/chat';
import { useScrollAnchor } from '@/lib/hooks/use-scroll-anchors';
import EmptyChatScreen from './EmptyChatScreen';
import UserMessageComponent from './UserMessageComponent';
import ZecoMessageComponent from './ZecoMessageComponent';
import { ImageModelDisplayName } from '@/models/image/image-generation-models';
import { SubmitData } from './PromptSection';
import { ImagePlaceholder } from '../ui/ImagePlaceholder';
import { ImageGenerationParams } from '@/models/image/image-generation-constraints';

interface ImageGenerationPanelProps {
	messages: ChatMessage[];
	isLoading: boolean;
	isSharedChat?: boolean;
	onEdit: (
		model: ImageModelDisplayName,
		messageId: string,
		prompt: string,
		data: SubmitData
	) => void;
	onRegenerate: (messageId: string, model: ImageModelDisplayName, data: SubmitData) => void;
	onNavigate: (messageId: string, targetResponseIndex: number) => void;
}

const ImageGenerationPanel = memo(
	({
		messages,
		isLoading,
		isSharedChat,
		onEdit,
		onRegenerate,
		onNavigate,
	}: ImageGenerationPanelProps) => {
		const { scrollRef, messagesRef } = useScrollAnchor();

		if (messages.length === 0) {
			return <EmptyChatScreen />;
		}

		return (
			<div className="flex flex-1 flex-col">
				<div className="flex-1 space-y-6 overflow-y-auto px-6">
					<div
						ref={scrollRef}
						className="space-y-4 py-6"
					>
						{messages.map((message, messageIndex) => {
							const isLatestResponse = messageIndex === messages.length - 1;
							const shouldHideResponseWhileLoading = isLoading && isLatestResponse;
							let activeResponse: Response | undefined;
							if (
								!shouldHideResponseWhileLoading &&
								message.response &&
								message.response.length > 0
							) {
								activeResponse = message.response.find((resp) => !!resp.isActive);
								if (!activeResponse) {
									activeResponse = message.response[message.response.length - 1];
								}
							}

							return (
								<div
									key={message.id}
									className={isLatestResponse && isLoading ? 'min-h-[50vh]' : ''}
								>
									<UserMessageComponent
										prompt={message.prompt.text}
										attachments={message.prompt.attachments}
										onEdit={(model, modifiedPrompt) =>
											onEdit(
												model as ImageModelDisplayName,
												message.id,
												modifiedPrompt,
												{
													existingAttachments: message.prompt.attachments,
												}
											)
										}
										isEditAvailable={
											!isLoading && !isSharedChat && isLatestResponse
										}
									/>
									{activeResponse ? (
										<ZecoMessageComponent
											id={`${message.id}:${message.response!.indexOf(activeResponse)}`}
											prompt={message.prompt.text}
											modelName={activeResponse.modelUsed.modelName}
											attachments={activeResponse.images?.map(
												({ type, name, url }) => ({
													name,
													contentType: type,
													url,
												})
											)}
											responseCount={message.response!.length}
											isNewResponseInProgress={isLoading}
											isLatestResponse={isLatestResponse}
											isSharedResponse={isSharedChat}
											onRegenerate={(model, data) => {
												data.existingAttachments =
													message.prompt.attachments;
												onRegenerate(
													message.id,
													model as ImageModelDisplayName,
													data
												);
											}}
											onNavigate={(targetIndex) => {
												onNavigate(message.id, targetIndex);
											}}
											type="image"
										/>
									) : (
										<ImagePlaceholder
											n={
												(message.prompt.params as ImageGenerationParams)
													?.n ?? 1
											}
										/>
									)}
								</div>
							);
						})}
						<div ref={messagesRef} />
					</div>
				</div>
			</div>
		);
	}
);

ImageGenerationPanel.displayName = 'ImageGenerationPanel';
export default ImageGenerationPanel;
