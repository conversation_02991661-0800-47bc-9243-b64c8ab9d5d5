import { Attachment as SDKAttachment } from 'ai';
import { useRef, useState, useMemo, useCallback, useEffect } from 'react';
import { Textarea } from '@heroui/input';
import { Button } from '@heroui/button';
import { Tooltip } from '@heroui/tooltip';
import { Chip } from '@heroui/chip';
import { useDisclosure } from '@heroui/modal';
import {
	IconPlayerStopFilled,
	IconWorld,
	IconPaperclip,
	IconArrowUp,
	IconWand,
	IconBolt,
	IconSettings,
} from '@tabler/icons-react';
import ImageParamsInlineControls from './ImageParamsInlineControls';
import VideoParamsInlineControls from './VideoParamsInlineControls';
import ImageParamsModal from './ImageParamsModal';
import VideoParamsModal from './VideoParamsModal';
import { ModelDisplayName } from '@/models/conversational/conversational-models';
import { ImageModelDisplayName } from '@/models/image/image-generation-models';
import { VideoModelDisplayName } from '@/models/video/video-generation-models';
import {
	ChatModelParams,
	getSupportedFileTypes,
	supportsWebSearch,
} from '@/models/conversational/conversational-model-constraints';
import {
	getSupportedImageTypesForImageModels,
	ImageGenerationParams,
} from '@/models/image/image-generation-constraints';
import {
	getSupportedImageTypesForVideoModels,
	VideoGenerationParams,
} from '@/models/video/video-generation-constraints';
import {
	ModelType,
	useMediaGenerationModeContext,
	useModelContext,
	useSelectedModelPromptDetailsContext,
} from '@/app/(post-auth)/providers';
import { Attachment, ChatType } from '@/types/chat';
import {
	calculateImageCreditCost,
	calculateVideoCreditCost,
} from '@/service/managers/credit-cost-manager';

export interface SubmitData {
	files?: FileList;
	existingAttachments?: Attachment[] | SDKAttachment[];
	isWebSearchEnabled?: boolean;
	params?: ChatModelParams | ImageGenerationParams | VideoGenerationParams;
}

interface PromptSectionState {
	files?: FileList;
	isWebSearchEnabled?: boolean;
	params?: ChatModelParams | ImageGenerationParams | VideoGenerationParams;
}

interface PromptSectionProps {
	type: ChatType;
	input: string;
	setInput: React.Dispatch<React.SetStateAction<string>>;
	isLoading: boolean;
	onSubmit: (model: ModelType, data: SubmitData) => void;
	isPromptEnhancementInProgress?: boolean;
	onPromptEnhancement?: (model: ModelType) => void;
	stop?: () => void;
	isDisabled?: boolean;
}

const PromptSection = ({
	type,
	input,
	isLoading,
	setInput,
	onSubmit,
	isPromptEnhancementInProgress = false,
	onPromptEnhancement,
	stop,
	isDisabled = false,
}: PromptSectionProps) => {
	const model = useModelContext();
	const { prompt: defaultPrompt, params: defaultParams } = useSelectedModelPromptDetailsContext();
	const isMediaEditMode = useMediaGenerationModeContext() === 'EDIT';
	const fileInputRef = useRef<HTMLInputElement>(null);

	const [state, setState] = useState<PromptSectionState>({});
	const resetPromptSectionState = useCallback(() => {
		setState({});
		if (fileInputRef.current) {
			fileInputRef.current.value = '';
		}
	}, []);

	// Parameters modal state
	const {
		isOpen: isParamsModalOpen,
		onOpen: onParamsModalOpen,
		onOpenChange: onParamsModalOpenChange,
	} = useDisclosure();

	useEffect(() => {
		resetPromptSectionState();
	}, [model, resetPromptSectionState]);

	useEffect(() => {
		if (type === 'image' || type === 'video') {
			setInput(defaultPrompt);
			setState((prev) => ({
				...prev,
				params: defaultParams,
			}));
		}
	}, [type, defaultPrompt, defaultParams, setInput]);

	const isSubmitDisabled =
		isDisabled || isLoading || isPromptEnhancementInProgress || !input.trim();

	const onSubmitHandler = useCallback(() => {
		const { files, isWebSearchEnabled, params } = state;
		onSubmit(model, {
			files,
			isWebSearchEnabled,
			params,
		});
		resetPromptSectionState();
	}, [model, onSubmit, resetPromptSectionState, state]);

	const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === 'Enter' && !e.shiftKey) {
			e.preventDefault();
			if (!isSubmitDisabled) {
				onSubmitHandler();
			}
		}
	};

	const removeFile = useCallback(
		(fileName: string) => {
			if (!state.files) {
				return;
			}
			const dt = new DataTransfer();
			Array.from(state.files).forEach((file) => {
				if (file.name !== fileName) {
					dt.items.add(file);
				}
			});
			setState((prev) => ({
				...prev,
				files: dt.files.length === 0 ? undefined : dt.files,
			}));
			if (fileInputRef.current) {
				fileInputRef.current.files = dt.files;
			}
		},
		[state.files]
	);

	const handleFileChange = useCallback(
		(event: React.ChangeEvent<HTMLInputElement>) => {
			if (event.target.files?.length) {
				const newFiles = event.target.files;
				const dt = new DataTransfer();

				// Add existing files
				if (state.files) {
					Array.from(state.files).forEach((file) => {
						dt.items.add(file);
					});
				}

				// Add new files
				Array.from(newFiles).forEach((file) => {
					dt.items.add(file);
				});

				setState((prev) => ({
					...prev,
					files: dt.files,
				}));
			}
		},
		[state.files]
	);

	const handleInlineParamsChange = (newParams: ImageGenerationParams | VideoGenerationParams) => {
		setState((prev) => ({
			...prev,
			params: { ...prev.params, ...newParams },
		}));
	};

	const renderFileChips = () => {
		if (!state.files || state.files.length === 0) {
			return null;
		}

		return (
			<div className="flex gap-2 border-x-8 border-transparent py-2 overflow-x-auto">
				{Array.from(state.files).map((file, index) => (
					<Chip
						key={index}
						size="sm"
						variant="flat"
						className="bg-white/10 text-white/90 shrink-0"
						onClose={() => removeFile(file.name)}
						classNames={{
							base: 'h-6',
							content: 'text-xs',
						}}
					>
						{file.name}
					</Chip>
				))}
			</div>
		);
	};

	const isEnabledClasses = (isEnabled: boolean) =>
		isEnabled
			? 'bg-blue-50 text-blue-500 border-blue-200 dark:bg-blue-950 dark:text-blue-400 dark:border-blue-900'
			: 'text-neutral-500 border-neutral-200 dark:border-neutral-700 dark:text-neutral-400';

	const textAreaClasses =
		'bg-[#101010] data-[focus=true]:bg-[#101010] data-[hover=true]:bg-[#101010]';

	const supportedFileTypes = useMemo(() => {
		if (type === 'chat') {
			return getSupportedFileTypes(model as ModelDisplayName);
		} else if (type === 'image') {
			return getSupportedImageTypesForImageModels(model as ImageModelDisplayName);
		} else {
			return getSupportedImageTypesForVideoModels(model as VideoModelDisplayName);
		}
	}, [type, model]);

	const showFileAttachment = !isMediaEditMode && supportedFileTypes.length > 0;

	const webSearchSupported = useMemo(() => {
		return type === 'chat' && supportsWebSearch(model as ModelDisplayName);
	}, [type, model]);

	const isPromptEnhancementAvailable = useMemo(
		() => type !== 'chat' && !isMediaEditMode,
		[type, isMediaEditMode]
	);

	const estimatedCredits = useMemo(() => {
		if (!model) return null;
		if (type === 'image') {
			return calculateImageCreditCost(model as ImageModelDisplayName, state.params as any);
		}
		if (type === 'video') {
			return calculateVideoCreditCost(model as VideoModelDisplayName, state.params as any);
		}
		return null;
	}, [model, state.params, type]);

	const getPlaceholder = () => {
		if (type === 'image') {
			return isMediaEditMode
				? 'Describe your edits...'
				: "Describe the image you'd like to create...";
		} else if (type === 'video') {
			return "Describe the video you'd like to generate...";
		} else {
			return "What's on your mind today?";
		}
	};

	return (
		<>
			<div
				className={`w-full flex flex-col ${isMediaEditMode ? 'space-y-3 px-3 py-3 rounded-2xl' : 'space-y-4 px-6 py-4 rounded-3xl'} bg-[#101010]`}
			>
				{renderFileChips()}

				<Textarea
					isDisabled={isPromptEnhancementInProgress}
					value={input}
					onChange={(event) => setInput(event.target.value)}
					onKeyDown={handleKeyDown}
					placeholder={getPlaceholder()}
					variant="flat"
					minRows={isMediaEditMode ? 1 : 2}
					maxRows={6}
					aria-label={`${type} prompt section`}
					classNames={{
						base: `${textAreaClasses} w-full`,
						input: 'text-white/90 placeholder:text-white/60',
						inputWrapper: `${textAreaClasses} group-data-[focus=true]:bg-[#101010]`,
					}}
				/>
				{estimatedCredits !== null && (
					<Tooltip
						content="Estimated credits required for this generation"
						placement="right"
						size="sm"
						radius="sm"
						className="bg-gray-50 text-[#090909]"
						delay={500}
						closeDelay={0}
					>
						<div className="text-xs text-gray-400 mt-1 text-right flex items-center justify-end gap-1">
							<IconBolt
								size={14}
								className="text-zeco-purple"
							/>
							<span className="font-semibold">{estimatedCredits}</span>
						</div>
					</Tooltip>
				)}

				<div className="flex flex-row gap-2 w-full items-center">
					{/* Desktop controls */}
					<div className="hidden md:flex flex-row gap-2 w-full items-center">
						{type === 'chat' && (
							<Tooltip
								content="Search the Web"
								placement="bottom"
								size="sm"
								radius="sm"
								className="bg-gray-50 text-[#090909]"
								delay={500}
								closeDelay={0}
							>
								<Button
									disableRipple
									variant="bordered"
									size="md"
									radius="full"
									onPress={() =>
										setState((prev) => ({
											...prev,
											isWebSearchEnabled: !prev.isWebSearchEnabled,
										}))
									}
									startContent={<IconWorld size={20} />}
									className={`gap-1 px-2 border-1 ${isEnabledClasses(!!state.isWebSearchEnabled)}`}
								>
									<span className="text-xs">Search</span>
								</Button>
							</Tooltip>
						)}
						{/* Desktop: Inline controls */}
						{type === 'image' && (
							<ImageParamsInlineControls
								model={model as ImageModelDisplayName}
								params={(state.params as ImageGenerationParams) ?? {}}
								onChange={handleInlineParamsChange}
							/>
						)}
						{type === 'video' && (
							<VideoParamsInlineControls
								model={model as VideoModelDisplayName}
								params={(state.params as VideoGenerationParams) ?? {}}
								onChange={handleInlineParamsChange}
							/>
						)}
					</div>

					{/* Mobile controls */}
					<div className="flex md:hidden flex-row gap-2 w-full items-center">
						{type === 'chat' && (
							<Tooltip
								content="Search the Web"
								placement="bottom"
								size="sm"
								radius="sm"
								className="bg-gray-50 text-[#090909]"
								delay={500}
								closeDelay={0}
							>
								<button
									onClick={() =>
										setState((prev) => ({
											...prev,
											isWebSearchEnabled: !prev.isWebSearchEnabled,
										}))
									}
									className={`p-2 rounded-full transition-colors ${
										state.isWebSearchEnabled
											? 'text-blue-500 bg-blue-50 dark:bg-blue-950'
											: 'text-white/70 hover:text-white/90 hover:bg-white/5'
									}`}
								>
									<IconWorld size={20} />
								</button>
							</Tooltip>
						)}
						{/* Mobile: Settings icon for image/video */}
						{(type === 'image' || type === 'video') && (
							<Tooltip
								content="Settings"
								placement="bottom"
								size="sm"
								radius="sm"
								className="bg-gray-50 text-[#090909]"
								delay={500}
								closeDelay={0}
							>
								<button
									onClick={onParamsModalOpen}
									className="p-2 rounded-full transition-colors text-white/70 hover:text-white/90 hover:bg-white/5"
								>
									<IconSettings size={20} />
								</button>
							</Tooltip>
						)}
					</div>

					<div className="flex flex-row gap-2 justify-end ml-auto">
						{showFileAttachment && (
							<>
								<Tooltip
									content={type === 'chat' ? 'Attach files' : 'Remix with Image'}
									placement="bottom"
									size="sm"
									radius="sm"
									className="bg-gray-50 text-[#090909]"
									delay={500}
									closeDelay={0}
								>
									<Button
										disableRipple
										isIconOnly
										variant="light"
										radius="full"
										size="md"
										onPress={() => fileInputRef.current?.click()}
										className={isEnabledClasses(!!state.files)}
									>
										<IconPaperclip size={18} />
									</Button>
								</Tooltip>

								<input
									type="file"
									ref={fileInputRef}
									onChange={handleFileChange}
									className="hidden"
									accept={supportedFileTypes.map((ft) => ft.extension).join(',')}
									multiple={false}
								/>
							</>
						)}

						{isPromptEnhancementAvailable && (
							<Tooltip
								content="Enhance Prompt"
								placement="bottom"
								size="sm"
								radius="sm"
								className="bg-gray-50 text-[#090909]"
								delay={500}
								closeDelay={0}
							>
								<Button
									disableRipple
									isIconOnly
									isDisabled={isSubmitDisabled}
									isLoading={isPromptEnhancementInProgress}
									color="secondary"
									variant="light"
									radius="full"
									size="md"
									onPress={() => {
										onPromptEnhancement?.(model);
									}}
								>
									<IconWand size={18} />
								</Button>
							</Tooltip>
						)}

						{isLoading ? (
							<Tooltip
								content="Stop generating"
								placement="bottom"
								size="sm"
								radius="sm"
								className="bg-gray-50 text-[#090909]"
								delay={500}
								closeDelay={0}
							>
								<Button
									disableRipple
									isIconOnly
									variant="light"
									radius="full"
									size="md"
									onPress={stop}
									className="text-red-500 ml-auto"
								>
									<IconPlayerStopFilled size={18} />
								</Button>
							</Tooltip>
						) : (
							<Tooltip
								content="Send message"
								placement="bottom"
								size="sm"
								radius="sm"
								className="bg-gray-50 text-[#090909]"
								delay={500}
								closeDelay={0}
							>
								<Button
									disableRipple
									isIconOnly
									isDisabled={isSubmitDisabled}
									variant="light"
									radius="full"
									size="md"
									onPress={onSubmitHandler}
								>
									<IconArrowUp size={18} />
								</Button>
							</Tooltip>
						)}
					</div>
				</div>
			</div>

			{/* Parameters Modals */}
			{type === 'image' && (
				<ImageParamsModal
					isOpen={isParamsModalOpen}
					onOpenChange={onParamsModalOpenChange}
					model={model as ImageModelDisplayName}
					defaultParams={(state.params as ImageGenerationParams) ?? {}}
					onApply={handleInlineParamsChange}
				/>
			)}

			{type === 'video' && (
				<VideoParamsModal
					isOpen={isParamsModalOpen}
					onOpenChange={onParamsModalOpenChange}
					model={model as VideoModelDisplayName}
					defaultParams={(state.params as VideoGenerationParams) ?? {}}
					onApply={handleInlineParamsChange}
				/>
			)}
		</>
	);
};

export default PromptSection;
