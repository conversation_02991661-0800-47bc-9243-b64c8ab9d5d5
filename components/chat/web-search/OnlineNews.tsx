import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { Card, CardBody } from '@heroui/card';
import { IconExternalLink, IconNews, IconCalendar } from '@tabler/icons-react';
import { NewsResults as NewsResultsType } from '@/types/web-search-results';

interface OnlineNewsProps {
	results: NewsResultsType[];
}

export const OnlineNews: React.FC<OnlineNewsProps> = ({ results }) => {
	if (!results.length) {
		return null;
	}

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
			className="w-full"
		>
			<Card
				disableRipple
				radius="lg"
				classNames={{
					base: 'w-full bg-transparent border-none shadow-none',
					body: 'space-y-6',
				}}
			>
				<CardBody>
					{results.map((news, index) => (
						<motion.div
							key={`${news.title}-${index}`}
							initial={{ opacity: 0, x: -20 }}
							animate={{ opacity: 1, x: 0 }}
							transition={{ duration: 0.3, delay: index * 0.1 }}
						>
							<Card
								disableRipple
								isPressable
								as={Link}
								href={news.link}
								target="_blank"
								rel="noopener noreferrer"
								radius="lg"
								classNames={{
									base: 'bg-[#101010] hover:bg-sidebar-hover/50 hover:shadow-2xl hover:scale-[1.02] transition-all duration-300',
									body: 'flex flex-row gap-4 p-4',
								}}
							>
								<CardBody>
									{news.thumbnail && (
										<div className="relative w-24 h-24 shrink-0 overflow-hidden rounded-lg bg-[#1a1a1a]">
											<Image
												src={news.thumbnail}
												alt={news.title}
												fill
												className="object-cover hover:scale-110 transition-transform duration-300"
											/>
										</div>
									)}
									<div className="flex-1 min-w-0 space-y-2">
										<div className="flex items-start justify-between gap-2">
											<div className="flex-1 min-w-0">
												<h4 className="text-sm font-medium text-primary-text line-clamp-2">
													{news.title}
												</h4>
												<div className="flex items-center gap-2 mt-1">
													<div className="flex items-center gap-1">
														<IconNews
															size={12}
															className="text-zeco-purple/80 shrink-0"
														/>
														<span className="text-xs text-secondary-text/80">
															{news.source}
														</span>
													</div>
													{news.date && (
														<>
															<span className="text-xs text-secondary-text/40">
																•
															</span>
															<div className="flex items-center gap-1">
																<IconCalendar
																	size={12}
																	className="text-zeco-purple/80 shrink-0"
																/>
																<span className="text-xs text-secondary-text/80">
																	{news.date}
																</span>
															</div>
														</>
													)}
												</div>
											</div>
											<IconExternalLink
												size={14}
												className="text-secondary-text/40 shrink-0 mt-1"
											/>
										</div>
										{news.snippet && (
											<p className="text-sm text-secondary-text/80 line-clamp-2">
												{news.snippet}
											</p>
										)}
									</div>
								</CardBody>
							</Card>
						</motion.div>
					))}
				</CardBody>
			</Card>
		</motion.div>
	);
};
