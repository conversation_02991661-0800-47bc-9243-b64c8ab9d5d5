import { useRef } from 'react';
import { motion } from 'motion/react';
import { Card, CardBody } from '@heroui/card';
import { Button } from '@heroui/button';
import Image from 'next/image';
import Link from 'next/link';
import { IconExternalLink, IconChevronsRight, IconChevronsLeft } from '@tabler/icons-react';
import { InlineImages as InlineImagesType } from '@/types/web-search-results';

interface OnlineImagesFoundProps {
	results: InlineImagesType[];
}

export const OnlineImagesFound: React.FC<OnlineImagesFoundProps> = ({ results }) => {
	const scrollContainerRef = useRef<HTMLDivElement>(null);

	if (!results.length) {
		return null;
	}

	const scroll = (direction: 'left' | 'right') => {
		const container = scrollContainerRef.current;
		if (!container) return;

		const scrollAmount = container.clientWidth * 0.8;
		container.scrollBy({
			left: direction === 'left' ? -scrollAmount : scrollAmount,
			behavior: 'smooth',
		});
	};

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
			className="w-full"
		>
			<Card
				disableRipple
				radius="lg"
				classNames={{
					base: 'w-full bg-transparent border-none shadow-none',
					body: 'relative',
				}}
			>
				<CardBody>
					<Button
						isIconOnly
						disableRipple
						radius="full"
						variant="flat"
						onPress={() => scroll('left')}
						className="absolute top-1/2 left-0 z-10 -translate-y-1/2 bg-[#101010]"
					>
						<IconChevronsLeft
							size={20}
							className="text-zeco-purple/80"
						/>
					</Button>
					<div
						ref={scrollContainerRef}
						className="scrollbar-hide flex w-full gap-4 overflow-x-auto scroll-smooth px-4"
					>
						{results.map((image, index) => (
							<motion.div
								key={`${image.title}-${index}`}
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{ duration: 0.3, delay: index * 0.1 }}
								className="w-[280px] flex-none"
							>
								<Card
									disableRipple
									isPressable
									as={Link}
									href={image.original}
									target="_blank"
									rel="noopener noreferrer"
									radius="lg"
									classNames={{
										base: 'transition-all duration-300 hover:shadow-2xl hover:translate-y-[-2px] h-full bg-[#101010] hover:bg-[#1a1a1a]',
										body: 'p-0',
									}}
								>
									<CardBody>
										<div className="relative aspect-square overflow-hidden">
											<Image
												src={image.thumbnail} // TODO: Use image.original
												alt={image.title}
												fill
												className="object-cover transition-transform duration-500 hover:scale-110"
											/>
											<div className="absolute inset-0 flex flex-col justify-end bg-linear-to-t from-black/90 via-black/50 to-transparent p-4 opacity-0 transition-all duration-300 hover:opacity-100">
												<div className="flex items-start justify-between gap-2">
													<div className="flex-1 space-y-1">
														<p className="line-clamp-2 text-sm font-medium text-primary-text">
															{image.title}
														</p>
														<p className="text-xs text-secondary-text/80">
															{image.source}
														</p>
													</div>
													<IconExternalLink
														size={14}
														className="mt-1 text-secondary-text/40"
													/>
												</div>
											</div>
										</div>
									</CardBody>
								</Card>
							</motion.div>
						))}
					</div>
					<Button
						isIconOnly
						disableRipple
						radius="full"
						variant="flat"
						onPress={() => scroll('right')}
						className="absolute top-1/2 right-0 z-10 -translate-y-1/2 bg-[#101010]"
					>
						<IconChevronsRight
							size={20}
							className="text-zeco-purple/80"
						/>
					</Button>
				</CardBody>
			</Card>
		</motion.div>
	);
};
