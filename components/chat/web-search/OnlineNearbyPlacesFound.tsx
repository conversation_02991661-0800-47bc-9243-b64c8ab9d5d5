import { motion } from 'motion/react';
import Image from 'next/image';
import Link from 'next/link';
import { Card, CardBody, CardFooter } from '@heroui/card';
import {
	IconMapPin,
	IconCircleCheck,
	IconStarFilled,
	IconClock,
	IconExternalLink,
} from '@tabler/icons-react';
import { LocalResults as LocalResultsType } from '@/types/web-search-results';

interface OnlineNearbyPlacesFoundProps {
	results: LocalResultsType;
}

export const OnlineNearbyPlacesFound: React.FC<OnlineNearbyPlacesFoundProps> = ({ results }) => {
	if (!results.places?.length) {
		return null;
	}

	return (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
		>
			<Card
				disableRipple
				radius="lg"
				classNames={{
					base: 'bg-transparent',
					body: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8',
					footer: 'flex flex-row items-center justify-end',
				}}
			>
				<CardBody>
					{results.places.map((place, index) => {
						const placeUrl = place.gps_coordinates
							? `https://www.google.com/maps?q=${place.gps_coordinates.latitude},${place.gps_coordinates.longitude}`
							: place.place_id_search || '#';
						return (
							<motion.div
								key={`${place.title}-${index}`}
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{ duration: 0.3, delay: index * 0.1 }}
							>
								<Card
									disableRipple
									isPressable
									as={Link}
									href={placeUrl}
									target="_blank"
									rel="noopener noreferrer"
									radius="lg"
									classNames={{
										base: 'bg-[#101010] hover:bg-sidebar-hover/50 hover:scale-[1.02] transition-all duration-300',
										body: 'p-0',
									}}
								>
									<CardBody>
										{place.thumbnail && (
											<div className="relative h-40 w-full overflow-hidden">
												<Image
													src={place.thumbnail}
													alt={place.title}
													fill
													className="object-cover transition-transform duration-300 hover:scale-110"
												/>
											</div>
										)}
										<div className="space-y-2 p-3">
											<div className="flex items-center justify-between gap-2">
												<div className="flex-1">
													<h4 className="line-clamp-2 text-sm font-medium text-primary-text">
														{place.title}
													</h4>
												</div>
												<IconExternalLink
													size={14}
													className="text-secondary-text/40"
												/>
											</div>
											{(place.rating || place.reviews) && (
												<div className="flex items-center gap-2">
													{place.rating && (
														<div className="flex items-center gap-1">
															<IconStarFilled
																size={14}
																className="text-yellow-400"
															/>
															<span className="text-xs text-primary-text">
																{place.rating}
															</span>
														</div>
													)}
													{place.reviews && (
														<span className="text-xs text-secondary-text/80">
															({place.reviews} reviews)
														</span>
													)}
												</div>
											)}
											{place.address && (
												<div className="flex items-center gap-2 text-xs text-secondary-text/80">
													<IconMapPin
														size={14}
														className="shrink-0 text-zeco-purple"
													/>
													<span className="line-clamp-2">
														{place.address}
													</span>
												</div>
											)}
											{place.hours && (
												<div className="flex items-center gap-2 text-xs text-secondary-text/80">
													<IconClock
														size={14}
														className="text-zeco-purple"
													/>
													<span>{place.hours}</span>
												</div>
											)}
											{place.service_options &&
												Object.keys(place.service_options).length > 0 && (
													<div className="flex flex-wrap gap-2">
														{Object.entries(place.service_options).map(
															([key, value]) => {
																if (!value) {
																	return null;
																}
																return (
																	<div
																		key={key}
																		className="flex items-center gap-1 rounded-full bg-card/20 py-1 text-xs text-secondary-text/80"
																	>
																		<IconCircleCheck
																			size={12}
																			className="text-zeco-purple"
																		/>
																		<span>
																			{key.replace(/_/g, ' ')}
																		</span>
																	</div>
																);
															}
														)}
													</div>
												)}
											{place.price && (
												<div className="text-xs text-secondary-text/80">
													Price: {place.price}
												</div>
											)}
										</div>
									</CardBody>
								</Card>
							</motion.div>
						);
					})}
				</CardBody>
				<CardFooter>
					{results.more_locations_link && (
						<Link
							href={results.more_locations_link}
							target="_blank"
							rel="noopener noreferrer"
							className="flex items-center gap-1 text-xs text-secondary-text/80 transition-all duration-300 hover:text-zeco-purple"
						>
							View More
							<IconExternalLink size={12} />
						</Link>
					)}
				</CardFooter>
			</Card>
		</motion.div>
	);
};
