import { Attachment as SDKAttachment } from 'ai';
import Image from 'next/image';
import { useRef, useState } from 'react';
import { EditIcon } from '@/components/icons';
import { Button } from '@heroui/button';
import { Chip } from '@heroui/chip';
import { Textarea } from '@heroui/input';
import { Tooltip } from '@heroui/tooltip';
import { IconCheck, IconCornerDownRight, IconX } from '@tabler/icons-react';
import { ModelType, useModelContext } from '@/app/(post-auth)/providers';
import { Attachment } from '@/types/chat';

interface UserMessageProps {
	prompt: string;
	attachments?: Attachment[] | SDKAttachment[];
	onEdit: (model: ModelType, prompt: string) => void;
	isEditAvailable: boolean;
}

const getAttachmentMimeType = (file: Attachment | SDKAttachment) =>
	(file as any).type || (file as any).contentType || '';

const UserMessageComponent: React.FC<UserMessageProps> = ({
	prompt,
	attachments,
	onEdit,
	isEditAvailable,
}) => {
	const model = useModelContext();
	const formRef = useRef<HTMLFormElement>(null);
	const [isEditing, setIsEditing] = useState(false);
	const [editedPrompt, setEditedPrompt] = useState(prompt);

	const imageAttachments = (attachments || []).filter((file) =>
		getAttachmentMimeType(file).startsWith('image/')
	);
	const nonImageAttachments = (attachments || []).filter(
		(file) => !getAttachmentMimeType(file).startsWith('image/')
	);

	const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();
		if (editedPrompt.trim() !== prompt.trim()) {
			onEdit(model, editedPrompt.trim());
		}
		setIsEditing(false);
	};

	const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === 'Enter' && !e.shiftKey) {
			e.preventDefault();
			if (formRef.current?.requestSubmit) {
				formRef.current.requestSubmit();
			} else {
				// Fallback for older browsers
				formRef.current?.dispatchEvent(
					new Event('submit', { cancelable: true, bubbles: true })
				);
			}
		}
	};

	const handleCancel = () => {
		setEditedPrompt(prompt);
		setIsEditing(false);
	};

	return (
		<div className="flex flex-col space-y-2 mb-6">
			<div className="flex items-center gap-2 justify-end group">
				{!isEditing && isEditAvailable && (
					<Tooltip
						content="Edit message"
						placement="bottom"
						size="sm"
						radius="sm"
						className="bg-gray-50 text-[#090909]"
						delay={500}
						closeDelay={0}
					>
						<Button
							disableRipple
							isIconOnly
							size="sm"
							variant="light"
							radius="full"
							onPress={() => setIsEditing(true)}
							className="opacity-0 group-hover:opacity-100 transition-opacity self-center"
						>
							<EditIcon
								size={16}
								className="text-secondary-text hover:text-white"
							/>
						</Button>
					</Tooltip>
				)}
				<div className={`${isEditing ? 'flex-grow' : 'flex-shrink'} max-w-[85%]`}>
					{isEditing ? (
						<form
							ref={formRef}
							onSubmit={handleSubmit}
							className="w-full flex flex-col space-y-4 bg-[#101010] px-6 py-4 rounded-3xl"
						>
							<Textarea
								value={editedPrompt}
								onChange={(e) => setEditedPrompt(e.target.value)}
								onKeyDown={handleKeyDown}
								variant="flat"
								minRows={2}
								maxRows={4}
								classNames={{
									base: 'w-full bg-[#101010]',
									input: 'text-white/90 placeholder:text-white/60',
									inputWrapper:
										'bg-[#101010] data-[focus=true]:bg-[#101010] data-[hover=true]:bg-[#101010] group-data-[focus=true]:bg-[#101010]',
								}}
							/>
							<div className="flex flex-row gap-2 justify-end">
								<Button
									disableRipple
									size="sm"
									variant="light"
									radius="full"
									onPress={handleCancel}
									startContent={<IconX size={16} />}
									className="text-neutral-500 border-neutral-200 dark:border-neutral-700 dark:text-neutral-400"
								>
									Cancel
								</Button>
								<Button
									type="submit"
									disableRipple
									size="sm"
									variant="light"
									radius="full"
									isDisabled={
										!editedPrompt.trim() ||
										editedPrompt.trim() === prompt.trim()
									}
									startContent={<IconCheck size={16} />}
									className="bg-blue-50 text-blue-500 border-blue-200 dark:bg-blue-950 dark:text-blue-400 dark:border-blue-900"
								>
									Save
								</Button>
							</div>
						</form>
					) : (
						<div className="text-primary-text bg-[#101010] rounded-2xl px-4 py-2.5 break-words backdrop-blur-sm">
							{prompt}
							{nonImageAttachments.length > 0 && (
								<div className="flex flex-wrap gap-2 mt-2">
									{nonImageAttachments.map((file, index) => (
										<Chip
											key={index}
											size="sm"
											variant="dot"
										>
											{file.name || 'Attached file'}
										</Chip>
									))}
								</div>
							)}
						</div>
					)}
				</div>
			</div>

			{imageAttachments.length > 0 && (
				<div className="flex flex-wrap gap-2 w-full justify-end items-center">
					<IconCornerDownRight
						size={16}
						className="text-secondary-text"
					/>
					{imageAttachments.map((file, index) => (
						<div
							key={index}
							className="relative w-12 h-12 rounded-lg overflow-hidden border border-white/10"
						>
							<Image
								src={file.url}
								alt={file.name || 'Attached image'}
								fill
								className="object-cover rounded-lg"
								unoptimized
							/>
						</div>
					))}
				</div>
			)}
		</div>
	);
};

export default UserMessageComponent;
