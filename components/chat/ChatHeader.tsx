'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { IconLayoutSidebar, IconCrown, IconLoader2, IconMenu2 } from '@tabler/icons-react';
import { Button, ButtonGroup } from '@heroui/button';
import { Tooltip } from '@heroui/tooltip';
import { useDisclosure } from '@heroui/modal';
import MobileSidebar from '@/components/sidebar/MobileSidebar';
import ModelSelectButton from './model-selection/ModelSelectButton';
import {
	useMediaGenerationModeContext,
	useMediaGenerationModeUpdateContext,
	useSidebarVisibility,
	useSidebarVisibilityUpdate,
	useUserContext,
} from '@/app/(post-auth)/providers';
import { ShareChatButton } from './ShareChatButton';
import { FeedbackButton } from './FeedbackButton';
import CreditsDisplay from './CreditsDisplay';
import { initiateSubscriptionPaymentAction } from '@/app/subscriptions/actions';
import { addToast } from '@heroui/toast';
import UpgradeToProModal from './model-selection/UpgradeToProModal';

const ChatHeader = () => {
	const isSidebarOpen = useSidebarVisibility();
	const setIsSidebarOpen = useSidebarVisibilityUpdate();

	const mediaGenerationMode = useMediaGenerationModeContext();
	const updateMediaGenerationMode = useMediaGenerationModeUpdateContext();
	const isMediaGenerationAvailable = mediaGenerationMode !== 'NONE';
	const isMediaEditMode = mediaGenerationMode === 'EDIT';
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

	const user = useUserContext();
	const isPro = user?.user_metadata?.is_pro_user || false;

	// Pro upgrade modal for edit feature
	const {
		isOpen: isUpgradeModalOpen,
		onOpen: onUpgradeModalOpen,
		onOpenChange: onUpgradeModalOpenChange,
	} = useDisclosure();

	const handleUpgrade = async () => {
		setIsSubmitting(true);
		try {
			const result = await initiateSubscriptionPaymentAction('zeco-pro-monthly-plan');
			if (result.success) {
				addToast({
					title: 'Redirecting to payment',
					description: 'You will be redirected to complete your payment.',
					color: 'primary',
				});

				// No need to refresh - metadata updates automatically

				window.location.href = result.paymentLink;
			} else {
				// Handle different error types from server
				if (result.isProcessing) {
					addToast({
						title: 'Payment Already Processing',
						description: 'Your payment is already being processed. Please wait.',
						color: 'warning',
					});
				} else {
					addToast({
						title: 'Payment Failed',
						description: result.error.message,
						color: 'danger',
					});
				}
			}
		} catch (error) {
			addToast({
				title: 'Payment Error',
				description: 'An unexpected error occurred while processing your payment.',
				color: 'danger',
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	// Simple button state - server action handles all logic
	const isButtonDisabled = isSubmitting;

	const getButtonText = () => {
		if (isSubmitting) return 'Initiating...';
		return 'Upgrade';
	};

	const getTooltipText = () => {
		if (isSubmitting) return 'Setting up your payment...';
		return 'Upgrade to Pro';
	};

	return (
		<>
			{/* Mobile Sidebar */}
			<MobileSidebar
				isOpen={isMobileSidebarOpen}
				onClose={() => setIsMobileSidebarOpen(false)}
			/>
			<div className="flex w-full flex-row items-center p-3">
				{/* Mobile: Hamburger menu button */}
				<Button
					isIconOnly
					size="md"
					variant="light"
					onPress={() => setIsMobileSidebarOpen(true)}
					className="mr-1 block md:hidden"
				>
					<IconMenu2 size={20} />
				</Button>
				{/* Desktop: Original sidebar toggle with hover functionality */}
				<AnimatePresence
					mode="wait"
					initial={false}
				>
					{!isSidebarOpen && (
						<motion.div
							animate={{ opacity: 1, scale: 1 }}
							transition={{ duration: 0.2 }}
							className="hidden md:flex md:flex-row"
						>
							<Tooltip
								content="Hover left edge to show sidebar"
								placement="bottom"
								size="sm"
								radius="sm"
								className="bg-gray-50 text-[#090909]"
								delay={500}
								closeDelay={0}
							>
								<Button
									isIconOnly
									size="sm"
									variant="light"
									onPress={() => setIsSidebarOpen(true)}
								>
									<IconLayoutSidebar size={20} />
								</Button>
							</Tooltip>
						</motion.div>
					)}
				</AnimatePresence>
				<ModelSelectButton />
				<CreditsDisplay className="ml-0 sm:ml-2 md:ml-4" />
				<div className="flex grow justify-end gap-2 md:gap-4">
					{/* Only show Create & Edit on desktop */}
					{isMediaGenerationAvailable && (
						<ButtonGroup
							variant="light"
							size="sm"
							radius="sm"
							className="hidden md:flex"
						>
							<Button
								disableRipple
								color={!isMediaEditMode ? 'secondary' : undefined}
								variant={!isMediaEditMode ? 'flat' : undefined}
								onPress={() => {
									updateMediaGenerationMode('CREATE');
								}}
								className={isMediaEditMode ? 'hover:bg-transparent!!' : undefined}
							>
								Create
							</Button>
							<Button
								disableRipple
								color={isMediaEditMode ? 'secondary' : undefined}
								variant={isMediaEditMode ? 'flat' : undefined}
								onPress={() => {
									if (!isPro) {
										onUpgradeModalOpen();
									} else {
										updateMediaGenerationMode('EDIT');
									}
								}}
								className={!isMediaEditMode ? 'hover:bg-transparent!!' : undefined}
							>
								Edit
							</Button>
						</ButtonGroup>
					)}
					{/* Only show upgrade button to free users */}
					{!isPro && (
						<>
							{/* Desktop: Button with text */}
							<div className="hidden md:flex">
								<Tooltip
									content={getTooltipText()}
									placement="bottom"
									size="sm"
									radius="sm"
									className="bg-gray-50 text-[#090909]"
									delay={500}
									closeDelay={0}
								>
									<Button
										size="sm"
										variant="flat"
										onPress={handleUpgrade}
										disabled={isButtonDisabled}
										className="ml-2 border-[#9455D3]/20 text-[#9455D3] hover:bg-[#9455D3]/10"
										endContent={
											isButtonDisabled ? (
												<IconLoader2
													size={16}
													className="animate-spin text-[#9455D3]"
												/>
											) : (
												<IconCrown
													size={16}
													color="#9455D3"
													fill="#9455D3"
												/>
											)
										}
									>
										{getButtonText()}
									</Button>
								</Tooltip>
							</div>
							{/* Mobile: Icon only */}
							<div className="flex md:hidden">
								<Tooltip
									content={getTooltipText()}
									placement="bottom"
									size="sm"
									radius="sm"
									className="bg-gray-50 text-[#090909]"
									delay={500}
									closeDelay={0}
								>
									<Button
										isIconOnly
										size="sm"
										variant="flat"
										onPress={handleUpgrade}
										disabled={isButtonDisabled}
										className="border-[#9455D3]/20 text-[#9455D3] hover:bg-[#9455D3]/10"
									>
										{isButtonDisabled ? (
											<IconLoader2
												size={16}
												className="animate-spin text-[#9455D3]"
											/>
										) : (
											<IconCrown
												size={16}
												color="#9455D3"
												fill="#9455D3"
											/>
										)}
									</Button>
								</Tooltip>
							</div>
						</>
					)}
					<ShareChatButton />
					<FeedbackButton />
				</div>

				{/* Upgrade to Pro Modal for Edit Feature */}
				<UpgradeToProModal
					isOpen={isUpgradeModalOpen}
					onOpenChange={onUpgradeModalOpenChange}
					modelName="Flux 1.1 Pro"
					modelType="image"
				/>
			</div>
		</>
	);
};

export default ChatHeader;
