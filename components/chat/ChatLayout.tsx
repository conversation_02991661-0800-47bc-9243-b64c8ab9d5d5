'use client';

import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Message, useChat } from 'ai/react';
import {
	useUserContext,
	useModelUpdateContext,
	useChatHistoryUpdateContext,
	useSharedChatUpdateContext,
	useSidebarVisibility,
	ModelType,
} from '@/app/(post-auth)/providers';
import ChatPanel from '@/components/chat/ChatPanel';
import PromptSection, { SubmitData } from './PromptSection';
import { ChatMessage } from '@/types/chat';
import {
	convertToSDKMessages,
	createAssistantMessage,
	createResponseId,
	calculateTotalTokensUsed,
	MAX_CONVERSATION_TOKENS,
} from '@/utils/chat/chat-helpers';
import {
	ModelDisplayName,
	modelDisplayNameToTechnicalNameMap,
} from '@/models/conversational/conversational-models';
import { FloatingDock } from '@/components/ui/FloatingDock';
import { Logo } from '@/components/icons';
import { TypewriterEffectSmooth } from '@/components/ui/TypeWriterEffect';
import { addToast } from '@heroui/toast';
import useUserLocation from '@/lib/hooks/useUserLocation';

interface ChatSnapshot {
	prompt?: string;
	messages: Message[];
	responseMap: Record<string, Message[]>;
	isNewChat: boolean;
	isWebSearchEnabled: boolean;
	submissionPromptId: string;
	submissionModel: string;
}
export interface ChatProps extends React.ComponentProps<'div'> {
	id: string;
	messages: ChatMessage[];
	isShared?: boolean;
}

const ChatLayout = memo(({ id, messages: initialMessages, isShared = false }: ChatProps) => {
	const user = useUserContext();
	const { location } = useUserLocation();
	const setChatHistory = useChatHistoryUpdateContext();
	const updateSharedChatDetails = useSharedChatUpdateContext();
	const updateActiveModel = useModelUpdateContext();
	const isSidebarOpen = useSidebarVisibility();

	const chatSnapshot = useRef<ChatSnapshot>({
		prompt: '',
		messages: [],
		responseMap: {},
		isNewChat: false,
		isWebSearchEnabled: false,
		submissionPromptId: '',
		submissionModel: '',
	});

	const [isSharedChat, setIsSharedChat] = useState<boolean>(isShared);
	const [responseMap, setResponseMap] = useState<Record<string, Message[]>>(() =>
		initialMessages.reduce(
			(acc, message) => {
				if (message.response?.length) {
					acc[message.id] = message.response.map((response, index) =>
						createAssistantMessage(message, response, index)
					);
				}
				return acc;
			},
			{} as Record<string, Message[]>
		)
	);

	const [totalTokensUsed, setTotalTokensUsed] = useState<number>(() =>
		calculateTotalTokensUsed(initialMessages, true)
	);

	const { handleSubmit, input, isLoading, messages, setMessages, setInput, reload, stop } =
		useChat({
			id,
			initialMessages: convertToSDKMessages(initialMessages),
			sendExtraMessageFields: true,
			maxSteps: 5,
			onError(error) {
				const errorMessage = error.message;
				let toastDescription = 'An unexpected error occurred. Please try again.';
				try {
					const parsedError = JSON.parse(errorMessage);
					toastDescription = parsedError.message || toastDescription;
				} catch (e) {
					// fallback to the generic error message for the toast.
				}

				addToast({
					title: 'Error generating response',
					description: toastDescription,
					color: 'danger',
				});

				const { prompt, messages, responseMap } = chatSnapshot.current;
				setMessages(messages);
				setResponseMap(responseMap);
				if (prompt) {
					setInput(prompt);
				}
			},
			onFinish(message, { usage }) {
				if (usage?.totalTokens) {
					setTotalTokensUsed((prev) => prev + usage.totalTokens);
				}
				setMessages((prev) => {
					chatSnapshot.current.submissionPromptId = prev[prev.length - 2].id;
					return prev.map((msg) =>
						msg.id === message.id
							? {
									...message,
									id: createResponseId(
										chatSnapshot.current.submissionPromptId,
										responseMap[chatSnapshot.current.submissionPromptId]
											?.length || 0
									),
									data: { modelName: chatSnapshot.current.submissionModel },
								}
							: msg
					);
				});

				setResponseMap((prev) => {
					const responses = prev[chatSnapshot.current.submissionPromptId] || [];
					const newMessage = {
						...message,
						id: createResponseId(
							chatSnapshot.current.submissionPromptId,
							responses.length
						),
						data: { modelName: chatSnapshot.current.submissionModel },
					};

					return {
						...prev,
						[chatSnapshot.current.submissionPromptId]: [...responses, newMessage],
					};
				});

				const currentTime = new Date().toISOString();
				if (chatSnapshot.current.isNewChat || isSharedChat) {
					window.history.replaceState(null, '', `${origin}/chat/${id}`);
					// Create new chat history
					setChatHistory((existingChatHistory) => [
						{
							chatId: id,
							chatType: 'chat',
							title: input,
							userId: user.id,
							historyContext: '',
							createdAt: currentTime,
							lastModifiedAt: currentTime,
						},
						...(existingChatHistory || []),
					]);
					setIsSharedChat(false);
					chatSnapshot.current.isNewChat = false;
				} else {
					// Update last modified time of current chat history
					setChatHistory((existingChatHistory) =>
						(existingChatHistory || []).map((chatHistory) =>
							chatHistory.chatId === id
								? {
										...chatHistory,
										lastModifiedAt: currentTime,
									}
								: chatHistory
						)
					);
				}
			},
		});

	const saveSnapshot = useCallback(
		(
			model: ModelDisplayName,
			prompt?: string,
			isNewChat: boolean = false,
			isWebSearchEnabled?: boolean
		) => {
			chatSnapshot.current = {
				prompt,
				messages,
				responseMap,
				isNewChat,
				submissionPromptId: '',
				isWebSearchEnabled:
					isWebSearchEnabled !== undefined
						? isWebSearchEnabled
						: chatSnapshot.current.isWebSearchEnabled,
				submissionModel: modelDisplayNameToTechnicalNameMap[model],
			};
		},
		[messages, responseMap]
	);

	const handleSubmitPrompt = useCallback(
		(modelName: ModelType, data: SubmitData) => {
			if (totalTokensUsed >= MAX_CONVERSATION_TOKENS) {
				addToast({
					title: 'Conversation Limit Reached',
					description:
						'This conversation has reached the maximum token limit (128K). Please click "Chat" in the sidebar to start a new conversation.',
					color: 'warning',
				});
				return;
			}

			const model = modelName as ModelDisplayName;
			saveSnapshot(model, input, messages.length === 0, !!data.isWebSearchEnabled);

			handleSubmit(undefined, {
				body: {
					model,
					isNewChat: chatSnapshot.current.isNewChat,
					isSharedChat,
					sharedMessages: isSharedChat ? initialMessages : undefined,
					requestType: 'submit',
					location,
				},
				data: {
					isWebSearchEnabled: !!data.isWebSearchEnabled,
					...data.params,
				},
				experimental_attachments: data.files,
			});
		},
		[
			handleSubmit,
			initialMessages,
			input,
			isSharedChat,
			messages.length,
			saveSnapshot,
			location,
			totalTokensUsed,
		]
	);

	const handleEditPrompt = useCallback(
		(model: ModelDisplayName, promptId: string, input: string, data: SubmitData) => {
			saveSnapshot(model);

			setResponseMap((prev) => {
				const { [promptId]: _, ...rest } = prev;
				return rest;
			});

			setMessages((prev) =>
				prev.map((msg) => (msg.id === promptId ? { ...msg, content: input } : msg))
			);

			reload({
				body: {
					model,
					requestType: 'edit',
					location,
				},
				// Currently, model-specific params are not supported in 'edit' flow.
				data: {
					isWebSearchEnabled: chatSnapshot.current.isWebSearchEnabled,
				},
				// TODO: Warn users if the model does not support this modality.
				experimental_attachments: data.existingAttachments,
			}).catch((error) => {
				console.error(
					`Failed to edit prompt and regenerate response.\nSelected Model: ${model}\n${error}`
				);
				addToast({
					title: 'Error editing prompt',
					description: 'Failed to edit and regenerate response. Please try again.',
					color: 'danger',
				});
			});
		},
		[location, reload, saveSnapshot, setMessages]
	);

	const handleRegenerateResponse = useCallback(
		(model: ModelDisplayName, data: SubmitData) => {
			saveSnapshot(model);

			reload({
				body: {
					model,
					requestType: 'regenerate',
					location,
				},
				// Currently, model-specific params are not sent in 'regenerate' flow.
				data: {
					isWebSearchEnabled: chatSnapshot.current.isWebSearchEnabled,
				},
				// TODO: Warn users if the model does not support this modality.
				experimental_attachments: data.existingAttachments,
			})
				.then(() => {
					updateActiveModel(model);
				})
				.catch((error) => {
					console.error(
						`Failed to regenerate response.\nSelected Model: ${model}\n${error}`
					);
					addToast({
						title: 'Error regenerating response',
						description: 'Failed to regenerate response. Please try again.',
						color: 'danger',
					});
				});
		},
		[location, reload, saveSnapshot, updateActiveModel]
	);

	const handleStop = useCallback(() => {
		stop();
		const { prompt, messages, responseMap } = chatSnapshot.current;
		setMessages(messages);
		setResponseMap(responseMap);
		if (prompt) {
			setInput(prompt);
		}
	}, [setInput, setMessages, stop]);

	const handleNavigate = useCallback(
		(promptId: string, currentResponseId: string, requestedResponseIndex: number) => {
			const responses = responseMap[promptId];
			if (!responses?.length) {
				return;
			}

			const targetResponse = responses.find(
				(response) => response.id === createResponseId(promptId, requestedResponseIndex)
			);

			if (targetResponse) {
				setMessages((prev) =>
					prev.map((msg) => (msg.id === currentResponseId ? targetResponse : msg))
				);
			}
		},
		[responseMap, setMessages]
	);

	useEffect(() => {
		updateSharedChatDetails({
			type: 'chat',
			chatId: id,
			isShareable: !isSharedChat && messages.length > 1,
		});
	}, [id, isSharedChat, messages.length, updateSharedChatDetails]);

	const PromptSectionComponent = (
		<PromptSection
			type="chat"
			input={input}
			isLoading={isLoading}
			setInput={setInput}
			stop={handleStop}
			onSubmit={handleSubmitPrompt}
		/>
	);

	return (
		<div
			className={`h-full w-full max-w-3xl flex flex-col transition-all duration-300 ${isSidebarOpen ? 'md:ml-[280px]' : ''}`}
		>
			<AnimatePresence mode="wait">
				{messages.length === 0 ? (
					// Dashboard Mode - No initial animations, only exit animation
					<motion.div
						key="dashboard"
						className="h-full flex flex-col"
						exit={{ opacity: 0 }}
						transition={{ duration: 0.2 }}
					>
						<div className="flex-1 flex flex-col items-center justify-center space-y-8 mb-12">
							<div className="flex flex-col items-center justify-center space-y-4">
								<div className="p-3 bg-black border-zeco-purple border-1.5 rounded-2xl relative group transition duration-300 ease-in-out">
									<Logo
										size={36}
										fill="#ccc"
										className="flex md:hidden"
									/>
									<Logo
										size={48}
										fill="#ccc"
										className="hidden md:flex"
									/>
								</div>
								<TypewriterEffectSmooth
									words={[
										{
											text: 'Everything',
											className: 'text-zinc-800 dark:text-zinc-200',
										},
										{
											text: 'AI',
											className: 'text-zinc-800 dark:text-zinc-200',
										},
										{
											text: 'for',
											className: 'text-zinc-800 dark:text-zinc-200',
										},
										{
											text: 'YOU.',
											className: 'text-gradient-primary font-bold',
										},
									]}
									className="text-center"
								/>
							</div>

							<div className="w-full max-w-2xl mx-auto px-4 sm:px-0">
								{PromptSectionComponent}
							</div>
						</div>

						<div className="flex items-center justify-center w-full mb-6">
							<FloatingDock />
						</div>
					</motion.div>
				) : (
					// Chat Mode - Keep animation for entering chat mode
					<motion.div
						key="chat"
						className="flex-1 flex flex-col w-full h-full"
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -20 }}
						transition={{ duration: 0.2, ease: 'easeInOut' }}
					>
						<main className="flex flex-col flex-1 overflow-y-auto w-full">
							<ChatPanel
								messages={messages}
								responseMap={responseMap}
								isLoading={isLoading}
								isSharedChat={isSharedChat}
								onEdit={handleEditPrompt}
								onRegenerate={handleRegenerateResponse}
								onNavigate={handleNavigate}
							/>
						</main>
						<footer className="flex flex-col w-full px-6 pb-4">
							{PromptSectionComponent}
							<div className="mt-2 relative w-full">
								<p className="text-xs text-white/40 text-center">
									These models can make mistakes. Consider checking important
									information.
								</p>
							</div>
						</footer>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
});

ChatLayout.displayName = 'ChatLayout';
export default ChatLayout;
