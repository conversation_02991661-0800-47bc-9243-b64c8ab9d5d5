'use client';

import { IconBolt, IconLoader2 } from '@tabler/icons-react';
import { Chip } from '@heroui/chip';
import { Tooltip } from '@heroui/tooltip';
import { useCredits } from '@/lib/hooks/use-credits';

interface CreditsDisplayProps {
	className?: string;
}

const CreditsDisplay = ({ className }: CreditsDisplayProps) => {
	const { creditData, isLoading, error } = useCredits();

	if (isLoading) {
		return (
			<Chip
				variant="flat"
				size="sm"
				className={`bg-default-100 text-default-600 ${className}`}
				startContent={
					<IconLoader2
						size={14}
						className="animate-spin"
					/>
				}
			></Chip>
		);
	}

	if (error || !creditData) {
		return null;
	}

	const credits = creditData.credits;

	const getTooltipContent = () => {
		const remaining = credits;
		let examples = '';

		if (remaining >= 2) {
			examples = `\n\nExample usage:\n• ${Math.floor(remaining / 0.5)} SDXL images\n• ${Math.floor(remaining / 2)} DALL·E 3 images\n• ${Math.floor(remaining / 4)} video clips`;
		}

		return `You have ${remaining} credits remaining${examples}`;
	};

	return (
		<Tooltip
			content={getTooltipContent()}
			placement="bottom"
			size="sm"
			radius="sm"
			className="max-w-xs bg-gray-50 text-[#090909]"
			delay={500}
			closeDelay={0}
		>
			<Chip
				variant="flat"
				size="sm"
				className={`pointer-none cursor-default ${className}`}
				startContent={
					<IconBolt
						size={14}
						className="text-zeco-purple"
					/>
				}
			>
				{credits}
			</Chip>
		</Tooltip>
	);
};

export default CreditsDisplay;
