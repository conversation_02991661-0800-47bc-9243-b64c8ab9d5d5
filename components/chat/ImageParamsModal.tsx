import { useEffect, useState } from 'react';
import Image from 'next/image';
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	Mo<PERSON><PERSON>eader,
	ModalBody,
	ModalFooter,
	useDisclosure,
} from '@heroui/modal';
import { Button } from '@heroui/button';
import { Select, SelectItem } from '@heroui/select';
import { Slider } from '@heroui/slider';
import {
	IMAGE_MODEL_CONSTRAINTS,
	ImageGenerationParams,
} from '@/models/image/image-generation-constraints';
import { ImageModelDisplayName } from '@/models/image/image-generation-models';
import { IconInfoCircle } from '@tabler/icons-react';
import ModelParamsInfo from './ModelParamsInfo';

interface ImageParamsModalProps {
	model: ImageModelDisplayName;
	isOpen: boolean;
	onOpenChange: () => void;
	onApply: (params: ImageGenerationParams) => void;
	onReset?: () => void;
	defaultParams?: ImageGenerationParams;
}

const ImageParamsModal = ({
	model,
	isOpen,
	onOpenChange,
	onApply,
	onReset,
	defaultParams,
}: ImageParamsModalProps) => {
	const modelConstraints = IMAGE_MODEL_CONSTRAINTS[model];
	const infoDrawerDisclosure = useDisclosure();

	const [n, setNumberOfImages] = useState<number>();
	const [quality, setQuality] = useState<string>();
	const [style, setStyle] = useState<string>();
	const [background, setBackground] = useState<string>();
	const [aspectRatio, setAspectRatio] = useState<`${number}:${number}`>();
	const [size, setSize] = useState<`${number}x${number}`>();
	const [imageSize, setImageSize] = useState<string>();
	const [colorPalette, setColorPalette] = useState<string>();
	const [inferenceSteps, setInferenceSteps] = useState<number>();
	const [guidanceScale, setGuidanceScale] = useState<number>();

	const resetValues = () => {
		setNumberOfImages(undefined);
		setQuality(undefined);
		setStyle(undefined);
		setBackground(undefined);
		setAspectRatio(undefined);
		setSize(undefined);
		setImageSize(undefined);
		setColorPalette(undefined);
		setInferenceSteps(undefined);
		setGuidanceScale(undefined);
		onReset?.();
	};

	const handleApply = () => {
		const params: ImageGenerationParams = {
			n,
			quality,
			style,
			background,
			aspectRatio,
			size,
			image_size: imageSize,
			color_palette: colorPalette,
			num_inference_steps: inferenceSteps,
			guidance_scale: guidanceScale,
		};
		onApply(params);
	};

	useEffect(() => {
		if (defaultParams) {
			setNumberOfImages(defaultParams.n);
			setQuality(defaultParams.quality);
			setStyle(defaultParams.style);
			setBackground(defaultParams.background);
			setAspectRatio(defaultParams.aspectRatio);
			setSize(defaultParams.size);
			setImageSize(defaultParams.image_size);
			setColorPalette(defaultParams.color_palette);
			setInferenceSteps(defaultParams.num_inference_steps);
			setGuidanceScale(defaultParams.guidance_scale);
		}
	}, [defaultParams]);

	return (
		<>
			<Modal
				isOpen={isOpen}
				onOpenChange={onOpenChange}
				isDismissable={false}
				scrollBehavior="outside"
				size="md"
				classNames={{
					header: 'flex flex-row gap-1 items-center justify-center',
					body: 'flex flex-col',
				}}
			>
				<ModalContent>
					{(onClose) => (
						<>
							<ModalHeader>
								<span>Image Generation Parameters</span>
								<IconInfoCircle
									size={20}
									className="cursor-pointer"
									onClick={infoDrawerDisclosure.onOpen}
								/>
							</ModalHeader>
							<ModalBody>
								{modelConstraints.n && modelConstraints.n.max > 1 && (
									<Slider
										className="max-w-md cursor-pointer"
										label="Number of Images"
										value={n || modelConstraints.n.min}
										maxValue={modelConstraints.n.max}
										minValue={modelConstraints.n.min}
										onChange={(value) => setNumberOfImages(value as number)}
										size="sm"
										step={modelConstraints.n.step}
									/>
								)}

								{modelConstraints.style && (
									<Select
										className="max-w-md"
										label="Style"
										variant="bordered"
										selectedKeys={[style || '']}
										onChange={(e) => setStyle(e.target.value)}
										items={modelConstraints.style}
										renderValue={(items) => {
											return items.map((item) => (
												<span key={item.key}>{item.data?.label}</span>
											));
										}}
									>
										{(styleOption) => (
											<SelectItem
												key={styleOption.value}
												textValue={styleOption.label}
											>
												<div className="flex items-center gap-3">
													{styleOption.exampleImage && (
														<div className="relative h-32 w-32 overflow-hidden rounded">
															<Image
																src={styleOption.exampleImage}
																alt={styleOption.label}
																fill
																className="object-cover"
																sizes="128px"
															/>
														</div>
													)}
													<div className="flex w-full flex-col">
														<span className="text-sm font-medium">
															{styleOption.label}
														</span>
														{styleOption.description && (
															<span className="text-xs break-words whitespace-normal text-default-500">
																{styleOption.description}
															</span>
														)}
													</div>
												</div>
											</SelectItem>
										)}
									</Select>
								)}

								{modelConstraints.background && (
									<Select
										className="max-w-md"
										label="Background"
										variant="bordered"
										selectedKeys={[background || '']}
										onChange={(e) => setBackground(e.target.value)}
										items={modelConstraints.background}
										renderValue={(items) => {
											return items.map((item) => (
												<span key={item.key}>{item.data?.label}</span>
											));
										}}
									>
										{(backgroundOption) => (
											<SelectItem
												key={backgroundOption.value}
												textValue={backgroundOption.label}
											>
												<div className="flex items-center gap-3">
													{backgroundOption.exampleImage && (
														<div className="relative h-32 w-32 overflow-hidden rounded">
															<Image
																src={backgroundOption.exampleImage}
																alt={backgroundOption.label}
																fill
																className="object-cover"
																sizes="128px"
															/>
														</div>
													)}
													<span className="text-sm font-medium">
														{backgroundOption.label}
													</span>
												</div>
											</SelectItem>
										)}
									</Select>
								)}

								{modelConstraints.quality && (
									<Select
										className="max-w-md"
										label="Quality"
										variant="bordered"
										selectedKeys={[quality || '']}
										onChange={(e) => setQuality(e.target.value)}
									>
										{modelConstraints.quality.map((value) => (
											<SelectItem key={value}>
												{value.charAt(0).toUpperCase() +
													value.slice(1).toLowerCase()}
											</SelectItem>
										))}
									</Select>
								)}

								{modelConstraints.aspectRatio && (
									<Select
										className="max-w-md"
										label="Aspect Ratio"
										variant="bordered"
										selectedKeys={[aspectRatio || '']}
										onChange={(e) =>
											setAspectRatio(e.target.value as `${number}:${number}`)
										}
									>
										{modelConstraints.aspectRatio.allowedRatios.map((value) => (
											<SelectItem key={value}>{value}</SelectItem>
										))}
									</Select>
								)}

								{modelConstraints.size && (
									<Select
										className="max-w-md"
										label="Size"
										variant="bordered"
										selectedKeys={[size || '']}
										onChange={(e) =>
											setSize(e.target.value as `${number}x${number}`)
										}
									>
										{modelConstraints.size.allowedSizes.map((value) => (
											<SelectItem key={value}>{value}</SelectItem>
										))}
									</Select>
								)}

								{modelConstraints.image_size && (
									<Select
										className="max-w-md"
										label="Image Size"
										variant="bordered"
										selectedKeys={[imageSize || '']}
										onChange={(e) => setImageSize(e.target.value)}
									>
										{modelConstraints.image_size.map((value) => {
											// Format the display value to be more user-friendly
											const displayValue = value
												.replace('_', ' ')
												.split(' ')
												.map(
													(word) =>
														word.charAt(0).toUpperCase() + word.slice(1)
												)
												.join(' ');

											return (
												<SelectItem
													key={value}
													title={displayValue}
												/>
											);
										})}
									</Select>
								)}

								{modelConstraints.color_palette && (
									<Select
										className="max-w-md"
										label="Color Palette"
										variant="bordered"
										selectedKeys={[colorPalette || '']}
										onChange={(e) => setColorPalette(e.target.value)}
									>
										{modelConstraints.color_palette.map((option) => (
											<SelectItem
												key={option.value}
												textValue={option.label}
											>
												<div className="flex items-center gap-3">
													<div className="flex -space-x-1 overflow-hidden">
														{option.colors?.map((color, colorIndex) => (
															<div
																key={colorIndex}
																className="h-5 w-5 rounded-full"
																style={{ backgroundColor: color }}
															/>
														))}
													</div>
													<div className="flex flex-col">
														<span className="text-sm font-medium">
															{option.label}
														</span>
														{option.description && (
															<span className="text-xs break-words whitespace-normal text-default-500">
																{option.description}
															</span>
														)}
													</div>
												</div>
											</SelectItem>
										))}
									</Select>
								)}

								{modelConstraints.num_inference_steps && (
									<Slider
										className="max-w-md cursor-pointer"
										label="Number of Inference Steps"
										value={
											inferenceSteps ||
											modelConstraints.num_inference_steps.min
										}
										maxValue={modelConstraints.num_inference_steps.max}
										minValue={modelConstraints.num_inference_steps.min}
										onChange={(value) => setInferenceSteps(value as number)}
										size="sm"
										step={modelConstraints.num_inference_steps.step}
									/>
								)}

								{modelConstraints.guidance_scale && (
									<Slider
										className="max-w-md cursor-pointer"
										label="Guidance Scale"
										value={guidanceScale || modelConstraints.guidance_scale.min}
										maxValue={modelConstraints.guidance_scale.max}
										minValue={modelConstraints.guidance_scale.min}
										onChange={(value) => setGuidanceScale(value as number)}
										size="sm"
										step={modelConstraints.guidance_scale.step}
									/>
								)}
							</ModalBody>
							<ModalFooter>
								<Button
									disableRipple
									variant="flat"
									onPress={onClose}
								>
									Cancel
								</Button>
								<Button
									disableRipple
									variant="flat"
									onPress={() => {
										resetValues();
									}}
								>
									Reset
								</Button>
								<Button
									disableRipple
									color="primary"
									variant="flat"
									onPress={() => {
										handleApply();
										onClose();
									}}
								>
									Apply
								</Button>
							</ModalFooter>
						</>
					)}
				</ModalContent>
			</Modal>
			<ModelParamsInfo
				type="image"
				isOpen={infoDrawerDisclosure.isOpen}
				onClose={infoDrawerDisclosure.onClose}
			/>
		</>
	);
};

export default ImageParamsModal;
