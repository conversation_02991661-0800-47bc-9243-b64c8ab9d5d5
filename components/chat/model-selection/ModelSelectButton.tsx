'use client';

import { IconCameraSearch, IconChevronDown } from '@tabler/icons-react';
import { Button } from '@heroui/button';
import { useDisclosure } from '@heroui/modal';
import { Tooltip } from '@heroui/tooltip';
import { useState, useEffect } from 'react';
import {
	ModelType,
	useMediaGenerationModeContext,
	useModelContext,
	useModelUpdateContext,
	useSelectedModelPromptDetailsUpdateContext,
} from '@/app/(post-auth)/providers';
import {
	ConversationalModelsList,
	ModelDisplayName,
	modelDisplayNameToProviderMap,
} from '@/models/conversational/conversational-models';
import { GeneratedImageDetails } from '@/models/image/image-generation-examples';
import {
	ImageModelDisplayName,
	imageModelDisplayNameToProviderMap,
	ImageModelsList,
} from '@/models/image/image-generation-models';
import {
	VideoModelDisplayName,
	videoModelDisplayNameToProviderMap,
	VideoModelsList,
} from '@/models/video/video-generation-models';
import ModelSelectionDialog from './ModelSelectionDialog';
import { getProviderIcon } from '../../../utils/model-selection-utils';
import { ChatType } from '@/types/chat';
import MediaModelSelectionDropdown from './MediaModelSelectionDropdown';
import ModelSelectionDropdown from './ModelSelectionDropdown';

const ModelSelectButton = () => {
	const { isOpen, onOpen, onOpenChange } = useDisclosure();
	const selectedModel = useModelContext();
	const setSelectedModel = useModelUpdateContext();
	const isMediaEditMode = useMediaGenerationModeContext() === 'EDIT';
	const updateSelectedModelImageDetails = useSelectedModelPromptDetailsUpdateContext();

	let type: ChatType, selectedModelProvider, models;

	if (Object.values(ModelDisplayName).includes(selectedModel as ModelDisplayName)) {
		type = 'chat';
		selectedModelProvider = modelDisplayNameToProviderMap[selectedModel as ModelDisplayName];
		models = ConversationalModelsList;
	} else if (
		Object.values(ImageModelDisplayName).includes(selectedModel as ImageModelDisplayName)
	) {
		type = 'image';
		selectedModelProvider =
			imageModelDisplayNameToProviderMap[selectedModel as ImageModelDisplayName];
		models = ImageModelsList;
	} else {
		type = 'video';
		selectedModelProvider =
			videoModelDisplayNameToProviderMap[selectedModel as VideoModelDisplayName];
		models = VideoModelsList;
	}

	const handleModelSelect = (model: ModelType, example?: GeneratedImageDetails) => {
		setSelectedModel(model);
		if (example) {
			updateSelectedModelImageDetails({
				prompt: example.prompt,
				params: example.parameters,
			});
		}
	};

	return (
		<>
			{type === 'chat' ? (
				<>
					{/* Desktop: Original conversational dropdown */}
					<div className="hidden md:flex">
						<Button
							disableRipple
							variant="light"
							className="hover:bg-transparent"
							onPress={onOpen}
							endContent={<IconChevronDown size={16} />}
						>
							<div className="flex items-center gap-2">
								<div className="shrink-0">
									{getProviderIcon(selectedModelProvider, type, 20)}
								</div>
								<span>{selectedModel}</span>
							</div>
						</Button>
					</div>
					{/* Mobile: Use ModelSelectionDropdown */}
					<div className="flex md:hidden">
						<ModelSelectionDropdown
							type={type as 'chat'}
							models={models as any}
							selectedModel={selectedModel}
							selectedModelProvider={selectedModelProvider}
							onModelSelect={(model: string) => handleModelSelect(model as ModelType)}
						/>
					</div>
				</>
			) : (
				<>
					{/* Desktop only: Show explore button */}
					<div className="hidden items-center md:flex">
						{!isMediaEditMode && (
							<Tooltip
								content="Explore Ideas"
								placement="bottom"
								size="sm"
								radius="sm"
								className="bg-gray-50 text-[#090909]"
								delay={500}
								closeDelay={0}
							>
								<Button
									isIconOnly
									disableRipple
									size="sm"
									variant="light"
									onPress={onOpen}
								>
									<IconCameraSearch size={20} />
								</Button>
							</Tooltip>
						)}
						<MediaModelSelectionDropdown
							type={type}
							models={models as any}
							selectedModel={selectedModel as any}
							selectedModelProvider={selectedModelProvider as any}
							onModelSelect={handleModelSelect}
							isDropdownDisabled={isMediaEditMode}
						/>
					</div>
					{/* Mobile: Use ModelSelectionDropdown */}
					<div className="flex md:hidden">
						<ModelSelectionDropdown
							type={type as 'image' | 'video'}
							models={models as any}
							selectedModel={selectedModel}
							selectedModelProvider={selectedModelProvider}
							onModelSelect={(model: string) => handleModelSelect(model as ModelType)}
						/>
					</div>
				</>
			)}
			<ModelSelectionDialog
				isOpen={isOpen}
				onOpenChange={onOpenChange}
				type={type}
				models={models}
				selectedModel={selectedModel}
				onModelSelect={handleModelSelect}
			/>
		</>
	);
};

export default ModelSelectButton;
