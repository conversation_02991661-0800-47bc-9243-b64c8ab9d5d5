import { motion, AnimatePresence } from 'framer-motion';
import { useState, useRef, useEffect } from 'react';
import { Card, CardFooter } from '@heroui/card';
import { Chip } from '@heroui/chip';
import { IconPlayerPlay } from '@tabler/icons-react';
import { GeneratedVideoDetails } from '@/models/video/video-generation-examples';
import { useIntersectionObserver } from '@/lib/hooks/use-intersection-observer';
import { getProviderIcon } from '@/utils/model-selection-utils';
import {
	videoModelDisplayNameToProviderMap,
	VideoModelDisplayName,
} from '@/models/video/video-generation-models';
import { cn } from '@/utils/cn';

interface LazyVideoCardProps extends GeneratedVideoDetails {
	modelName: VideoModelDisplayName;
	onVideoCardClick: (video: GeneratedVideoDetails & { modelName: VideoModelDisplayName }) => void;
}

const VideoPlaceholder = () => {
	return (
		<div className="absolute inset-0 flex flex-col">
			{/* Video aspect ratio placeholder */}
			<div className="relative w-full h-full">
				<div className="absolute inset-0 rounded-lg bg-zeco-purple/20 animate-pulse" />
			</div>
			{/* Prompt placeholder */}
			<div className="p-3 space-y-2">
				<div className="h-4 w-3/4 bg-zeco-purple/20 rounded animate-pulse" />
				<div className="h-4 w-1/2 bg-zeco-purple/20 rounded animate-pulse" />
			</div>
		</div>
	);
};

const VideoCardSkeleton = () => {
	return (
		<Card
			classNames={{
				base: 'h-[300px] w-full',
				footer: 'flex flex-col items-start gap-1 p-3 min-h-0',
			}}
		>
			<VideoPlaceholder />
		</Card>
	);
};

export const LazyVideoCard = ({
	videoUrl,
	prompt,
	filter,
	parameters,
	modelName,
	onVideoCardClick,
}: LazyVideoCardProps) => {
	const { ref, hasBeenVisible } = useIntersectionObserver({
		threshold: 0.1,
		rootMargin: '100px',
		freezeOnceVisible: true,
	});

	const [isHovered, setIsHovered] = useState(false);
	const [isPlaying, setIsPlaying] = useState(false);
	const [isLoading, setIsLoading] = useState(true);
	const videoRef = useRef<HTMLVideoElement>(null);

	useEffect(() => {
		if (!hasBeenVisible) return;

		const video = videoRef.current;
		if (video) {
			const handleLoadedData = () => {
				setIsLoading(false);
			};
			video.addEventListener('canplaythrough', handleLoadedData);
			return () => {
				video.removeEventListener('canplaythrough', handleLoadedData);
			};
		}
	}, [hasBeenVisible]);

	// Don't render the actual video until it's been visible
	if (!hasBeenVisible) {
		return (
			<div ref={ref}>
				<VideoCardSkeleton />
			</div>
		);
	}

	const handleMouseEnter = () => {
		setIsHovered(true);
		if (videoRef.current && !isPlaying && !isLoading) {
			videoRef.current.muted = true;
			videoRef.current.play().catch(() => {
				// Autoplay might be blocked by browser policy
				console.log('Autoplay prevented');
			});
		}
	};

	const handleMouseLeave = () => {
		setIsHovered(false);
		if (videoRef.current && !isPlaying) {
			videoRef.current.pause();
			videoRef.current.currentTime = 0;
		}
	};

	const handleClick = () => {
		if (videoRef.current && !isLoading) {
			if (isPlaying) {
				videoRef.current.pause();
				setIsPlaying(false);
			} else {
				videoRef.current.play();
				setIsPlaying(true);
			}
		}
		onVideoCardClick({ videoUrl, prompt, filter, parameters, modelName });
	};

	return (
		<div ref={ref}>
			<Card
				isHoverable
				isPressable
				isFooterBlurred
				disableRipple
				classNames={{
					base: 'h-[300px] w-full',
					footer: 'flex flex-col items-start gap-1 p-3 min-h-0',
				}}
				onPress={handleClick}
				onMouseEnter={handleMouseEnter}
				onMouseLeave={handleMouseLeave}
			>
				<div className="relative w-full h-full">
					{isLoading && <VideoPlaceholder />}
					<video
						ref={videoRef}
						src={videoUrl}
						className={cn('w-full h-full object-cover', isLoading && 'opacity-0')}
						preload="metadata"
						playsInline
						loop
						muted
						onPlay={() => setIsPlaying(true)}
						onPause={() => setIsPlaying(false)}
					/>
					{!isPlaying && !isLoading && (
						<div className="absolute inset-0 flex items-center justify-center bg-black/50 z-10">
							<IconPlayerPlay
								size={40}
								className="text-white opacity-70 group-hover:opacity-100 transition-opacity"
							/>
						</div>
					)}
				</div>
				<AnimatePresence>
					{isHovered && !isLoading && (
						<motion.div
							initial={{ opacity: 0 }}
							animate={{ opacity: 1 }}
							exit={{ opacity: 0 }}
							transition={{ duration: 0.1 }}
							className="absolute top-2 left-2 z-20"
						>
							{getProviderIcon(
								videoModelDisplayNameToProviderMap[modelName],
								'video',
								28
							)}
						</motion.div>
					)}
				</AnimatePresence>
				<AnimatePresence>
					{isHovered && !isLoading && (
						<motion.div
							initial={{ opacity: 0, y: 40 }}
							animate={{ opacity: 1, y: 0 }}
							exit={{ opacity: 0, y: 40 }}
							transition={{ duration: 0.1 }}
							className="absolute bottom-0 left-0 w-full z-10"
						>
							<CardFooter>
								<p className="text-xs text-white/80 truncate w-full">{prompt}</p>
								{filter && filter.length > 0 && (
									<div className="flex flex-row gap-1 overflow-x-auto w-full">
										{filter.map((f) => (
											<Chip
												key={f}
												size="sm"
												variant="flat"
												className="font-mono bg-zeco-purple/50 whitespace-nowrap"
											>
												#{f}
											</Chip>
										))}
									</div>
								)}
							</CardFooter>
						</motion.div>
					)}
				</AnimatePresence>
			</Card>
		</div>
	);
};
