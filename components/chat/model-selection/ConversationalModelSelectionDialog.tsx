import { AnimatePresence, motion } from 'motion/react';
import Image from 'next/image';
import { useState } from 'react';
import { ButtonGroup, Button } from '@heroui/button';
import { Card, CardBody, CardHeader, CardFooter } from '@heroui/card';
import { Chip } from '@heroui/chip';
import { Divider } from '@heroui/divider';
import { Input } from '@heroui/input';
import {
	Modal,
	ModalContent,
	ModalHeader,
	ModalBody,
	useDisclosure,
	ModalFooter,
} from '@heroui/modal';
import { Tabs, Tab } from '@heroui/tabs';
import { Tooltip } from '@heroui/tooltip';
import {
	IconSearch,
	IconLayoutGrid,
	IconStack2,
	IconCrown,
	IconInfoCircle,
} from '@tabler/icons-react';
import { ModelType, useUserContext } from '@/app/(post-auth)/providers';
import { getCategoryIcon, getProviderIcon } from '@/utils/model-selection-utils';
import { ModelSelectionDialogProps } from '@/components/chat/model-selection/ModelSelectionDialog';
import UpgradeToProModal from './UpgradeToProModal';
import {
	Modes,
	ConversationalModel,
	modelDisplayNameToProviderMap,
} from '@/models/conversational/conversational-models';
import { ModelTier } from '@/models/model-tiers';
import { cn } from '@/utils/cn';

const ModelCardRow = ({
	model,
	selected,
	selectedCategory,
	onSelect,
	onShowDetails,
}: {
	model: ConversationalModel;
	selected: boolean;
	selectedCategory: Modes;
	onSelect: () => void;
	onShowDetails: () => void;
}) => {
	const { key, tags, swePerf, tier } = model;
	return (
		<Card
			isPressable
			disableRipple
			onPress={onSelect}
			classNames={{
				base: `w-full overflow-visible ${selected ? 'border border-zeco-purple/50 bg-zeco-purple/5' : 'bg-transparent'}`,
				body: 'flex flex-row items-center gap-4',
				footer: 'gap-2',
			}}
		>
			<CardBody>
				{getProviderIcon(modelDisplayNameToProviderMap[key], 'chat', 32)}
				<div className="flex flex-col gap-1">
					<div className="flex flex-row items-center gap-1">
						<span className="text-sm">{key}</span>
						<Tooltip
							content="Model Facts"
							placement="bottom"
							size="sm"
							radius="sm"
							className="bg-gray-50 text-[#090909]"
							delay={500}
							closeDelay={0}
						>
							<IconInfoCircle
								size={16}
								onClick={(e) => {
									e.stopPropagation();
									onShowDetails();
								}}
							/>
						</Tooltip>
					</div>
					<div className="flex flex-row gap-1">
						{tags[selectedCategory]?.map((tag) => (
							<Chip
								key={tag}
								size="sm"
								variant="flat"
								className="bg-[#141414] text-[#A1A1AA]"
							>
								{tag}
							</Chip>
						))}
					</div>
				</div>
				<div className="flex flex-1 items-center justify-end gap-1">
					{tier === ModelTier.Premium && (
						<Chip
							size="sm"
							variant="light"
							radius="sm"
							className="bg-[#141414] text-[#9455D3]"
							startContent={
								<IconCrown
									color="#9455D3"
									fill="#9455D3"
									size={16}
								/>
							}
						>
							Pro
						</Chip>
					)}
					{selectedCategory === Modes.SoftwareEngineering && (
						<>
							{swePerf?.sweBenchVerified && (
								<Tooltip
									content={`Accuracy on SWE-bench Verified: ${swePerf.sweBenchVerified}`}
									placement="bottom"
									size="sm"
									radius="sm"
									className="bg-gray-50 text-[#090909]"
									delay={500}
									closeDelay={0}
								>
									<Chip
										size="sm"
										variant="flat"
										radius="sm"
										startContent={
											<Image
												src="/swebench.ico"
												width={12}
												height={12}
												alt="swe-bench logo"
											/>
										}
										className="items-center gap-0.5 bg-[#151B2A] pl-2 text-[#5C7EC7]"
									>
										{swePerf.sweBenchVerified}
									</Chip>
								</Tooltip>
							)}
							{swePerf?.codeforces && (
								<Tooltip
									content={`Codeforces Rating: ${swePerf.codeforces}`}
									placement="bottom"
									size="sm"
									radius="sm"
									className="bg-gray-50 text-[#090909]"
									delay={500}
									closeDelay={0}
								>
									<Chip
										size="sm"
										variant="flat"
										radius="sm"
										startContent={
											<Image
												src="/codeforces.png"
												width={12}
												height={12}
												alt="codeforces logo"
											/>
										}
										className="items-center gap-0.5 bg-[#331F1F] pl-2 text-[#FF7B7B]"
									>
										{swePerf.codeforces}
									</Chip>
								</Tooltip>
							)}
						</>
					)}
				</div>
			</CardBody>
		</Card>
	);
};

const ModelCardGrid = ({
	model,
	selected,
	selectedCategory,
	onSelect,
	onShowDetails,
}: {
	model: ConversationalModel;
	selected: boolean;
	selectedCategory: Modes;
	onSelect: () => void;
	onShowDetails: () => void;
}) => {
	const { key, tags, swePerf, tier } = model;
	return (
		<Card
			isPressable
			disableRipple
			onPress={onSelect}
			classNames={{
				header: 'justify-center',
				base: `px-4 h-[180px] w-full overflow-visible ${selected ? 'border border-zeco-purple/50 bg-zeco-purple/5' : 'bg-transparent'}`,
				body: 'flex flex-col items-center gap-1',
				footer: 'w-full overflow-x-auto',
			}}
		>
			<CardHeader>
				{getProviderIcon(modelDisplayNameToProviderMap[key], 'chat', 32)}
			</CardHeader>
			<CardBody>
				<div className="flex flex-row items-center justify-center gap-1">
					<span className="text-md">{key}</span>
					<Tooltip
						content="Model Facts"
						placement="bottom"
						size="sm"
						radius="sm"
						className="bg-gray-50 text-[#090909]"
						delay={500}
						closeDelay={0}
					>
						<IconInfoCircle
							size={16}
							onClick={(e) => {
								e.stopPropagation();
								onShowDetails();
							}}
						/>
					</Tooltip>
				</div>
				<div className="flex gap-2">
					{tier === ModelTier.Premium && (
						<Chip
							size="sm"
							variant="light"
							radius="sm"
							className="bg-[#141414] text-[#9455D3]"
							startContent={
								<IconCrown
									color="#9455D3"
									fill="#9455D3"
									size={16}
								/>
							}
						>
							Pro
						</Chip>
					)}
					{selectedCategory === Modes.SoftwareEngineering && (
						<>
							{swePerf?.sweBenchVerified && (
								<Tooltip
									content={`Accuracy on SWE-bench Verified: ${swePerf.sweBenchVerified}`}
									placement="bottom"
									size="sm"
									radius="sm"
									className="bg-gray-50 text-[#090909]"
									delay={500}
									closeDelay={0}
								>
									<Chip
										size="sm"
										variant="flat"
										radius="sm"
										startContent={
											<Image
												src="/swebench.ico"
												width={12}
												height={12}
												alt="swe-bench logo"
											/>
										}
										className="items-center gap-0.5 bg-[#151B2A] pl-2 text-[#5C7EC7]"
									>
										{swePerf.sweBenchVerified}
									</Chip>
								</Tooltip>
							)}
							{swePerf?.codeforces && (
								<Tooltip
									content={`Codeforces Rating: ${swePerf.codeforces}`}
									placement="bottom"
									size="sm"
									radius="sm"
									className="bg-gray-50 text-[#090909]"
									delay={500}
									closeDelay={0}
								>
									<Chip
										size="sm"
										variant="flat"
										radius="sm"
										startContent={
											<Image
												src="/codeforces.png"
												width={12}
												height={12}
												alt="codeforces logo"
											/>
										}
										className="items-center gap-0.5 bg-[#331F1F] pl-2 text-[#FF7B7B]"
									>
										{swePerf.codeforces}
									</Chip>
								</Tooltip>
							)}
						</>
					)}
				</div>
			</CardBody>
			<CardFooter>
				{tags[selectedCategory]?.map((tag) => (
					<Chip
						key={tag}
						size="sm"
						variant="flat"
						className="bg-[#141414] text-[#A1A1AA]"
					>
						{tag}
					</Chip>
				))}
			</CardFooter>
		</Card>
	);
};

const ModelCardDetailsModal = ({
	isOpen,
	onOpenChange,
	selectedModelDetails,
	selectedCategory,
	onModelSelect,
}: {
	isOpen: boolean;
	onOpenChange: () => void;
	selectedModelDetails: ConversationalModel | null;
	selectedCategory: Modes;
	onModelSelect: (model: ModelType) => void;
}) => {
	return (
		<Modal
			hideCloseButton
			isOpen={isOpen}
			onOpenChange={onOpenChange}
			size="2xl"
			classNames={{
				backdrop: 'backdrop-blur-md',
				base: 'bg-[#050505]',
			}}
		>
			<ModalContent>
				{(onClose) => (
					<>
						<ModalHeader className="flex flex-col gap-2">
							{selectedModelDetails && (
								<div className="flex items-center justify-between">
									<div className="flex items-center gap-3">
										{getProviderIcon(
											modelDisplayNameToProviderMap[selectedModelDetails.key],
											'chat',
											32
										)}
										<span className="text-lg font-medium">
											{selectedModelDetails.key}
										</span>
									</div>
									<div className="flex items-center gap-2">
										{selectedModelDetails.tier === ModelTier.Premium && (
											<Chip
												size="sm"
												variant="light"
												radius="sm"
												className="bg-[#141414] text-[#9455D3]"
												startContent={
													<IconCrown
														color="#9455D3"
														fill="#9455D3"
														size={16}
													/>
												}
											>
												Pro
											</Chip>
										)}
										{selectedCategory === Modes.SoftwareEngineering &&
											selectedModelDetails.swePerf && (
												<>
													{selectedModelDetails.swePerf
														.sweBenchVerified && (
														<Tooltip
															content={`Accuracy on SWE-bench Verified: ${selectedModelDetails.swePerf.sweBenchVerified}`}
															placement="bottom"
															size="sm"
															radius="sm"
															className="bg-gray-50 text-[#090909]"
														>
															<Chip
																size="sm"
																variant="flat"
																radius="sm"
																startContent={
																	<Image
																		src="/swebench.ico"
																		width={12}
																		height={12}
																		alt="swe-bench logo"
																	/>
																}
																className="items-center gap-0.5 bg-[#151B2A] pl-2 text-[#5C7EC7]"
															>
																{
																	selectedModelDetails.swePerf
																		.sweBenchVerified
																}
															</Chip>
														</Tooltip>
													)}
													{selectedModelDetails.swePerf.codeforces && (
														<Tooltip
															content={`Codeforces Rating: ${selectedModelDetails.swePerf.codeforces}`}
															placement="bottom"
															size="sm"
															radius="sm"
															className="bg-gray-50 text-[#090909]"
														>
															<Chip
																size="sm"
																variant="flat"
																radius="sm"
																startContent={
																	<Image
																		src="/codeforces.png"
																		width={12}
																		height={12}
																		alt="codeforces logo"
																	/>
																}
																className="items-center gap-0.5 bg-[#331F1F] pl-2 text-[#FF7B7B]"
															>
																{
																	selectedModelDetails.swePerf
																		.codeforces
																}
															</Chip>
														</Tooltip>
													)}
												</>
											)}
									</div>
								</div>
							)}
						</ModalHeader>
						<Divider />
						<ModalBody className="py-4">
							{selectedModelDetails && (
								<div className="flex flex-col gap-4">
									{/* Key Features */}
									<div className="flex flex-wrap gap-1.5">
										{selectedModelDetails.tags[selectedCategory]?.map((tag) => (
											<Chip
												key={tag}
												size="sm"
												variant="flat"
												className="bg-[#141414] text-[#A1A1AA]"
											>
												{tag}
											</Chip>
										))}
									</div>

									{/* Facts */}
									{selectedModelDetails.facts[selectedCategory] && (
										<>
											{selectedModelDetails.facts[selectedCategory]?.map(
												(fact, index) => (
													<div
														key={index}
														className="flex items-start gap-2"
													>
														<div className="mt-1 rounded-full bg-zeco-purple/10 p-1">
															<div className="h-1.5 w-1.5 rounded-full bg-zeco-purple" />
														</div>
														<p className="text-sm text-gray-300">
															{fact}
														</p>
													</div>
												)
											)}
										</>
									)}
								</div>
							)}
						</ModalBody>
						<ModalFooter>
							<Button
								size="sm"
								variant="flat"
								color="secondary"
								onPress={() => {
									if (selectedModelDetails) {
										onClose();
										onModelSelect(selectedModelDetails.key);
									}
								}}
							>
								Select Model
							</Button>
						</ModalFooter>
					</>
				)}
			</ModalContent>
		</Modal>
	);
};

const ConversationalModelSelectionDialog = ({
	isOpen,
	onOpenChange,
	models,
	selectedModel,
	onModelSelect,
}: Omit<ModelSelectionDialogProps, 'type'>) => {
	const {
		isOpen: isModelCardOpen,
		onOpen: onModelCardOpen,
		onOpenChange: onModelCardOpenChange,
	} = useDisclosure();

	const {
		isOpen: isUpgradeModalOpen,
		onOpen: onUpgradeModalOpen,
		onOpenChange: onUpgradeModalOpenChange,
	} = useDisclosure();

	const [selectedModelDetails, setSelectedModelDetails] = useState<ConversationalModel | null>(
		null
	);

	const [selectedProModel, setSelectedProModel] = useState<string | null>(null);
	const user = useUserContext();
	const userIsPro = user?.user_metadata?.is_pro_user || false;

	const [view, setView] = useState<'row' | 'grid'>('row');
	const [modelSearchQuery, setModelSearchQuery] = useState('');
	const [selectedCategory, setSelectedCategory] = useState<Modes>(Modes.GeneralPurpose);

	const filteredModels = (models as ConversationalModel[]).filter(
		({ key, categories, disabled }) =>
			!disabled &&
			categories.includes(selectedCategory) &&
			key.toLowerCase().includes(modelSearchQuery.toLowerCase())
	);

	// Helper function to handle model selection with Pro check
	const handleModelSelect = (model: ConversationalModel, onClose: () => void) => {
		if (model.tier === ModelTier.Premium && !userIsPro) {
			// Show upgrade modal for Pro models when user is not Pro
			setSelectedProModel(model.key);
			onUpgradeModalOpen();
		} else {
			// Allow selection for standard models or when user is Pro
			onModelSelect(model.key);
			onClose();
		}
	};

	return (
		<>
			<Modal
				hideCloseButton
				isOpen={isOpen}
				onOpenChange={onOpenChange}
				size="4xl"
				scrollBehavior="inside"
				classNames={{
					backdrop: 'backdrop-blur-md',
					base: 'bg-[#050505]',
					header: 'flex flex-col gap-2',
					body: cn(
						'h-[400px] flex-none overflow-y-auto',
						`${filteredModels.length > 0 && (view === 'grid' ? 'grid grid-cols-3' : 'flex flex-col gap-4')}`
					),
				}}
			>
				<ModalContent>
					{(onClose) => (
						<>
							<ModalHeader>
								<div className="flex flex-row justify-between gap-6">
									<Input
										isClearable
										placeholder="Search models"
										inputMode="search"
										value={modelSearchQuery}
										onValueChange={(value) => {
											setModelSearchQuery(value);
										}}
										startContent={<IconSearch size={16} />}
										classNames={{
											base: 'bg-transparent hover:bg-transparent data-[focus=true]:bg-transparent',
											inputWrapper:
												'p-0 bg-transparent hover:bg-transparent data-[focus=true]:bg-transparent group-data-[focus=true]:bg-transparent',
										}}
									/>
									<ButtonGroup
										isIconOnly
										size="sm"
										variant="light"
									>
										<Button
											isIconOnly
											disableRipple
											className={cn(
												'hover:bg-transparent',
												`${view === 'grid' && 'bg-zeco-purple/20 hover:bg-zeco-purple/20'}`
											)}
											onPress={() => {
												setView('grid');
											}}
										>
											<IconLayoutGrid size={20} />
										</Button>
										<Button
											isIconOnly
											disableRipple
											className={cn(
												'hover:bg-transparent',
												`${view === 'row' && 'bg-zeco-purple/20 hover:bg-zeco-purple/20'}`
											)}
											onPress={() => {
												setView('row');
											}}
										>
											<IconStack2 size={20} />
										</Button>
									</ButtonGroup>
								</div>
								<Divider />
								{/* TODO: Add 'All Models' TAB */}
								<Tabs
									variant="light"
									radius="sm"
									size="sm"
									aria-label="Conversational Model Categories"
									selectedKey={selectedCategory}
									onSelectionChange={(key) => setSelectedCategory(key as Modes)}
									classNames={{
										cursor: 'hidden',
										tab: cn(
											'h-10 w-full',
											'bg-[#141414] data-[selected=true]:bg-zeco-purple/10'
										),
										tabContent: 'font-medium text-sm',
										tabList: 'px-0',
									}}
								>
									{Object.values(Modes).map((mode) => (
										<Tab
											key={mode}
											title={
												<div className="flex flex-row items-center gap-1">
													{getCategoryIcon(
														mode,
														mode === selectedCategory
													)}
													{mode}
												</div>
											}
										/>
									))}
								</Tabs>
							</ModalHeader>

							<ModalBody>
								<AnimatePresence mode="popLayout">
									{filteredModels.map((model) => (
										<motion.div
											key={model.key}
											initial={{ opacity: 0, y: 20 }}
											animate={{ opacity: 1, y: 0 }}
											whileHover={{ scale: 1.02 }}
											transition={{ duration: 0.2 }}
											layout="position"
										>
											{view === 'row' ? (
												<ModelCardRow
													model={model}
													selected={model.key === selectedModel}
													selectedCategory={selectedCategory}
													onSelect={() => {
														handleModelSelect(model, onClose);
													}}
													onShowDetails={() => {
														setSelectedModelDetails(model);
														onModelCardOpen();
													}}
												/>
											) : (
												<ModelCardGrid
													model={model}
													selected={model.key === selectedModel}
													selectedCategory={selectedCategory}
													onSelect={() => {
														handleModelSelect(model, onClose);
													}}
													onShowDetails={() => {
														setSelectedModelDetails(model);
														onModelCardOpen();
													}}
												/>
											)}
										</motion.div>
									))}
									{filteredModels.length === 0 && (
										<motion.span
											initial={{ opacity: 0 }}
											animate={{ opacity: 1 }}
											exit={{ opacity: 0 }}
											className="flex justify-center text-sm"
										>
											{`No models found matching "${modelSearchQuery}"`}
										</motion.span>
									)}
								</AnimatePresence>
							</ModalBody>
						</>
					)}
				</ModalContent>
			</Modal>
			<ModelCardDetailsModal
				isOpen={isModelCardOpen}
				onOpenChange={onModelCardOpenChange}
				selectedModelDetails={selectedModelDetails}
				selectedCategory={selectedCategory}
				onModelSelect={(key) => {
					// Find the model object for Pro checking
					const model = (models as ConversationalModel[]).find((m) => m.key === key);
					if (model) {
						handleModelSelect(model, () => {
							onOpenChange(); // Close main dialog
							onModelCardOpenChange(); // Close model details modal
						});
					} else {
						onModelSelect(key);
						onOpenChange();
					}
				}}
			/>

			{/* Upgrade to Pro Modal */}
			<UpgradeToProModal
				isOpen={isUpgradeModalOpen}
				onOpenChange={onUpgradeModalOpenChange}
				modelName={selectedProModel || undefined}
				modelType="chat"
			/>
		</>
	);
};

export default ConversationalModelSelectionDialog;
