import { ModelType } from '@/app/(post-auth)/providers';
import { ModelTier } from '@/models/model-tiers';
import { GeneratedImageDetails } from '@/models/image/image-generation-examples';
import { GeneratedVideoDetails } from '@/models/video/video-generation-examples';
import { ChatType } from '@/types/chat';
import ConversationalModelSelectionDialog from './ConversationalModelSelectionDialog';
import ImageModelSelectionDialog from './ImageModelSelectionDialog';
import VideoModelSelectionDialog from './VideoModelSelectionDialog';

export interface ModelInfo {
	key: string;
	description?: string;
	provider: string;
	tier?: ModelTier;
	disabled?: boolean;
	imageExamples?: GeneratedImageDetails[];
	videoExamples?: GeneratedVideoDetails[];
}

export interface ModelSelectionDialogProps {
	isOpen: boolean;
	onOpenChange: () => void;
	type: ChatType;
	models: ModelInfo[];
	selectedModel: string;
	onModelSelect: (model: ModelType, example?: any) => void;
}

const ModelSelectionDialog = (props: ModelSelectionDialogProps) => {
	const { type, ...rest } = props;

	if (type === 'chat') {
		return <ConversationalModelSelectionDialog {...rest} />;
	} else if (type === 'image') {
		return <ImageModelSelectionDialog {...rest} />;
	} else if (type === 'video') {
		return <VideoModelSelectionDialog {...rest} />;
	}
};

export default ModelSelectionDialog;
