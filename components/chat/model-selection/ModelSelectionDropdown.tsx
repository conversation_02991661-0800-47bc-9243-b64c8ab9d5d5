import { ImageGenerationModel } from '@/models/image/image-generation-models';
import { ModelTier } from '@/models/model-tiers';
import { VideoGenerationModel } from '@/models/video/video-generation-models';
import { ConversationalModel } from '@/models/conversational/conversational-models';
import { getProviderIcon } from '@/utils/model-selection-utils';
import { Button } from '@heroui/button';
import { Chip } from '@heroui/chip';
import { Dropdown, DropdownItem, DropdownMenu, DropdownTrigger } from '@heroui/dropdown';
import { Modal, ModalBody, ModalContent, ModalHeader, useDisclosure } from '@heroui/modal';
import { Tooltip } from '@heroui/tooltip';
import { IconChevronDown, IconCrown } from '@tabler/icons-react';
import { useEffect, useState } from 'react';

type Model = ConversationalModel | ImageGenerationModel | VideoGenerationModel;

interface ModelSelectionDropdownProps {
	type: 'chat' | 'image' | 'video';
	models: Model[];
	selectedModel: string;
	selectedModelProvider: string;
	onModelSelect: (model: string) => void;
	isRegenerate?: boolean;
}

const ModelSelectionDropdown = ({
	type,
	models,
	selectedModel,
	selectedModelProvider,
	onModelSelect,
	isRegenerate = false,
}: ModelSelectionDropdownProps) => {
	const { isOpen, onOpen, onOpenChange, onClose } = useDisclosure();
	const [isMobile, setIsMobile] = useState(false);

	const enabledModels = models.filter((model) => !model.disabled);

	useEffect(() => {
		const checkMobile = () => setIsMobile(window.innerWidth < 640);
		checkMobile();
		window.addEventListener('resize', checkMobile);
		return () => window.removeEventListener('resize', checkMobile);
	}, []);

	const renderTriggerButton = () => (
		<Button
			disableRipple
			size={isRegenerate ? 'sm' : 'md'}
			variant="light"
			radius="md"
			onPress={onOpen} // Always use onOpen, logic is handled internally
			startContent={getProviderIcon(selectedModelProvider, type, isRegenerate ? 16 : 20)}
			endContent={<IconChevronDown size={isRegenerate ? 12 : 16} />}
			className={`${isRegenerate ? '' : 'hover:bg-transparent'}`}
		>
			<Tooltip
				isDisabled={!isRegenerate}
				content="Regenerate with different model"
				placement="top"
				size="sm"
				radius="sm"
				className="bg-gray-50 text-[#090909]"
				delay={500}
				closeDelay={0}
			>
				<span className="max-w-[100px] truncate sm:max-w-[150px]">{selectedModel}</span>
			</Tooltip>
		</Button>
	);

	const renderModelItem = (model: Model, isModal: boolean = false) => {
		let key: string, description: string, provider: string, tier: ModelTier;
		if (type === 'chat') {
			const chatModel = model as ConversationalModel;
			key = chatModel.key;
			const primaryCategory = chatModel.categories[0];
			description = chatModel.facts[primaryCategory]?.[0] || chatModel.categories.join(', ');
			provider = chatModel.provider;
			tier = chatModel.tier;
		} else {
			const mediaModel = model as ImageGenerationModel | VideoGenerationModel;
			key = mediaModel.key;
			description = mediaModel.description;
			provider = mediaModel.provider;
			tier = mediaModel.tier;
		}
		const isSelected = key === selectedModel;

		const itemContent = (
			<div className="flex w-full items-center justify-between gap-3 px-1">
				{getProviderIcon(provider, type, 20)}
				<div className="flex flex-1 flex-col items-start">
					<span className={`text-sm ${isSelected && isModal ? 'font-bold' : ''}`}>
						{key}
					</span>
					<p className="line-clamp-1 text-xs text-zinc-500">{description}</p>
				</div>
				{tier === ModelTier.Premium && (
					<Chip
						size="sm"
						variant="light"
						radius="sm"
						className="bg-[#141414] text-[#9455D3]"
						startContent={
							<IconCrown
								color="#9455D3"
								fill="#9455D3"
								size={16}
							/>
						}
					>
						Pro
					</Chip>
				)}
			</div>
		);

		if (isModal) {
			return (
				<div
					key={key}
					className={`cursor-pointer rounded-lg p-3 hover:bg-white/5 ${isSelected ? 'bg-zeco-purple/10' : ''}`}
					onClick={() => {
						onModelSelect(key);
						onClose();
					}}
				>
					{itemContent}
				</div>
			);
		}

		return (
			<DropdownItem
				key={key}
				textValue={key}
				className={isSelected ? 'bg-zeco-purple/10' : ''}
			>
				{itemContent}
			</DropdownItem>
		);
	};

	if (isMobile) {
		return (
			<>
				{renderTriggerButton()}
				<Modal
					isOpen={isOpen}
					onOpenChange={onOpenChange}
					placement="bottom"
					className="m-0! w-screen! max-w-full! rounded-b-none!"
				>
					<ModalContent className="z-6000 flex h-[70vh] flex-col bg-[#101010] text-white">
						<ModalHeader>Select a Model</ModalHeader>
						<ModalBody className="grow overflow-y-auto px-4">
							<div className="flex flex-col gap-1">
								{enabledModels.map((m) => renderModelItem(m, true))}
							</div>
						</ModalBody>
					</ModalContent>
				</Modal>
			</>
		);
	}

	return (
		<Dropdown
			backdrop="blur"
			placement="bottom"
			classNames={{
				base: 'w-[90vw] sm:w-[400px]',
				content: 'bg-[#101010] overflow-y-auto',
			}}
		>
			<DropdownTrigger>{renderTriggerButton()}</DropdownTrigger>
			<DropdownMenu
				variant="flat"
				selectionMode="single"
				aria-label="Model Selection Dropdown"
				items={enabledModels}
				onAction={(key) => onModelSelect(key as string)}
				classNames={{ list: 'gap-1.5 p-1 max-h-[60vh] overflow-y-auto' }}
				itemClasses={{ base: 'py-1 data-[selectable=true]:focus:bg-zeco-purple/10' }}
			>
				{(model) => renderModelItem(model)}
			</DropdownMenu>
		</Dropdown>
	);
};

export default ModelSelectionDropdown;
