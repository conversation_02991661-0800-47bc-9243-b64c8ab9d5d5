import {
	ImageGenerationModel,
	ImageModelDisplayName,
	ImageModelProvider,
} from '@/models/image/image-generation-models';
import { ModelTier } from '@/models/model-tiers';
import {
	VideoGenerationModel,
	VideoModelDisplayName,
	VideoModelProvider,
} from '@/models/video/video-generation-models';
import { getProviderIcon } from '@/utils/model-selection-utils';
import { Button } from '@heroui/button';
import { Chip } from '@heroui/chip';
import { Dropdown, DropdownItem, DropdownMenu, DropdownTrigger } from '@heroui/dropdown';
import { Tooltip } from '@heroui/tooltip';
import { useDisclosure } from '@heroui/modal';
import { IconChevronDown, IconCrown } from '@tabler/icons-react';
import { useState } from 'react';
import { useUserContext } from '@/app/(post-auth)/providers';
import UpgradeToProModal from './UpgradeToProModal';

type MediaModelDisplayName = ImageModelDisplayName | VideoModelDisplayName;
type MediaModelProvider = ImageModelProvider | VideoModelProvider;
type MediaModel = ImageGenerationModel | VideoGenerationModel;

interface MediaModelSelectionDropdownProps {
	type: 'image' | 'video';
	models: MediaModel[];
	selectedModel: MediaModelDisplayName;
	selectedModelProvider: MediaModelProvider;
	onModelSelect: (model: MediaModelDisplayName) => void;
	isRegenerate?: boolean;
	isDropdownDisabled?: boolean;
}

const MediaModelSelectionDropdown = ({
	type,
	models,
	selectedModel,
	selectedModelProvider,
	onModelSelect,
	isRegenerate = false,
	isDropdownDisabled = false,
}: MediaModelSelectionDropdownProps) => {
	const {
		isOpen: isUpgradeModalOpen,
		onOpen: onUpgradeModalOpen,
		onOpenChange: onUpgradeModalOpenChange,
	} = useDisclosure();
	const [selectedProModel, setSelectedProModel] = useState<string | null>(null);
	const user = useUserContext();
	const userIsPro = user?.user_metadata?.is_pro_user || false;
	return (
		<>
			<Dropdown
				isDisabled={isDropdownDisabled}
				backdrop="blur"
				placement="bottom-start"
				className="pointer-none"
				classNames={{
					base: 'w-[400px]',
					content: 'bg-[#101010]',
				}}
			>
				<DropdownTrigger>
					<Button
						disableRipple
						size={isRegenerate ? 'sm' : 'md'}
						variant="light"
						radius="md"
						startContent={getProviderIcon(
							selectedModelProvider,
							type,
							isRegenerate ? 16 : 20
						)}
						endContent={<IconChevronDown size={isRegenerate ? 12 : 16} />}
						className={`${isRegenerate ? '' : 'hover:bg-transparent'}`}
					>
						<Tooltip
							isDisabled={!isRegenerate}
							content="Regenerate with different model"
							placement="bottom"
							size="sm"
							radius="sm"
							className="bg-gray-50 text-[#090909]"
							delay={500}
							closeDelay={0}
						>
							{selectedModel}
						</Tooltip>
					</Button>
				</DropdownTrigger>
				<DropdownMenu
					variant="flat"
					selectionMode="single"
					aria-label="Media Model Selection Dropdown"
					items={models}
					onAction={(key) => {
						const model = models.find((m) => m.key === key);
						if (model && model.tier === ModelTier.Premium && !userIsPro) {
							// Show upgrade modal for Pro models when user is not Pro
							setSelectedProModel(key as string);
							onUpgradeModalOpen();
						} else {
							// Allow selection for standard models or when user is Pro
							onModelSelect(key as MediaModelDisplayName);
						}
					}}
					classNames={{ list: 'gap-1.5' }}
					itemClasses={{
						base: 'data-[selectable=true]:focus:bg-zeco-purple/10',
						description: 'line-clamp-1',
					}}
				>
					{({ key, description, provider, tier, disabled }) =>
						!disabled ? (
							<DropdownItem
								key={key}
								description={
									<Tooltip
										content={description}
										placement="right-start"
										size="sm"
										radius="sm"
										className="w-[300px] bg-gray-50 text-wrap text-[#090909]"
										delay={1000}
									>
										{description}
									</Tooltip>
								}
								startContent={getProviderIcon(provider, type, 20)}
								endContent={
									tier === ModelTier.Premium ? (
										<Chip
											size="sm"
											variant="light"
											radius="sm"
											className="bg-[#141414] text-[#9455D3]"
											startContent={
												<IconCrown
													color="#9455D3"
													fill="#9455D3"
													size={16}
												/>
											}
										>
											Pro
										</Chip>
									) : null
								}
							>
								{key}
							</DropdownItem>
						) : null
					}
				</DropdownMenu>
			</Dropdown>

			{/* Upgrade to Pro Modal */}
			<UpgradeToProModal
				isOpen={isUpgradeModalOpen}
				onOpenChange={onUpgradeModalOpenChange}
				modelName={selectedProModel || undefined}
				modelType={type}
			/>
		</>
	);
};

export default MediaModelSelectionDropdown;
