import { motion, useScroll, useTransform } from 'motion/react';
import NextImage from 'next/image';
import { useRef, useState } from 'react';
import { Button } from '@heroui/button';
import { Chip } from '@heroui/chip';
import { Image as HeroUIImage } from '@heroui/image';
import {
	Modal,
	ModalContent,
	ModalBody,
	ModalHeader,
	useDisclosure,
	ModalFooter,
} from '@heroui/modal';
import { Tabs, Tab } from '@heroui/tabs';
import { GeneratedImageDetails } from '@/models/image/image-generation-examples';
import { ModelSelectionDialogProps } from './ModelSelectionDialog';
import { GeneratedImageFilter } from '@/models/image/image-generation-examples';
import { LazyImageCard } from './LazyImageCard';
import { getProviderIcon } from '../../../utils/model-selection-utils';
import {
	imageModelDisplayNameToProviderMap,
	ImageModelDisplayName,
} from '@/models/image/image-generation-models';
import { cn } from '@/utils/cn';

const FILTER_EMOJIS: Record<GeneratedImageFilter, string> = {
	Trending: '🔥',
	Photorealism: '📸',
	Character: '🧑',
	Product: '📦',
	Graphic: '🎨',
	Concept: '💡',
	Logo: '🏷️',
	Poster: '🖼️',
	Textual: '🔤',
	Anime: '🧚',
	Cartoon: '🐭',
	CGI: '🖥️',
	Painting: '🖌️',
	Sketch: '✏️',
	Architecture: '🏛️',
	Portrait: '👤',
	Landscape: '🏞️',
	Surreal: '🌀',
	Cinematic: '🎬',
	Meme: '😂',
};

const ParallaxScroll = ({
	images,
	onImageCardClick,
	className,
}: {
	images: (GeneratedImageDetails & { modelName: ImageModelDisplayName })[];
	onImageCardClick: (img: GeneratedImageDetails & { modelName: ImageModelDisplayName }) => void;
	className?: string;
}) => {
	const gridRef = useRef<any>(null);
	const { scrollYProgress } = useScroll({
		container: gridRef, // remove this if your container is not fixed height
		offset: ['start start', 'end start'], // remove this if your container is not fixed height
	});

	const translateFirst = useTransform(scrollYProgress, [0, 1], [0, -200]);
	const translateSecond = useTransform(scrollYProgress, [0, 1], [0, 200]);
	const translateThird = useTransform(scrollYProgress, [0, 1], [0, -200]);

	const third = Math.ceil(images.length / 3);

	const firstPart = images.slice(0, third);
	const secondPart = images.slice(third, 2 * third);
	const thirdPart = images.slice(2 * third);

	return (
		<div
			className={`h-160 w-full items-start overflow-y-auto ${className ?? ''}`}
			ref={gridRef}
		>
			<div className="mx-auto grid max-w-5xl grid-cols-1 items-start gap-10 md:grid-cols-2 lg:grid-cols-3">
				<div className="grid gap-10">
					{firstPart.map((el, idx) => (
						<motion.div
							style={{ y: translateFirst }}
							key={'grid-1' + idx}
							className="cursor-pointer"
						>
							<LazyImageCard
								{...el}
								onImageCardClick={onImageCardClick}
							/>
						</motion.div>
					))}
				</div>
				<div className="grid gap-10">
					{secondPart.map((el, idx) => (
						<motion.div
							style={{ y: translateSecond }}
							key={'grid-2' + idx}
							className="cursor-pointer"
						>
							<LazyImageCard
								{...el}
								onImageCardClick={onImageCardClick}
							/>
						</motion.div>
					))}
				</div>
				<div className="grid gap-10">
					{thirdPart.map((el, idx) => (
						<motion.div
							style={{ y: translateThird }}
							key={'grid-3' + idx}
							className="cursor-pointer"
						>
							<LazyImageCard
								{...el}
								onImageCardClick={onImageCardClick}
							/>
						</motion.div>
					))}
				</div>
			</div>
		</div>
	);
};

const ImageDetailsModal = ({
	isOpen,
	onOpenChange,
	selectedImageDetails,
	onImageSelect,
}: {
	isOpen: boolean;
	onOpenChange: () => void;
	selectedImageDetails: (GeneratedImageDetails & { modelName: ImageModelDisplayName }) | null;
	onImageSelect: (model: ImageModelDisplayName, selectedImage: GeneratedImageDetails) => void;
}) => {
	if (!selectedImageDetails) {
		return null;
	}

	const { imageUrl, prompt, filters, parameters, modelName } = selectedImageDetails;

	return (
		<Modal
			isOpen={isOpen}
			onOpenChange={onOpenChange}
			scrollBehavior="inside"
			size="2xl"
			classNames={{
				backdrop: 'backdrop-blur-md',
				base: 'bg-[#101010]',
				body: 'flex flex-row',
				header: 'flex flex-row justify-start items-center gap-1',
			}}
		>
			<ModalContent>
				{(onClose) => (
					<>
						<ModalHeader>
							{getProviderIcon(
								imageModelDisplayNameToProviderMap[modelName],
								'image',
								32
							)}
							<span className="text-lg font-semibold text-white">{modelName}</span>
						</ModalHeader>
						<ModalBody>
							<div className="relative flex-1">
								<HeroUIImage
									fill
									removeWrapper
									as={NextImage}
									src={imageUrl}
									alt={prompt.substring(0, 50)}
									className="object-contain"
									sizes="(min-width: 1024px) 33vw, (min-width: 640px) 50vw, 100vw"
									quality={90}
									placeholder="blur"
									blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/4gHYSUNDX1BST0ZJTEUAAQEAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADb/2wBDABQODxIPDRQSEBIXFRQdHx4eHRoaHSQtJSEkLzYvLy0vLzY3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzb/wBDAR0dHh4eHRoaHSQtJSEkLzYvLy0vLzY3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzb/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAb/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
								/>
							</div>
							<div className="flex flex-1 flex-col justify-start gap-6">
								<div className="space-y-1">
									<p className="text-sm font-semibold text-white">Prompt</p>
									<p className="max-h-36 overflow-y-auto text-sm break-words whitespace-pre-line text-white/90">
										{prompt}
									</p>
								</div>
								{parameters && Object.keys(parameters).length > 0 && (
									<div className="space-y-1">
										<p className="text-sm font-semibold text-white">
											Parameters
										</p>
										<div className="flex flex-col gap-1">
											{Object.entries(parameters).map(([key, value]) => (
												<div
													key={key}
													className="flex flex-row gap-2 text-xs text-white/80"
												>
													<span className="font-mono text-white/60">
														{key}:
													</span>
													<span className="font-mono">
														{String(value)}
													</span>
												</div>
											))}
										</div>
									</div>
								)}
								{filters.length > 0 && (
									<div className="flex flex-wrap gap-1">
										{filters.map((filter) => (
											<Chip
												key={filter}
												size="sm"
												variant="flat"
												className="bg-zeco-purple/20 font-mono whitespace-nowrap"
											>
												#{filter}
											</Chip>
										))}
									</div>
								)}
							</div>
						</ModalBody>
						<ModalFooter>
							<Button
								disableRipple
								size="sm"
								variant="flat"
								color="secondary"
								onPress={() => {
									onClose();
									onImageSelect(modelName, selectedImageDetails);
								}}
							>
								Select Model
							</Button>
						</ModalFooter>
					</>
				)}
			</ModalContent>
		</Modal>
	);
};

const ImageModelSelectionDialog = ({
	isOpen,
	onOpenChange,
	models,
	onModelSelect,
}: Omit<ModelSelectionDialogProps, 'type'>) => {
	const {
		isOpen: isImageDetailsCardOpen,
		onOpen: onImageDetailsCardOpen,
		onOpenChange: onImageDetailsCardOpenChange,
	} = useDisclosure();

	const [selectedFilter, setSelectedFilter] = useState<GeneratedImageFilter | 'all'>('all');
	const [selectedImageDetails, setSelectedImageDetails] = useState<
		(GeneratedImageDetails & { modelName: ImageModelDisplayName }) | null
	>(null);

	const generatedImages = models
		.map((model) =>
			model.imageExamples!.map(
				(imageExample) =>
					({ ...imageExample, modelName: model.key }) as GeneratedImageDetails & {
						modelName: ImageModelDisplayName;
					}
			)
		)
		.flat();

	const filteredImages =
		selectedFilter === 'all'
			? generatedImages
			: generatedImages.filter((img) => img.filters.includes(selectedFilter));

	const handleImageCardClick = (
		img: GeneratedImageDetails & { modelName: ImageModelDisplayName }
	) => {
		setSelectedImageDetails(img);
		onImageDetailsCardOpen();
	};

	const handleImageSelect = (
		model: ImageModelDisplayName,
		selectedImage: GeneratedImageDetails
	) => {
		onModelSelect(model, selectedImage);
		onOpenChange();
	};

	return (
		<>
			<Modal
				hideCloseButton
				isOpen={isOpen}
				onOpenChange={onOpenChange}
				scrollBehavior="inside"
				size="4xl"
				classNames={{
					backdrop: 'backdrop-blur-md',
					base: 'bg-[#090909]',
					header: 'flex flex-col',
				}}
			>
				<ModalContent>
					{(onClose) => (
						<>
							<ModalHeader>
								<Tabs
									fullWidth
									variant="light"
									size="sm"
									radius="full"
									aria-label="Image Style Filters"
									selectedKey={selectedFilter}
									onSelectionChange={(key) =>
										setSelectedFilter(
											key === 'all' ? 'all' : (key as GeneratedImageFilter)
										)
									}
									classNames={{
										cursor: 'hidden',
										tab: cn(
											'data-[hover-unselected=true]:bg-zeco-purple/20 data-[hover-unselected=true]:opacity-100',
											'bg-[#141414] data-[selected=true]:bg-zeco-purple'
										),
										tabContent: 'font-medium text-xs',
									}}
								>
									<Tab
										key="all"
										title="✨ All styles"
									/>
									{Object.values(GeneratedImageFilter).map((filter) => (
										<Tab
											key={filter}
											title={`${FILTER_EMOJIS[filter]} #${filter}`}
										/>
									))}
								</Tabs>
							</ModalHeader>
							<ModalBody>
								<ParallaxScroll
									images={filteredImages}
									onImageCardClick={handleImageCardClick}
								/>
							</ModalBody>
						</>
					)}
				</ModalContent>
			</Modal>
			<ImageDetailsModal
				isOpen={isImageDetailsCardOpen}
				onOpenChange={onImageDetailsCardOpenChange}
				selectedImageDetails={selectedImageDetails}
				onImageSelect={handleImageSelect}
			/>
		</>
	);
};

export default ImageModelSelectionDialog;
