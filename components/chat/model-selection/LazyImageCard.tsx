import { motion, AnimatePresence } from 'framer-motion';
import NextImage from 'next/image';
import { useState } from 'react';
import { Card, CardFooter } from '@heroui/card';
import { Chip } from '@heroui/chip';
import { Image as HeroUIImage } from '@heroui/image';
import { Spinner } from '@heroui/spinner';
import { GeneratedImageDetails } from '@/models/image/image-generation-examples';
import { useIntersectionObserver } from '@/lib/hooks/use-intersection-observer';
import { getProviderIcon } from '@/utils/model-selection-utils';
import {
	imageModelDisplayNameToProviderMap,
	ImageModelDisplayName,
} from '@/models/image/image-generation-models';

interface LazyImageCardProps extends GeneratedImageDetails {
	modelName: ImageModelDisplayName;
	onImageCardClick: (img: GeneratedImageDetails & { modelName: ImageModelDisplayName }) => void;
}

const ImageCardSkeleton = () => {
	return (
		<Card
			classNames={{
				base: 'h-[300px] w-full',
				footer: 'flex flex-col items-start gap-1 p-3 min-h-0',
			}}
		>
			<div className="relative h-full w-full bg-zeco-purple/10 animate-pulse rounded-lg">
				<div className="absolute inset-0 flex items-center justify-center">
					<Spinner
						size="lg"
						color="secondary"
						classNames={{
							circle1: 'border-b-zeco-purple',
							circle2: 'border-b-zeco-purple',
						}}
					/>
				</div>
			</div>
		</Card>
	);
};

export const LazyImageCard = ({
	imageUrl,
	prompt,
	filters,
	parameters,
	modelName,
	onImageCardClick,
}: LazyImageCardProps) => {
	const { ref, hasBeenVisible } = useIntersectionObserver({
		threshold: 0.1,
		rootMargin: '100px',
		freezeOnceVisible: true,
	});

	const [isHovered, setIsHovered] = useState(false);

	// Don't render the actual image until it's been visible
	if (!hasBeenVisible) {
		return (
			<div ref={ref}>
				<ImageCardSkeleton />
			</div>
		);
	}

	return (
		<div ref={ref}>
			<Card
				isHoverable
				isPressable
				isFooterBlurred
				disableRipple
				classNames={{
					base: 'h-[300px] w-full',
					footer: 'flex flex-col items-start gap-1 p-3 min-h-0',
				}}
				onPress={() => {
					onImageCardClick({ imageUrl, prompt, filters, parameters, modelName });
				}}
				onMouseEnter={() => setIsHovered(true)}
				onMouseLeave={() => setIsHovered(false)}
			>
				<HeroUIImage
					fill
					removeWrapper
					as={NextImage}
					src={imageUrl}
					alt={prompt.substring(0, 50)}
					className="z-0 h-full w-full object-cover"
					sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
					quality={90}
					placeholder="blur"
					blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/4gHYSUNDX1BST0ZJTEUAAQEAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADb/2wBDABQODxIPDRQSEBIXFRQdHx4eHRoaHSQtJSEkLzYvLy0vLzY3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzb/2wBDAR0dHh4eHRoaHSQtJSEkLzYvLy0vLzY3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzb/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAb/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
				/>
				{/* Model icon on hover, top-left */}
				{isHovered && (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						exit={{ opacity: 0 }}
						transition={{ duration: 0.1 }}
						className="absolute top-2 left-2 z-20"
					>
						{getProviderIcon(
							imageModelDisplayNameToProviderMap[modelName],
							'image',
							28
						)}
					</motion.div>
				)}
				<AnimatePresence>
					{isHovered && (
						<motion.div
							initial={{ opacity: 0, y: 40 }}
							animate={{ opacity: 1, y: 0 }}
							exit={{ opacity: 0, y: 40 }}
							transition={{ duration: 0.1 }}
							className="absolute bottom-0 left-0 w-full z-10"
						>
							<CardFooter>
								<p className="text-xs text-white/80 truncate w-full">{prompt}</p>
								{filters.length > 0 && (
									<div className="flex flex-row gap-1 overflow-x-auto w-full">
										{filters.map((filter) => (
											<Chip
												key={filter}
												size="sm"
												variant="flat"
												className="font-mono bg-zeco-purple/50 whitespace-nowrap"
											>
												#{filter}
											</Chip>
										))}
									</div>
								)}
							</CardFooter>
						</motion.div>
					)}
				</AnimatePresence>
			</Card>
		</div>
	);
};
