import { motion, useScroll, useTransform } from 'framer-motion';
import { useRef, useState } from 'react';
import { But<PERSON> } from '@heroui/button';
import { Chip } from '@heroui/chip';
import {
	Modal,
	ModalContent,
	ModalBody,
	ModalHeader,
	useDisclosure,
	ModalFooter,
} from '@heroui/modal';
import { Tabs, Tab } from '@heroui/tabs';
import { GeneratedVideoDetails } from '@/models/video/video-generation-examples';
import { ModelSelectionDialogProps } from './ModelSelectionDialog';
import { GeneratedVideoFilter } from '@/models/video/video-generation-examples';
import { LazyVideoCard } from './LazyVideoCard';
import { getProviderIcon } from '../../../utils/model-selection-utils';
import {
	videoModelDisplayNameToProviderMap,
	VideoModelDisplayName,
} from '@/models/video/video-generation-models';
import { cn } from '@/utils/cn';

const FILTER_EMOJIS: Record<GeneratedVideoFilter, string> = {
	ImageToVideo: '🎬',
	Photorealism: '📸',
	Character: '🧑',
	Animation: '🎭',
	Landscape: '🏞️',
	Cinematic: '🎬',
	Portrait: '👤',
	Surreal: '🌀',
	CGI: '🖥️',
	Product: '📦',
	Anime: '🧚',
};

const ParallaxScroll = ({
	videos,
	onVideoCardClick,
	className,
}: {
	videos: (GeneratedVideoDetails & { modelName: VideoModelDisplayName })[];
	onVideoCardClick: (video: GeneratedVideoDetails & { modelName: VideoModelDisplayName }) => void;
	className?: string;
}) => {
	const gridRef = useRef<any>(null);
	const { scrollYProgress } = useScroll({
		container: gridRef,
		offset: ['start start', 'end start'],
	});

	const translateFirst = useTransform(scrollYProgress, [0, 1], [0, -200]);
	const translateSecond = useTransform(scrollYProgress, [0, 1], [0, 200]);
	const translateThird = useTransform(scrollYProgress, [0, 1], [0, -200]);

	const third = Math.ceil(videos.length / 3);

	const firstPart = videos.slice(0, third);
	const secondPart = videos.slice(third, 2 * third);
	const thirdPart = videos.slice(2 * third);

	return (
		<div
			className={`h-160 items-start overflow-y-auto w-full ${className ?? ''}`}
			ref={gridRef}
		>
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 items-start max-w-5xl mx-auto gap-10">
				<div className="grid gap-10">
					{firstPart.map((el, idx) => (
						<motion.div
							style={{ y: translateFirst }}
							key={'grid-1' + idx}
							className="cursor-pointer"
						>
							<LazyVideoCard
								{...el}
								onVideoCardClick={onVideoCardClick}
							/>
						</motion.div>
					))}
				</div>
				<div className="grid gap-10">
					{secondPart.map((el, idx) => (
						<motion.div
							style={{ y: translateSecond }}
							key={'grid-2' + idx}
							className="cursor-pointer"
						>
							<LazyVideoCard
								{...el}
								onVideoCardClick={onVideoCardClick}
							/>
						</motion.div>
					))}
				</div>
				<div className="grid gap-10">
					{thirdPart.map((el, idx) => (
						<motion.div
							style={{ y: translateThird }}
							key={'grid-3' + idx}
							className="cursor-pointer"
						>
							<LazyVideoCard
								{...el}
								onVideoCardClick={onVideoCardClick}
							/>
						</motion.div>
					))}
				</div>
			</div>
		</div>
	);
};

const VideoPlaceholder = () => {
	return (
		<div className="absolute inset-0 flex flex-col">
			{/* Video aspect ratio placeholder */}
			<div className="relative w-full h-full">
				<div className="absolute inset-0 rounded-lg bg-zeco-purple/20 animate-pulse" />
			</div>
			{/* Prompt placeholder */}
			<div className="p-3 space-y-2">
				<div className="h-4 w-3/4 bg-zeco-purple/20 rounded animate-pulse" />
				<div className="h-4 w-1/2 bg-zeco-purple/20 rounded animate-pulse" />
			</div>
		</div>
	);
};

const VideoDetailsModal = ({
	isOpen,
	onOpenChange,
	selectedVideoDetails,
	onVideoSelect,
}: {
	isOpen: boolean;
	onOpenChange: () => void;
	selectedVideoDetails: (GeneratedVideoDetails & { modelName: VideoModelDisplayName }) | null;
	onVideoSelect: (model: VideoModelDisplayName, selectedVideo: GeneratedVideoDetails) => void;
}) => {
	if (!selectedVideoDetails) {
		return null;
	}

	const { videoUrl, prompt, filter, parameters, modelName } = selectedVideoDetails;

	return (
		<Modal
			isOpen={isOpen}
			onOpenChange={onOpenChange}
			scrollBehavior="inside"
			size="2xl"
			classNames={{
				backdrop: 'backdrop-blur-md',
				base: 'bg-[#101010]',
				body: 'flex flex-row',
				header: 'flex flex-row justify-start items-center gap-1',
			}}
		>
			<ModalContent>
				{(onClose) => (
					<>
						<ModalHeader>
							{getProviderIcon(
								videoModelDisplayNameToProviderMap[modelName],
								'video',
								32
							)}
							<span className="text-lg font-semibold text-white">{modelName}</span>
						</ModalHeader>
						<ModalBody>
							<div className="relative flex-1">
								<video
									src={videoUrl}
									className="w-full h-full object-contain"
									controls
									playsInline
								/>
							</div>
							<div className="flex flex-1 flex-col gap-6 justify-start">
								<div className="space-y-1">
									<p className="text-sm text-white font-semibold">Prompt</p>
									<p className="max-h-36 text-sm text-white/90 break-words whitespace-pre-line overflow-y-auto">
										{prompt}
									</p>
								</div>
								{parameters && Object.keys(parameters).length > 0 && (
									<div className="space-y-1">
										<p className="text-sm text-white font-semibold">
											Parameters
										</p>
										<div className="flex flex-col gap-1">
											{Object.entries(parameters).map(([key, value]) => (
												<div
													key={key}
													className="flex flex-row gap-2 text-xs text-white/80"
												>
													<span className="font-mono text-white/60">
														{key}:
													</span>
													<span className="font-mono">
														{String(value)}
													</span>
												</div>
											))}
										</div>
									</div>
								)}
								{filter && filter.length > 0 && (
									<div className="flex flex-wrap gap-1">
										{filter.map((f) => (
											<Chip
												key={f}
												size="sm"
												variant="flat"
												className="font-mono bg-zeco-purple/20 whitespace-nowrap"
											>
												#{f}
											</Chip>
										))}
									</div>
								)}
							</div>
						</ModalBody>
						<ModalFooter>
							<Button
								disableRipple
								size="sm"
								variant="flat"
								color="secondary"
								onPress={() => {
									onClose();
									onVideoSelect(modelName, selectedVideoDetails);
								}}
							>
								Select Model
							</Button>
						</ModalFooter>
					</>
				)}
			</ModalContent>
		</Modal>
	);
};

const VideoModelSelectionDialog = ({
	isOpen,
	onOpenChange,
	models,
	onModelSelect,
}: Omit<ModelSelectionDialogProps, 'type'>) => {
	const {
		isOpen: isVideoDetailsCardOpen,
		onOpen: onVideoDetailsCardOpen,
		onOpenChange: onVideoDetailsCardOpenChange,
	} = useDisclosure();

	const [selectedFilter, setSelectedFilter] = useState<GeneratedVideoFilter | 'all'>('all');
	const [selectedVideoDetails, setSelectedVideoDetails] = useState<
		(GeneratedVideoDetails & { modelName: VideoModelDisplayName }) | null
	>(null);

	const generatedVideos = models
		.map((model) =>
			model.videoExamples!.map(
				(videoExample) =>
					({ ...videoExample, modelName: model.key }) as GeneratedVideoDetails & {
						modelName: VideoModelDisplayName;
					}
			)
		)
		.flat();

	const filteredVideos =
		selectedFilter === 'all'
			? generatedVideos
			: generatedVideos.filter((video) => video.filter?.includes(selectedFilter));

	const handleVideoCardClick = (
		video: GeneratedVideoDetails & { modelName: VideoModelDisplayName }
	) => {
		setSelectedVideoDetails(video);
		onVideoDetailsCardOpen();
	};

	const handleVideoSelect = (
		model: VideoModelDisplayName,
		selectedVideo: GeneratedVideoDetails
	) => {
		onModelSelect(model, selectedVideo);
		onOpenChange();
	};

	return (
		<>
			<Modal
				hideCloseButton
				isOpen={isOpen}
				onOpenChange={onOpenChange}
				scrollBehavior="inside"
				size="4xl"
				classNames={{
					backdrop: 'backdrop-blur-md',
					base: 'bg-[#090909]',
					header: 'flex flex-col',
				}}
			>
				<ModalContent>
					{(onClose) => (
						<>
							<ModalHeader>
								<Tabs
									fullWidth
									variant="light"
									size="sm"
									radius="full"
									aria-label="Video Style Filters"
									selectedKey={selectedFilter}
									onSelectionChange={(key) =>
										setSelectedFilter(
											key === 'all' ? 'all' : (key as GeneratedVideoFilter)
										)
									}
									classNames={{
										cursor: 'hidden',
										tab: cn(
											'data-[hover-unselected=true]:bg-zeco-purple/20 data-[hover-unselected=true]:opacity-100',
											'bg-[#141414] data-[selected=true]:bg-zeco-purple'
										),
										tabContent: 'font-medium text-xs',
									}}
								>
									<Tab
										key="all"
										title="✨ All styles"
									/>
									{Object.values(GeneratedVideoFilter).map((filter) => (
										<Tab
											key={filter}
											title={`${FILTER_EMOJIS[filter]} #${filter}`}
										/>
									))}
								</Tabs>
							</ModalHeader>
							<ModalBody>
								<ParallaxScroll
									videos={filteredVideos}
									onVideoCardClick={handleVideoCardClick}
								/>
							</ModalBody>
						</>
					)}
				</ModalContent>
			</Modal>
			<VideoDetailsModal
				isOpen={isVideoDetailsCardOpen}
				onOpenChange={onVideoDetailsCardOpenChange}
				selectedVideoDetails={selectedVideoDetails}
				onVideoSelect={handleVideoSelect}
			/>
		</>
	);
};

export default VideoModelSelectionDialog;
