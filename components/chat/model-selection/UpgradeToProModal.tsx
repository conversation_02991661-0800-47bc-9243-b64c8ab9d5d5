'use client';

import { initiateSubscriptionPaymentAction } from '@/app/subscriptions/actions';
import { PriceQuoteCard } from '@/components/landing-page/PricingQuoteSection';
import { plans, PricingPlanType } from '@/components/landing-page/pricing';
import useUserLocation from '@/lib/hooks/useUserLocation';
import { Button } from '@heroui/button';
import { Modal, ModalBody, ModalContent, ModalFooter, ModalHeader } from '@heroui/modal';
import { IconCrown, IconLoader2 } from '@tabler/icons-react';
import { useState } from 'react';
import { addToast } from '@heroui/toast';

interface UpgradeToProModalProps {
	isOpen: boolean;
	onOpenChange: (open: boolean) => void;
	modelName?: string;
	modelType?: 'chat' | 'image' | 'video';
}

const UpgradeToProModal = ({
	isOpen,
	onOpenChange,
	modelName,
	modelType = 'image',
}: UpgradeToProModalProps) => {
	const { currency } = useUserLocation();
	const proPlan = plans.find((plan) => plan.planType === PricingPlanType.PRO);
	const [isSubmitting, setIsSubmitting] = useState(false);

	if (!proPlan) return null;

	const getModelTypeText = () => {
		switch (modelType) {
			case 'chat':
				return 'conversational AI model';
			case 'image':
				return 'image generation model';
			case 'video':
				return 'video generation model';
			default:
				return 'AI model';
		}
	};

	const handleUpgrade = async () => {
		setIsSubmitting(true);
		try {
			const result = await initiateSubscriptionPaymentAction('zeco-pro-monthly-plan');
			if (result.success) {
				addToast({
					title: 'Redirecting to payment',
					description: 'You will be redirected to complete your payment.',
					color: 'primary',
				});

				// Close modal before redirect
				onOpenChange(false);

				// Redirect to payment
				window.location.href = result.paymentLink;
			} else {
				// Handle different error types from server
				if (result.isProcessing) {
					addToast({
						title: 'Payment Already Processing',
						description: 'Your payment is already being processed. Please wait.',
						color: 'warning',
					});
				} else {
					addToast({
						title: 'Payment Failed',
						description: result.error.message,
						color: 'danger',
					});
				}
			}
		} catch (error) {
			addToast({
				title: 'Payment Error',
				description: 'An unexpected error occurred while processing your payment.',
				color: 'danger',
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	// Button state helper functions
	const isButtonDisabled = isSubmitting;

	const getButtonText = () => {
		if (isSubmitting) return 'Processing...';
		return 'Upgrade Now';
	};

	const getButtonIcon = () => {
		if (isSubmitting) {
			return (
				<IconLoader2
					size={18}
					className="animate-spin"
				/>
			);
		}
		return <IconCrown size={18} />;
	};

	return (
		<Modal
			isOpen={isOpen}
			onOpenChange={onOpenChange}
			size="lg"
			backdrop="blur"
			scrollBehavior="inside"
			classNames={{
				base: 'bg-[#0a0a0a] border border-neutral-800',
				header: 'border-b border-neutral-800',
				body: 'py-0',
				footer: 'border-t border-neutral-800',
			}}
		>
			<ModalContent>
				{(onClose) => (
					<>
						<ModalHeader className="flex flex-col gap-1 px-6 py-4">
							<div className="flex w-full items-center justify-between">
								<div className="flex items-center gap-3">
									<div className="rounded-full bg-purple-500/10 p-2">
										<IconCrown
											color="#9455D3"
											size={24}
										/>
									</div>
									<div>
										<h2 className="text-xl font-semibold">
											Upgrade to ZECO Pro
										</h2>
										<p className="text-sm font-normal text-gray-400">
											{modelType === 'image' && modelName === 'Flux 1.1 Pro'
												? `Unlock ${modelName} and other advanced image editing feature`
												: `Unlock ${modelName ? `${modelName} and ` : ''}premium ${getModelTypeText()}s`}
										</p>
									</div>
								</div>
							</div>
						</ModalHeader>

						<ModalBody className="px-6 py-6">
							<div className="space-y-6">
								{/* Pricing card */}
								<div className="flex justify-center">
									<div className="w-full max-w-sm">
										<PriceQuoteCard
											item={proPlan}
											currency={currency}
											hoveredIndex={null}
											index={0}
										/>
									</div>
								</div>
							</div>
						</ModalBody>

						<ModalFooter className="px-6 py-4">
							<div className="flex w-full gap-3">
								<Button
									color="primary"
									onPress={handleUpgrade}
									disabled={isButtonDisabled}
									className="flex-1 bg-linear-to-r from-purple-600 to-violet-600 text-white"
									startContent={getButtonIcon()}
								>
									{getButtonText()}
								</Button>
							</div>
						</ModalFooter>
					</>
				)}
			</ModalContent>
		</Modal>
	);
};

export default UpgradeToProModal;
