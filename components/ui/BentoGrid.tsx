import { cn } from '@/utils/cn';

export const BentoGrid = ({
	className,
	children,
}: {
	className?: string;
	children?: React.ReactNode;
}) => {
	return (
		<div className={cn('grid md:auto-rows-[18rem] max-w-8xl mx-auto', className)}>
			{children}
		</div>
	);
};

export const BentoGridItem = ({
	className,
	title,
	description,
	header,
}: {
	className?: string;
	title?: string | React.ReactNode;
	description?: string | React.ReactNode;
	header?: React.ReactNode;
}) => {
	return (
		<div
			className={cn(
				'rounded-2xl group/bento hover:shadow-xl transition duration-200 shadow-none justify-between flex flex-col space-y-4',
				className
			)}
		>
			{header} {/* Always render the header */}
			{(title || description) && (
				<div className="xl:group-hover/bento:translate-y-2 transition duration-200">
					<div className="font-bold text-xl">{title}</div>
					<div className="font-normal text-sm text-neutral-300">{description}</div>
				</div>
			)}
		</div>
	);
};
