import { cn } from '@/utils/cn';

export const BentoGrid = ({
	className,
	children,
}: {
	className?: string;
	children?: React.ReactNode;
}) => {
	return (
		<div className={cn('max-w-8xl mx-auto grid md:auto-rows-[18rem]', className)}>
			{children}
		</div>
	);
};

export const BentoGridItem = ({
	className,
	title,
	description,
	header,
}: {
	className?: string;
	title?: string | React.ReactNode;
	description?: string | React.ReactNode;
	header?: React.ReactNode;
}) => {
	return (
		<div
			className={cn(
				'group/bento flex flex-col justify-between space-y-4 rounded-2xl shadow-none transition duration-200 hover:shadow-xl',
				className
			)}
		>
			{header} {/* Always render the header */}
			{(title || description) && (
				<div className="transition duration-200 xl:group-hover/bento:translate-y-2">
					<div className="text-xl font-bold">{title}</div>
					<div className="text-sm font-normal text-neutral-300">{description}</div>
				</div>
			)}
		</div>
	);
};
