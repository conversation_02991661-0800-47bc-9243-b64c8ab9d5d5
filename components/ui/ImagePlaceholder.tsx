'use client';

interface ImagePlaceholderProps {
	n?: number;
}

/**
 * Renders a ZECO-branded grid of image placeholders
 * with the usual square pulse shimmer.
 *
 */

export const ImagePlaceholder: React.FC<ImagePlaceholderProps> = ({ n = 1 }) => {
	return (
		<div className="animate-in fade-in slide-in-from-bottom-5 flex flex-col gap-2 duration-300">
			{/* header */}
			<div className="mt-4 flex items-center gap-1.5 px-3 text-xs text-white/60">
				<span className="font-medium">ZECO</span>
			</div>

			{/* grid of shimmering tiles */}
			<div className="grid grid-cols-1 gap-4 px-3 sm:grid-cols-2 lg:grid-cols-3">
				{Array.from({ length: n }).map((_, i) => (
					<div
						key={i}
						className="relative w-full"
						style={{ paddingTop: '100%' }}
						aria-hidden="true"
						role="status"
					>
						<div className="absolute inset-0 animate-pulse rounded-lg bg-zeco-purple/20" />
					</div>
				))}
			</div>
		</div>
	);
};

ImagePlaceholder.displayName = 'ImagePlaceholder';
