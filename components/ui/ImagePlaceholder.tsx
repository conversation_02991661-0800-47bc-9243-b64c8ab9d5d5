'use client';

interface ImagePlaceholderProps {
	n?: number;
}

/**
 * Renders a ZECO-branded grid of image placeholders
 * with the usual square pulse shimmer.
 *
 */

export const ImagePlaceholder: React.FC<ImagePlaceholderProps> = ({ n = 1 }) => {
	return (
		<div className="flex flex-col gap-2 animate-in fade-in slide-in-from-bottom-5 duration-300">
			{/* header */}
			<div className="flex items-center px-3 gap-1.5 text-xs text-white/60 mt-4">
				<span className="font-medium">ZECO</span>
			</div>

			{/* grid of shimmering tiles */}
			<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 px-3">
				{Array.from({ length: n }).map((_, i) => (
					<div
						key={i}
						className="relative w-full"
						style={{ paddingTop: '100%' }}
						aria-hidden="true"
						role="status"
					>
						<div className="absolute inset-0 rounded-lg bg-zeco-purple/20 animate-pulse" />
					</div>
				))}
			</div>
		</div>
	);
};

ImagePlaceholder.displayName = 'ImagePlaceholder';
