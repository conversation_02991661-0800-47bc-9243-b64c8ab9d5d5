'use client';

import { cn } from '@/utils/cn';
import { motion } from 'framer-motion';

export const TypewriterEffectSmooth = ({
	words,
	className,
	cursorClassName,
}: {
	words: {
		text: string;
		className?: string;
	}[];
	className?: string;
	cursorClassName?: string;
}) => {
	// split text inside of words into array of characters
	const wordsArray = words.map((word) => {
		return {
			...word,
			text: word.text.split(''),
		};
	});
	const renderWords = () => {
		return (
			<div className="bg-linear-to-r from-fuchsia-500 via-purple-600 to-violet-700 bg-clip-text py-2 text-transparent">
				{wordsArray.map((word, idx) => {
					return (
						<div
							key={`word-${idx}`}
							className="inline-block"
						>
							{word.text.map((char, index) => (
								<span
									key={`char-${index}`}
									className={word.className}
								>
									{char}
								</span>
							))}
							&nbsp;
						</div>
					);
				})}
			</div>
		);
	};

	return (
		<div className={cn('my-2 flex space-x-1', className)}>
			<motion.div
				className="overflow-hidden pb-2"
				initial={{
					width: '0%',
				}}
				whileInView={{
					width: 'fit-content',
				}}
				transition={{
					duration: 2,
					ease: 'linear',
					delay: 0.2,
				}}
			>
				<div
					className="text-lg leading-tight font-bold sm:text-2xl md:text-3xl lg:text-4xl"
					style={{
						whiteSpace: 'nowrap',
					}}
				>
					{renderWords()}{' '}
				</div>{' '}
			</motion.div>
			<motion.span
				initial={{
					opacity: 0,
				}}
				animate={{
					opacity: 1,
				}}
				transition={{
					duration: 0.8,

					repeat: Infinity,
					repeatType: 'reverse',
				}}
				className={cn(
					'bg-zeco-purple mt-1 block h-6 w-0.5 rounded-xs sm:h-8 sm:w-1 md:h-10 lg:h-12',
					cursorClassName
				)}
			></motion.span>
		</div>
	);
};
