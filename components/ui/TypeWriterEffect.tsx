'use client';

import { cn } from '@/utils/cn';
import { motion } from 'framer-motion';

export const TypewriterEffectSmooth = ({
	words,
	className,
	cursorClassName,
}: {
	words: {
		text: string;
		className?: string;
	}[];
	className?: string;
	cursorClassName?: string;
}) => {
	// split text inside of words into array of characters
	const wordsArray = words.map((word) => {
		return {
			...word,
			text: word.text.split(''),
		};
	});
	const renderWords = () => {
		return (
			<div className="bg-clip-text text-transparent bg-gradient-to-r from-fuchsia-500 via-purple-600 to-violet-700 py-2">
				{wordsArray.map((word, idx) => {
					return (
						<div
							key={`word-${idx}`}
							className="inline-block"
						>
							{word.text.map((char, index) => (
								<span
									key={`char-${index}`}
									className={word.className}
								>
									{char}
								</span>
							))}
							&nbsp;
						</div>
					);
				})}
			</div>
		);
	};

	return (
		<div className={cn('flex space-x-1 my-2', className)}>
			<motion.div
				className="overflow-hidden pb-2"
				initial={{
					width: '0%',
				}}
				whileInView={{
					width: 'fit-content',
				}}
				transition={{
					duration: 2,
					ease: 'linear',
					delay: 0.2,
				}}
			>
				<div
					className="text-lg sm:text-2xl md:text-3xl lg:text-4xl font-bold leading-tight"
					style={{
						whiteSpace: 'nowrap',
					}}
				>
					{renderWords()}{' '}
				</div>{' '}
			</motion.div>
			<motion.span
				initial={{
					opacity: 0,
				}}
				animate={{
					opacity: 1,
				}}
				transition={{
					duration: 0.8,

					repeat: Infinity,
					repeatType: 'reverse',
				}}
				className={cn(
					'block rounded-sm w-0.5 sm:w-1 mt-1 h-6 sm:h-8 md:h-10 lg:h-12 bg-zeco-purple',
					cursorClassName
				)}
			></motion.span>
		</div>
	);
};
