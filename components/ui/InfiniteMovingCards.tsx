'use client';

import { cn } from '@/utils/cn';
import { useRef, useEffect, useState } from 'react';

export const InfiniteMovingCards = ({
	items,
	direction = 'right',
	speed = 'slow',
	pauseOnHover = true,
	className,
}: {
	items: {
		icon: () => React.ReactNode;
	}[];
	direction?: 'left' | 'right';
	speed?: 'fast' | 'normal' | 'slow';
	pauseOnHover?: boolean;
	className?: string;
}) => {
	const containerRef = useRef<HTMLDivElement>(null);
	const scrollerRef = useRef<HTMLUListElement>(null);
	const [start, setStart] = useState(false);

	useEffect(() => {
		if (containerRef.current) {
			if (direction === 'left') {
				containerRef.current.style.setProperty('--animation-direction', 'forwards');
			} else {
				containerRef.current.style.setProperty('--animation-direction', 'reverse');
			}

			if (speed === 'fast') {
				containerRef.current.style.setProperty('--animation-duration', '20s');
			} else if (speed === 'normal') {
				containerRef.current.style.setProperty('--animation-duration', '50s');
			} else {
				containerRef.current.style.setProperty('--animation-duration', '80s');
			}
			setStart(true);
		}
	}, [direction, speed]);

	return (
		<div
			ref={containerRef}
			className={cn(
				'scroller relative z-20 max-w-7xl overflow-hidden mask-[linear-gradient(to_right,transparent,white_20%,white_80%,transparent)]',
				className
			)}
		>
			<ul
				ref={scrollerRef}
				className={cn(
					'flex w-max min-w-full shrink-0 flex-nowrap gap-10',
					start && 'animate-scroll',
					pauseOnHover && 'sm:hover:[animation-play-state:paused]'
				)}
			>
				{[...items, ...items].map((item, idx) => (
					<li
						className="flex shrink-0 rounded-2xl border-b-0"
						style={{
							background: 'bg-black',
						}}
						key={idx}
					>
						<div>
							<div className="relative z-20 flex flex-row items-center">
								<span className="flex flex-col gap-1">
									<span className="text-sm leading-[.6]">{item.icon()}</span>
								</span>
							</div>
						</div>
					</li>
				))}
			</ul>
		</div>
	);
};
