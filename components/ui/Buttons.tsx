'use client';

import { memo, useState, useCallback } from 'react';
import { cn } from '@/utils/cn';
import FormComponent from '@/components/landing-page/FormComponent';
import { Button, ButtonProps } from '@heroui/button';
import Link from 'next/link';

interface CTAButtonProps {
	title: string;
	className?: string;
	arrowNeeded?: boolean;
}

// TODO: Create a new button which redirects to a link/ makes an API call on click.
// TODO: Make the onclick more generic.
export const CTAButton: React.FC<CTAButtonProps> = ({ title, className, arrowNeeded = false }) => {
	const [isModalOpen, setIsModalOpen] = useState(false);

	const handleShowModalCallback = useCallback(() => {
		setIsModalOpen(true);
	}, []);

	const handleCloseModal = useCallback(() => {
		setIsModalOpen(false);
	}, []);

	return (
		<div>
			<Button
				className={cn(
					'flex w-48 h-14 my-2 sm:mr-2 py-3 hover:py-3.5 sm:py-3.5 hover:sm:py-4 items-center justify-center hover:space-x-2 font-semibold rounded-2xl text-md cursor-pointer bg-white border-2 border-white hover:border-none text-black hover:bg-linear-to-r from-fuchsia-500 via-purple-600 to-violet-700 hover:text-white',
					className
				)}
				onPress={handleShowModalCallback}
			>
				<span>{title}</span>
				{arrowNeeded && <span className="text-xl md:text-2xl md:pb-0.5">&rarr;</span>}
			</Button>
			<FormComponent
				isOpen={isModalOpen}
				onClose={handleCloseModal}
			/>
		</div>
	);
};

CTAButton.displayName = 'JoinBetaCTAButton';

export const GetStartedCTAButton: React.FC<CTAButtonProps> = ({
	title,
	className,
	arrowNeeded = false,
}) => {
	return (
		<Link href="/login">
			<Button
				className={cn(
					'flex w-48 h-14 my-2 sm:mr-2 py-3 hover:py-3.5 sm:py-3.5 hover:sm:py-4 items-center justify-center hover:space-x-2 font-semibold rounded-2xl text-md cursor-pointer bg-white border-2 border-white hover:border-none text-black hover:bg-linear-to-r from-fuchsia-500 via-purple-600 to-violet-700 hover:text-white',
					className
				)}
			>
				<span>{title}</span>
				{arrowNeeded && <span className="text-xl md:text-2xl md:pb-0.5">&rarr;</span>}
			</Button>
		</Link>
	);
};

GetStartedCTAButton.displayName = 'GetStartedCTAButton';

export const ExploreButton: React.FC = memo(() => {
	const handleScroll = useCallback(() => {
		const section = document.getElementById('multi-model-section');
		const rect = section!!.getBoundingClientRect();
		window.scrollTo({
			top: window.scrollY + rect.top,
			behavior: 'smooth',
		});
	}, []);

	return (
		<Button
			className="flex w-48 h-14 my-2 bg-transparent sm:ml-2 py-3 sm:py-3.5 items-center justify-center font-semibold border-2 border-white text-white rounded-2xl text-md cursor-pointer hover:xl:bg-white hover:xl:text-black"
			onPress={handleScroll}
		>
			Explore
		</Button>
	);
});

ExploreButton.displayName = 'ExploreButton';

export const FormButton: React.FC<ButtonProps> = memo((props: ButtonProps) => {
	return (
		<Button
			className="flex w-24 sm:mt-1 sm:mr-2 py-4 items-center justify-center rounded-2xl text-md cursor-pointer bg-white border-2 border-white hover:border-none text-black hover:bg-linear-to-r from-fuchsia-500 via-purple-600 to-violet-700 hover:text-white"
			{...props}
		>
			Submit
		</Button>
	);
});

FormButton.displayName = 'FormButton';
