'use client';

import { useState, useEffect, memo } from 'react';
import Image from 'next/image';
import { motion, Variants } from 'motion/react';
import { cn } from '@/utils/cn';

interface ProgressiveImageProps {
	src: string;
	alt: string;
	className?: string;
}

const fade: Variants = {
	hidden: { opacity: 0, filter: 'blur(8px)' },
	visible: { opacity: 1, filter: 'blur(0px)' },
};

export const ProgressiveImage: React.FC<ProgressiveImageProps> = memo(({ src, alt, className }) => {
	const [loaded, setLoaded] = useState(false);
	const [error, setError] = useState(false);

	useEffect(() => {
		setLoaded(false);
		setError(false);
	}, [src]);

	const wrapper: React.CSSProperties = {
		position: 'relative',
		overflow: 'hidden',
		width: '100%',
		paddingTop: '100%',
	};

	return (
		<div
			className={cn('relative', className)}
			style={wrapper}
		>
			<motion.div
				initial="hidden"
				animate={loaded ? 'visible' : 'hidden'}
				variants={fade}
				transition={{ duration: 0.45 }}
				className="absolute inset-0"
			>
				<Image
					src={src}
					alt={alt}
					fill
					onLoad={() => setLoaded(true)}
					onError={() => setError(true)}
					loading="lazy"
					decoding="async"
					sizes="(max-width: 768px) 100vw, 50vw"
					className="object-cover"
				/>
			</motion.div>

			{error && (
				<div className="absolute inset-0 flex items-center justify-center bg-zinc-800 text-sm text-zinc-300">
					Failed to load
				</div>
			)}
		</div>
	);
});

ProgressiveImage.displayName = 'ProgressiveImage';
