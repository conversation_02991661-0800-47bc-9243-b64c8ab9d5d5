'use client';
import { cn } from '@/utils/cn';
import { IconMenu2, IconX } from '@tabler/icons-react';
import { motion, AnimatePresence, useScroll, useMotionValueEvent } from 'framer-motion';
import { Button } from '@heroui/button';
import Link from 'next/link';

import React, { useRef, useState, memo, useCallback, useEffect, useMemo } from 'react';

interface NavbarProps {
	children: React.ReactNode;
	className?: string;
	isFixed?: boolean;
}

interface NavBodyProps {
	children: React.ReactNode;
	className?: string;
	visible?: boolean;
}

interface NavItemsProps {
	items: {
		name: string;
		link: string;
	}[];
	className?: string;
	onItemClick?: () => void;
}

interface MobileNavProps {
	children: React.ReactNode;
	className?: string;
	visible?: boolean;
}

interface MobileNavHeaderProps {
	children: React.ReactNode;
	className?: string;
}

interface MobileNavMenuProps {
	children: React.ReactNode;
	className?: string;
	isOpen: boolean;
	onClose: () => void;
}

export const Navbar = memo(({ children, className, isFixed = false }: NavbarProps) => {
	const ref = useRef<HTMLDivElement>(null);
	const { scrollY } = useScroll({
		target: ref,
		offset: ['start start', 'end start'],
	});
	const [visible, setVisible] = useState<boolean>(isFixed);

	const handleScrollChange = useCallback(
		(latest: number) => {
			if (isFixed) return; // Don't change state if navbar is fixed

			if (latest > 100) {
				setVisible(true);
			} else {
				setVisible(false);
			}
		},
		[isFixed]
	);

	useMotionValueEvent(scrollY, 'change', handleScrollChange);

	return (
		<motion.div
			ref={ref}
			className={cn('fixed inset-x-0 top-0 z-40 w-full', className)}
		>
			{React.Children.map(children, (child) =>
				React.isValidElement(child)
					? React.cloneElement(child as React.ReactElement<{ visible?: boolean }>, {
							visible,
						})
					: child
			)}
		</motion.div>
	);
});

Navbar.displayName = 'Navbar';

export const NavBody = memo(({ children, className, visible }: NavBodyProps) => {
	const animationProps = useMemo(
		() => ({
			backdropFilter: visible ? 'blur(10px)' : 'none',
			boxShadow: visible
				? '0 0 24px rgba(34, 42, 53, 0.06), 0 1px 1px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(34, 42, 53, 0.04), 0 0 4px rgba(34, 42, 53, 0.08), 0 16px 68px rgba(47, 48, 55, 0.05), 0 1px 0 rgba(255, 255, 255, 0.1) inset'
				: 'none',
			width: visible ? '40%' : '100%',
			y: visible ? 20 : 0,
		}),
		[visible]
	);
	return (
		<motion.div
			animate={animationProps}
			transition={{
				type: 'spring',
				stiffness: 200,
				damping: 50,
			}}
			style={{
				minWidth: '800px',
			}}
			className={cn(
				'relative z-60 mx-auto hidden w-full max-w-7xl flex-row items-center justify-between self-start rounded-full bg-transparent px-4 py-2 lg:flex dark:bg-transparent',
				visible && 'bg-white/90 dark:bg-neutral-950/90',
				className
			)}
		>
			{children}
		</motion.div>
	);
});

NavBody.displayName = 'NavBody';

export const NavItems = memo(({ items, className, onItemClick }: NavItemsProps) => {
	const [hovered, setHovered] = useState<number | null>(null);

	const handleMouseLeave = useCallback(() => setHovered(null), []);
	const handleMouseEnter = useCallback((idx: number) => setHovered(idx), []);

	return (
		<motion.div
			onMouseLeave={handleMouseLeave}
			className={cn(
				'absolute inset-0 hidden flex-1 flex-row items-center justify-center space-x-2 text-sm font-medium text-zinc-600 transition duration-200 hover:text-zinc-800 lg:flex md:space-x-2',
				className
			)}
		>
			{items.map((item, idx) => (
				<Link
					key={`link-${idx}`}
					href={item.link}
					onMouseEnter={() => handleMouseEnter(idx)}
					onClick={onItemClick}
					className="relative px-4 py-2 text-neutral-600 dark:text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-100 transition-colors"
				>
					{hovered === idx && (
						<motion.div
							layoutId="hovered"
							className="absolute inset-0 h-full w-full rounded-full bg-gray-100 dark:bg-neutral-800"
						/>
					)}
					<span className="relative z-20">{item.name}</span>
				</Link>
			))}
		</motion.div>
	);
});

NavItems.displayName = 'NavItems';

export const MobileNav = memo(({ children, className, visible }: MobileNavProps) => {
	const animationProps = useMemo(
		() => ({
			backdropFilter: visible ? 'blur(10px)' : 'none',
			boxShadow: visible
				? '0 0 24px rgba(34, 42, 53, 0.06), 0 1px 1px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(34, 42, 53, 0.04), 0 0 4px rgba(34, 42, 53, 0.08), 0 16px 68px rgba(47, 48, 55, 0.05), 0 1px 0 rgba(255, 255, 255, 0.1) inset'
				: 'none',
			width: visible ? '90%' : '100%',
			paddingRight: visible ? '12px' : '0px',
			paddingLeft: visible ? '12px' : '0px',
			borderRadius: visible ? '4px' : '2rem',
			y: visible ? 20 : 0,
		}),
		[visible]
	);
	return (
		<motion.div
			animate={animationProps}
			transition={{
				type: 'spring',
				stiffness: 200,
				damping: 50,
			}}
			className={cn(
				'relative z-50 mx-auto flex w-full max-w-[calc(100vw-2rem)] flex-col items-center justify-between bg-transparent px-0 py-2 lg:hidden',
				visible && 'bg-white/90 dark:bg-neutral-950/90',
				className
			)}
		>
			{children}
		</motion.div>
	);
});

MobileNav.displayName = 'MobileNav';

export const MobileNavHeader = memo(({ children, className }: MobileNavHeaderProps) => {
	return (
		<div className={cn('flex w-full flex-row items-center justify-between', className)}>
			{children}
		</div>
	);
});

MobileNavHeader.displayName = 'MobileNavHeader';

export const MobileNavMenu = memo(
	({ children, className, isOpen, onClose }: MobileNavMenuProps) => {
		const menuRef = useRef<HTMLDivElement>(null);

		// Focus trap implementation
		useEffect(() => {
			if (!isOpen || !menuRef.current) return;

			const menu = menuRef.current;
			const focusableElements = menu.querySelectorAll(
				'a[href], button, [tabindex]:not([tabindex="-1"])'
			);
			const firstElement = focusableElements[0] as HTMLElement;
			const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

			// Focus the first element when menu opens
			firstElement?.focus();

			const handleKeyDown = (e: KeyboardEvent) => {
				if (e.key === 'Escape') {
					onClose();
					return;
				}

				if (e.key === 'Tab') {
					if (e.shiftKey) {
						// Shift + Tab: moving backwards
						if (document.activeElement === firstElement) {
							e.preventDefault();
							lastElement?.focus();
						}
					} else {
						// Tab: moving forwards
						if (document.activeElement === lastElement) {
							e.preventDefault();
							firstElement?.focus();
						}
					}
				}
			};

			document.addEventListener('keydown', handleKeyDown);

			return () => {
				document.removeEventListener('keydown', handleKeyDown);
			};
		}, [isOpen, onClose]);

		return (
			<AnimatePresence>
				{isOpen && (
					<motion.div
						ref={menuRef}
						initial={{ opacity: 0, y: -20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: -20 }}
						transition={{ duration: 0.3, ease: 'easeInOut' }}
						className={cn(
							'absolute inset-x-0 top-16 z-50 flex w-full flex-col items-start justify-start gap-4 rounded-lg bg-white px-4 py-8 shadow-[0_0_24px_rgba(34,42,53,0.06),0_1px_1px_rgba(0,0,0,0.05),0_0_0_1px_rgba(34,42,53,0.04),0_0_4px_rgba(34,42,53,0.08),0_16px_68px_rgba(47,48,55,0.05),0_1px_0_rgba(255,255,255,0.1)_inset] dark:bg-neutral-950',
							className
						)}
						role="menu"
						aria-label="Mobile navigation menu"
					>
						{children}
					</motion.div>
				)}
			</AnimatePresence>
		);
	}
);

MobileNavMenu.displayName = 'MobileNavMenu';

export const MobileNavToggle = memo(
	({ isOpen, onClick }: { isOpen: boolean; onClick: () => void }) => {
		return (
			<button
				onClick={onClick}
				aria-label={isOpen ? 'Close menu' : 'Open menu'}
				aria-expanded={isOpen}
				className="p-1 text-black dark:text-white hover:text-neutral-600 dark:hover:text-neutral-300 transition-colors"
			>
				{isOpen ? <IconX className="w-6 h-6" /> : <IconMenu2 className="w-6 h-6" />}
			</button>
		);
	}
);

MobileNavToggle.displayName = 'MobileNavToggle';

export const NavbarButton = memo(
	({
		href,
		children,
		className,
		onPress,
		...props
	}: {
		href?: string;
		children: React.ReactNode;
		className?: string;
		onPress?: () => void;
	}) => {
		return (
			<Button
				as={href ? Link : 'button'}
				href={href}
				className={cn(
					'font-semibold bg-white text-black hover:bg-linear-to-r from-fuchsia-500 via-purple-600 to-violet-700 hover:text-white transition-all duration-200',
					className
				)}
				onPress={onPress}
				{...props}
			>
				{children}
			</Button>
		);
	}
);

NavbarButton.displayName = 'NavbarButton';
