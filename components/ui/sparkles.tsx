'use client';
import { useEffect, useState, memo, useMemo, useCallback } from 'react';
import Particles, { initParticlesEngine } from '@tsparticles/react';
import type { Container } from '@tsparticles/engine';
import { loadSlim } from '@tsparticles/slim';
import { cn } from '@/utils/cn';
import { motion, useAnimation } from 'motion/react';

type ParticlesProps = {
	id?: string;
	className?: string;
	background?: string;
	particleSize?: number;
	minSize?: number;
	maxSize?: number;
	speed?: number;
	particleColor?: string;
	particleDensity?: number;
};

const SparklesCore = ({
	id,
	className,
	background = '#0d47a1',
	minSize = 1,
	maxSize = 3,
	speed = 4,
	particleColor = '#ffffff',
	particleDensity = 60,
}: ParticlesProps) => {
	const [init, setInit] = useState(false);
	const controls = useAnimation();

	useEffect(() => {
		const loadParticles = async () => {
			await initParticlesEngine(async (engine) => {
				await loadSlim(engine);
			});
			setInit(true);
		};
		loadParticles();
	}, []);

	const particlesLoaded = useCallback(
		async (container?: Container) => {
			if (container) {
				controls.start({
					opacity: 1,
					transition: { duration: 1 },
				});
			}
		},
		[controls]
	);

	const particleOptions = useMemo(
		() => ({
			background: { color: { value: background } },
			fullScreen: { enable: false, zIndex: 1 },
			fpsLimit: 60,
			interactivity: {
				events: {
					onClick: { enable: true, mode: 'push' },
					onHover: { enable: false },
					resize: { enable: true },
				},
				modes: {
					push: { quantity: 2 },
				},
			},
			particles: {
				collisions: { enable: false },
				color: { value: particleColor },
				move: { enable: true, speed: { min: 0.1, max: 0.5 } },
				number: {
					density: { enable: true, width: 400, height: 400 },
					value: particleDensity,
				},
				opacity: {
					value: { min: 0.1, max: 1 },
					animation: {
						enable: true,
						speed,
						startValue: 'random' as const,
					},
				},
				shape: { type: 'circle' },
				size: {
					value: { min: minSize, max: maxSize },
					animation: { enable: false, speed: 5 },
				},
				zIndex: { value: 0 },
			},
			detectRetina: true,
		}),
		[background, minSize, maxSize, speed, particleColor, particleDensity]
	);

	return (
		<motion.div
			animate={controls}
			className={cn('opacity-0', className)}
		>
			{init && (
				<Particles
					id={id || 'tsparticles'}
					className={cn('h-full w-full')}
					particlesLoaded={particlesLoaded}
					options={particleOptions}
				/>
			)}
		</motion.div>
	);
};

export default memo(SparklesCore);
