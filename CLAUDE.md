# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Package Manager

This project uses **PNPM** as the package manager. Always use `pnpm` commands:

```bash
pnpm install        # Install dependencies
pnpm dev           # Start development server (localhost:3000)
pnpm build         # Build for production
pnpm start         # Start production server
pnpm lint          # Run ESLint
pnpm format        # Format code with Prettier
pnpm format:check  # Check code formatting
npx tsc --noEmit   # Check TypeScript types without building
```

### Development Workflow

**IMPORTANT**: Before running `pnpm build`, always check for TypeScript issues:

```bash
npx tsc --noEmit
```

Only run `pnpm build` after TypeScript check passes and changes are approved.

### Requirements

- Node.js ≥20.12.0
- PNPM ≥9.2.0

## Project Architecture

### Framework Stack

- **Next.js 15** with App Router
- **React 19** with TypeScript
- **Supabase** for authentication, database, and storage
- **HeroUI** (NextUI v2) for UI components
- **Tailwind CSS** for styling
- **Framer Motion** for animations

### High-Level Architecture

This is a comprehensive AI platform that provides multiple AI services in one interface:

1. **Multi-Modal AI Chat** (`/chat`) - Conversational AI with multiple model support
2. **Image Generation** (`/image`) - AI-powered image creation and enhancement
3. **Video Generation** (`/video`) - Text-to-video generation

### Key Directory Structure

```
app/
├── (post-auth)/           # Protected routes requiring authentication
│   ├── (main)/           # Core AI features (chat, image, video)
├── api/                  # API routes for AI services
├── auth/                 # Authentication flows
└── subscriptions/        # Payment and subscription handling

components/
├── chat/                 # Chat interface components
├── landing-page/         # Marketing and onboarding
└── ui/                  # Reusable UI components

models/                   # AI model configurations
├── conversational/       # Chat models (GPT, Claude, Gemini, etc.)
├── image/               # Image generation models
├── video/               # Video generation models
└── providers/           # AI provider configurations

service/                 # Business logic and data management
├── db/                  # Database models and queries
├── managers/            # Payment and subscription management
└── helpers/             # Utility functions

```

### Authentication & Database

- **Supabase Auth** handles user authentication with social logins
- **PostgreSQL** database with Supabase for user data, chat history, subscriptions
- **Row Level Security (RLS)** implemented for data protection
- **Supabase Storage** for user-generated content (images, videos, documents)

### AI Model Management

Models are organized by type in the `models/` directory:

- Each model has specific constraints, parameters, and configurations
- Model selection is handled through specialized dialog components
- Provider-specific configurations are in `models/providers/`

### Payment Integration

- **Creem** payment processor for Indian market
- Subscription-based model with tiered access
- Webhook handlers for payment notifications
- Usage tracking and credit management

### External Services Integration

- **FAL AI** for image and video generation
- **ElevenLabs** for voice synthesis
- **Firecrawl** for web scraping
- **Multiple AI providers** (OpenAI, Anthropic, Google, Mistral, xAI)

### Development Patterns

1. **Route Groups**: Use Next.js route groups for logical organization
2. **Server Actions**: Leverage Next.js server actions for form handling
3. **Streaming**: Implement streaming for real-time AI responses
4. **Type Safety**: Comprehensive TypeScript usage with Zod validation
5. **Error Handling**: Centralized error handling with user-friendly messages

### Key Features Implementation

- **Real-time Chat**: WebSocket-like streaming with AI models
- **File Upload**: Multi-format document processing with Python services
- **Image Processing**: Client-side and server-side image handling
- **Payment Flow**: Subscription-based access with usage tracking
- **PWA Support**: Progressive Web App capabilities with service workers

### Environment Variables

Critical environment variables are exposed through `next.config.js`:

- AI provider API keys (OpenAI, Anthropic, Google, etc.)
- Supabase configuration
- Payment processor keys
- External service APIs

### Code Quality

- **ESLint + Prettier** for code formatting
- **Husky + lint-staged** for pre-commit hooks
- **TypeScript** strict mode enabled
- **Tailwind CSS** with custom design system colors

## Architecture Layers - CRITICAL RULE

NEVER write direct database queries in Server Actions or Managers!

**Layer Structure:**

1. **Server Actions** (`app/*/actions.ts`): Authentication, validation, coordination only
2. **Managers** (`service/managers/*.ts`): Business logic, orchestration, call DB functions
3. **DB Layer** (`service/db/*.ts`): Pure data access, no business logic

**Examples:**

- ❌ `await supabase.from('table').select()` in actions or managers
- ✅ `await DbFunction.getTable()` in managers
- ✅ `const { data } = await ManagerFunction.businessLogic()` in actions
