@import url('https://fonts.googleapis.com/css2?family=Satisfy&display=swap');
@import 'katex/dist/katex.min.css';
@import 'tailwindcss';

/* Tailwind CSS v4 Theme Configuration */
@theme {
	/* Custom Colors */
	--color-zeco-purple: #9455d3;
	--color-card: #18141e;
	--color-card-button-bg: #322142;
	--color-grey: #18141e;
	--color-matte-black: #28282b;
	--color-chat: #131314;
	--color-primary-text: #ececec;
	--color-secondary-text: #b4b4b4;
	--color-sidebar: #1e1f20;
	--color-sidebar-hover: #282a2c;

	/* Font Families */
	--font-family-inter: inter, system-ui;
	--font-family-satisfy: Satisfy, sans-serif;

	/* Custom Animations */
	--animate-meteor-effect: meteor 5s linear infinite;
	--animate-scroll: scroll var(--animation-duration, 40s) var(--animation-direction, forwards)
		linear infinite;
	--animate-fade-in: fadeIn 0.5s ease-in-out;
}

/* Custom Keyframes */
@keyframes scroll {
	to {
		transform: translate(calc(-50% - 0.5rem));
	}
}

@keyframes meteor {
	0% {
		transform: rotate(215deg) translateX(0);
		opacity: 1;
	}
	70% {
		opacity: 1;
	}
	100% {
		transform: rotate(215deg) translateX(-500px);
		opacity: 0;
	}
}

@keyframes fadeIn {
	0% {
		opacity: 0;
		transform: translateY(10px);
	}
	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

/* Custom Background Utilities */
@utility bg-grid {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='currentColor'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e");
}

@utility bg-grid-small {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='8' height='8' fill='none' stroke='currentColor'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e");
}

@utility bg-dot {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='16' height='16' fill='none'%3e%3ccircle fill='currentColor' id='pattern-circle' cx='10' cy='10' r='1.6257413380501518'%3e%3c/circle%3e%3c/svg%3e");
}

@utility bg-dot-thick {
	background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='16' height='16' fill='none'%3e%3ccircle fill='currentColor' id='pattern-circle' cx='10' cy='10' r='2.5'%3e%3c/circle%3e%3c/svg%3e");
}

/* Hide scrollbars globally while maintaining scroll functionality */
* {
	/* For WebKit browsers (Chrome, Safari) */
	::-webkit-scrollbar {
		display: none;
	}

	/* For Firefox */
	scrollbar-width: none;

	/* For Internet Explorer and Edge */
	-ms-overflow-style: none;
}

.katex-mathml {
	display: none !important;
}

.Satisfy {
	font-family: 'Satisfy', cursive;
	font-weight: normal;
	font-style: normal;
}

.text-fade-right {
	-webkit-mask-image: linear-gradient(to right, black 75%, transparent 95%);
	mask-image: linear-gradient(to right, black 75%, transparent 95%);
}
