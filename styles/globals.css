@import url('https://fonts.googleapis.com/css2?family=Satisfy&display=swap') layer(base);
@import 'katex/dist/katex.min.css' layer(base);

@import 'tailwindcss';

@config "../tailwind.config.ts";

@theme inline {
	/* Custom Colors */
	--color-zeco-purple: #9455d3;
	--color-card: #18141e;
	--color-card-button-bg: #322142;
	--color-grey: #18141e;
	--color-matte-black: #28282b;
	--color-chat: #131314;
	--color-primary-text: #ececec;
	--color-secondary-text: #b4b4b4;
	--color-sidebar: #1e1f20;
	--color-sidebar-hover: #282a2c;

	/* Custom Fonts */
	--font-family-inter: inter, system-ui;
	--font-family-satisfy: Satisfy, sans-serif;

	/* Custom Animations */
	--animate-meteor-effect: meteor 5s linear infinite;
	--animate-scroll: scroll var(--animation-duration, 40s) var(--animation-direction, forwards)
		linear infinite;
	--animate-fade-in: fadeIn 0.5s ease-in-out;

	@keyframes meteor {
		0% {
			transform: rotate(215deg) translateX(0);
			opacity: 1;
		}
		70% {
			opacity: 1;
		}
		100% {
			transform: rotate(215deg) translateX(-500px);
			opacity: 0;
		}
	}

	@keyframes scroll {
		to {
			transform: translate(calc(-50% - 0.5rem));
		}
	}

	@keyframes fadeIn {
		0% {
			opacity: 0;
			transform: translateY(10px);
		}
		100% {
			opacity: 1;
			transform: translateY(0);
		}
	}
}

/* Hide scrollbars globally while maintaining scroll functionality */
* {
	/* For WebKit browsers (Chrome, Safari) */
	::-webkit-scrollbar {
		display: none;
	}

	/* For Firefox */
	scrollbar-width: none;

	/* For Internet Explorer and Edge */
	-ms-overflow-style: none;
}

.katex-mathml {
	display: none !important;
}

.Satisfy {
	font-family: 'Satisfy', cursive;
	font-weight: normal;
	font-style: normal;
}

.text-fade-right {
	-webkit-mask-image: linear-gradient(to right, black 75%, transparent 95%);
	mask-image: linear-gradient(to right, black 75%, transparent 95%);
}
