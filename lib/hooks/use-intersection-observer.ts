import { useEffect, useRef, useState } from 'react';

interface UseIntersectionObserverOptions {
	threshold?: number | number[];
	root?: Element | null;
	rootMargin?: string;
	freezeOnceVisible?: boolean;
}

interface UseIntersectionObserverReturn {
	ref: React.RefObject<HTMLDivElement | null>;
	isIntersecting: boolean;
	hasBeenVisible: boolean;
}

/**
 * Custom hook that uses Intersection Observer API to detect when an element enters the viewport
 * @param options - Configuration options for the Intersection Observer
 * @returns Object containing ref to attach to element, current intersection state, and whether element has ever been visible
 */
export const useIntersectionObserver = ({
	threshold = 0.1,
	root = null,
	rootMargin = '50px',
	freezeOnceVisible = false,
}: UseIntersectionObserverOptions = {}): UseIntersectionObserverReturn => {
	const ref = useRef<HTMLDivElement>(null);
	const [isIntersecting, setIsIntersecting] = useState(false);
	const [hasBeenVisible, setHasBeenVisible] = useState(false);

	useEffect(() => {
		const element = ref.current;
		if (!element) return;

		// If freezeOnceVisible is true and element has been visible, don't observe anymore
		if (freezeOnceVisible && hasBeenVisible) {
			return;
		}

		const observer = new IntersectionObserver(
			([entry]) => {
				const isElementIntersecting = entry.isIntersecting;
				setIsIntersecting(isElementIntersecting);

				if (isElementIntersecting && !hasBeenVisible) {
					setHasBeenVisible(true);
				}

				// If freezeOnceVisible is true and element becomes visible, stop observing
				if (freezeOnceVisible && isElementIntersecting) {
					observer.unobserve(element);
				}
			},
			{
				threshold,
				root,
				rootMargin,
			}
		);

		observer.observe(element);

		return () => {
			observer.unobserve(element);
		};
	}, [threshold, root, rootMargin, freezeOnceVisible, hasBeenVisible]);

	return {
		ref,
		isIntersecting,
		hasBeenVisible,
	};
};
