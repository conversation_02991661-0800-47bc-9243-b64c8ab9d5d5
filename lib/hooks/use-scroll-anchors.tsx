import { useCallback, useEffect, useRef } from 'react';

export const useScrollAnchor = () => {
	const messagesRef = useRef<HTMLDivElement>(null);
	const scrollRef = useRef<HTMLDivElement>(null);

	const scrollToBottom = useCallback(() => {
		if (messagesRef.current) {
			messagesRef.current.scrollIntoView({
				behavior: 'smooth',
			});
		}
	}, []);

	// Scroll to the end upon initial load of the chat.
	useEffect(() => {
		// HACK: Add a delay to ensure the DOM has fully rendered.
		const timeoutId = setTimeout(() => {
			scrollToBottom();
		}, 500);

		return () => clearTimeout(timeoutId);
	}, [scrollToBottom]);

	useEffect(() => {
		if (scrollRef.current) {
			const observer = new MutationObserver(() => {
				if (messagesRef.current) {
					messagesRef.current.scrollIntoView({ behavior: 'smooth' });
				}
			});

			observer.observe(scrollRef.current, { childList: true });

			return () => observer.disconnect();
		}
	}, []);

	return {
		messagesRef,
		scrollRef,
	};
};
