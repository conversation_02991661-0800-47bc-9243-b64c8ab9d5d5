import { useState, useEffect } from 'react';

// Cookie consent options
export interface CookieOptions {
	necessary: boolean;
	analytics: boolean;
	marketing: boolean;
}

// Cookie consent state stored in localStorage
export interface CookieConsentState {
	accepted: boolean;
	options: CookieOptions;
	timestamp: number;
}

export const useCookieConsent = () => {
	// State for cookie options
	const [cookieOptions, setCookieOptions] = useState<CookieOptions>({
		necessary: true, // Always required
		analytics: false,
		marketing: false,
	});

	// State for consent status
	const [hasConsent, setHasConsent] = useState<boolean | null>(null);

	// State for showing the settings modal
	const [showSettings, setShowSettings] = useState(false);

	// Load consent state from localStorage on mount
	useEffect(() => {
		const loadConsentState = () => {
			const consentState = localStorage.getItem('cookie-consent');
			if (!consentState) {
				setHasConsent(false);
				return;
			}

			try {
				const { accepted, options } = JSON.parse(consentState) as CookieConsentState;
				setCookieOptions(options);
				setHasConsent(accepted);
			} catch (e) {
				setHasConsent(false);
			}
		};

		loadConsentState();
	}, []);

	// Save consent to localStorage
	const saveConsent = (accepted: boolean, options: CookieOptions) => {
		const consentState: CookieConsentState = {
			accepted,
			options,
			timestamp: Date.now(),
		};
		localStorage.setItem('cookie-consent', JSON.stringify(consentState));
		setCookieOptions(options);
		setHasConsent(accepted);
	};

	// Accept all cookies
	const acceptAllCookies = () => {
		const allOptions: CookieOptions = {
			necessary: true,
			analytics: true,
			marketing: true,
		};
		saveConsent(true, allOptions);
	};

	// Accept only necessary cookies
	const acceptNecessaryCookies = () => {
		const necessaryOptions: CookieOptions = {
			necessary: true,
			analytics: false,
			marketing: false,
		};
		saveConsent(true, necessaryOptions);
	};

	// Save custom cookie settings
	const saveCustomCookieSettings = (options: CookieOptions) => {
		saveConsent(true, {
			...options,
			necessary: true, // Always keep necessary cookies
		});
	};

	// Open cookie settings modal
	const openCookieSettings = () => {
		setShowSettings(true);
	};

	// Close cookie settings modal
	const closeCookieSettings = () => {
		setShowSettings(false);
	};

	return {
		cookieOptions,
		hasConsent,
		showSettings,
		acceptAllCookies,
		acceptNecessaryCookies,
		saveCustomCookieSettings,
		openCookieSettings,
		closeCookieSettings,
		setShowSettings,
	};
};

export default useCookieConsent;
