import { useState, useEffect } from 'react';

interface LocationData {
	country: string;
	country_code: string;
	city: string;
	region: string;
	latitude: number;
	longitude: number;
	timezone: string;
	languages: string;
}

const useUserLocation = () => {
	const [currency, setCurrency] = useState<'usd' | 'inr'>('inr');
	const [location, setLocation] = useState<LocationData | null>(null);

	useEffect(() => {
		// Check local storage first
		const storedLocation = localStorage.getItem('userLocation');
		if (storedLocation) {
			const parsedLocation = JSON.parse(storedLocation);
			setLocation(parsedLocation);
			setCurrency(parsedLocation.country_code === 'IN' ? 'inr' : 'usd');
			return;
		}

		// Try IP-based detection
		fetch('https://ipapi.co/json/')
			.then((res) => res.json())
			.then((data) => {
				const locationData: LocationData = {
					country: data.country_name,
					country_code: data.country_code,
					city: data.city,
					region: data.region,
					latitude: data.latitude,
					longitude: data.longitude,
					timezone: data.timezone,
					languages: data.languages,
				};
				setLocation(locationData);
				setCurrency(data.country_code === 'IN' ? 'inr' : 'usd');
				localStorage.setItem('userLocation', JSON.stringify(locationData));
			})
			.catch(() => {
				// Fallback to server-side detection
				fetch('/api/detect-location')
					.then((res) => res.json())
					.then((data) => {
						const locationData: LocationData = {
							country: data.country_name || 'Unknown',
							country_code: data.country,
							city: data.city || 'Unknown',
							region: data.region || 'Unknown',
							latitude: data.latitude || 0,
							longitude: data.longitude || 0,
							timezone: data.timezone || 'UTC',
							languages: data.languages || 'en',
						};
						setLocation(locationData);
						setCurrency(data.country === 'IN' ? 'inr' : 'usd');
						localStorage.setItem('userLocation', JSON.stringify(locationData));
					});
			});
	}, []);

	return {
		currency,
		setCurrency,
		location,
	};
};

export default useUserLocation;
