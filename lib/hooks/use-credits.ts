'use client';

import { useState, useEffect, useCallback } from 'react';
import { useUserContext } from '@/app/(post-auth)/providers';
import { createSupabaseBrowserClient } from '@/utils/supabase/client';

interface CreditData {
	credits: number;
	creditsUsed: number;
	creditsGranted: number;
	tier1TokensUsed: number;
	tier1TokensGranted: number;
	tier2TokensUsed: number;
	tier2TokensGranted: number;
}

interface UseCreditReturn {
	creditData: CreditData | null;
	isLoading: boolean;
	error: string | null;
	refreshCredits: () => Promise<void>;
}

export const useCredits = (): UseCreditReturn => {
	const user = useUserContext();
	const supabase = createSupabaseBrowserClient();
	const [creditData, setCreditData] = useState<CreditData | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	const fetchCredits = useCallback(async () => {
		if (!user?.id) {
			setIsLoading(false);
			return;
		}

		try {
			setError(null);
			const response = await fetch('/api/user/credits');
			if (!response.ok) {
				throw new Error(`Failed to fetch credits: ${response.status}`);
			}

			const data = await response.json();
			setCreditData(data);
		} catch (err) {
			//console.error('Error fetching credits:', err);
			setError(err instanceof Error ? err.message : 'Failed to fetch credits');
			setCreditData({
				credits: 0,
				creditsUsed: 0,
				creditsGranted: 0,
				tier1TokensUsed: 0,
				tier1TokensGranted: 0,
				tier2TokensUsed: 0,
				tier2TokensGranted: 0,
			});
		} finally {
			setIsLoading(false);
		}
	}, [user?.id]);

	const refreshCredits = useCallback(async () => {
		setIsLoading(true);
		await fetchCredits();
	}, [fetchCredits]);

	useEffect(() => {
		if (!user) return;

		// Initial fetch
		fetchCredits();

		// Setup real-time subscription
		const channel = supabase
			.channel('credit-updates')
			.on(
				'postgres_changes',
				{
					event: 'UPDATE',
					schema: 'public',
					table: 'subscription_cycle',
					filter: `user_id=eq.${user.id}`,
				},
				(payload) => {
					//console.log('Credit update received:', payload);
					// Immediately refresh credits when the subscription cycle is updated
					fetchCredits();
				}
			)
			.subscribe();

		// Cleanup function to remove the channel subscription when the component unmounts
		return () => {
			supabase.removeChannel(channel);
		};
	}, [user, supabase, fetchCredits]);

	return {
		creditData,
		isLoading,
		error,
		refreshCredits,
	};
};
