'use client';

import { useEffect } from 'react';
import { useUserContext } from '@/app/(post-auth)/providers';
import { createSupabaseBrowserClient } from '@/utils/supabase/client';
import { addToast } from '@heroui/toast';
import { getSubscriptionPlan } from '@/service/db/subscription-plan';

export function useSubscriptionListener() {
	const user = useUserContext();
	const supabase = createSupabaseBrowserClient();

	useEffect(() => {
		if (!user) return;

		// Get free plan ID for filtering
		const { data: freePlan } = getSubscriptionPlan('zeco-free-plan');
		const freePlanId = freePlan?.id || 'zeco-free-plan';

		const channel = supabase
			.channel('subscription-updates')
			.on(
				'postgres_changes',
				{
					event: 'INSERT',
					schema: 'public',
					table: 'user_subscription',
					filter: `user_id=eq.${user.id}&status=eq.active&subscription_plan_id!=eq.${freePlanId}`,
				},
				async (payload) => {
					console.log('New subscription detected:', payload.new);

					addToast({
						title: 'Subscription Active!',
						description: 'Your new plan is ready. All features have been unlocked.',
						color: 'success',
					});

					// Refresh the user's session to get the latest user_metadata
					await supabase.auth.refreshSession();
				}
			)
			.subscribe();

		// Cleanup function to remove the channel subscription when the component unmounts
		return () => {
			supabase.removeChannel(channel);
		};
	}, [user, supabase]);
}
