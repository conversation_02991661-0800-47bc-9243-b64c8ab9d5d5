name: Deploy to Production

on:
    push:
        branches:
            - main

jobs:
    deploy:
        name: Deploy to Vercel
        runs-on: ubuntu-latest
        if: contains(github.event.head_commit.message, '#deploy-to-prod')

        steps:
            - name: Trigger Vercel Deploy Hook
              run: |
                  curl -f -X POST "${{ secrets.VERCEL_DEPLOY_HOOK_URL }}"

            - name: Deployment Status
              run: echo "Deployment triggered successfully"
