import { ImageModelDisplayName } from '@/models/image/image-generation-models';
import { VideoModelDisplayName } from '@/models/video/video-generation-models';

/**
 * Credit cost configuration for different operations
 */

// Credit costs per model (doubled from the pricing table)
export const MODEL_CREDIT_COSTS: Record<string, number> = {
	// Image models
	[ImageModelDisplayName.Stable_Diffusion_XL]: 0.5,
	[ImageModelDisplayName.FLUX_1_1_PRO]: 1.0,
	[ImageModelDisplayName.FLUX_KONTEXT_PRO]: 1.0,
	[ImageModelDisplayName.Ideogram_V2]: 1.0,
	[ImageModelDisplayName.Ideogram_V3]: 2.0,
	[ImageModelDisplayName.Imagen_3]: 1.0,
	[ImageModelDisplayName.Imagen_4]: 2.5,
	[ImageModelDisplayName.DALL_E_3]: 2.0,
	[ImageModelDisplayName.GPT_Image_1]: 2.0,

	// Video models
	[VideoModelDisplayName.Stable_Video_Diffusion]: 4.0,
	[VideoModelDisplayName.Luma_Ray_2]: 26.0,
	[VideoModelDisplayName.Luma_Ray_2_Flash]: 10.0,
	[VideoModelDisplayName.Kling_V1_6_Standard]: 12.0,
	[VideoModelDisplayName.Kling_V1_6_Pro]: 24.0,
	[VideoModelDisplayName.Kling_V2_Master]: 70.0,
	[VideoModelDisplayName.MiniMax_V1]: 26.0,
	[VideoModelDisplayName.Veo_2]: 26.0,
	[VideoModelDisplayName.Veo_3]: 26.0,
	[VideoModelDisplayName.Luma_Dream_Machine]: 10.0,
} as const;

// Resolution multipliers for images (only applies to DALL-E 3 HD)
export const IMAGE_RESOLUTION_MULTIPLIERS = {
	'1024x1024': 1.0, // Base resolution
	'1024x1792': 1.0, // Standard tall format
	'1792x1024': 1.0, // Standard wide format
	'2048x2048': 2.0, // 2K resolution (HD) - only for DALL-E 3
	'2048x3584': 2.0, // 2K tall format - only for DALL-E 3
	'3584x2048': 2.0, // 2K wide format - only for DALL-E 3
} as const;

// Video resolution multipliers (only applies to Luma models)
export const VIDEO_RESOLUTION_MULTIPLIERS = {
	'540p': 1.0, // Base resolution (540p)
	'720p': 2.0, // 2x cost for 720p
	'1080p': 4.0, // 4x cost for 1080p
	'1280x720': 2.0, // Same as 720p
	'1920x1080': 4.0, // Same as 1080p
} as const;

/**
 * Calculate credit cost for image generation
 */
export function calculateImageCreditCost(
	modelName: ImageModelDisplayName,
	options: {
		resolution?: string;
		numberOfImages?: number;
	} = {}
): number {
	const { resolution = '1024x1024', numberOfImages = 1 } = options;

	// Get exact credit cost for the model
	let baseCost = MODEL_CREDIT_COSTS[modelName] || 1.0;

	// Apply resolution multiplier only for DALL-E 3 HD
	if (modelName === ImageModelDisplayName.DALL_E_3) {
		const resolutionMultiplier =
			IMAGE_RESOLUTION_MULTIPLIERS[resolution as keyof typeof IMAGE_RESOLUTION_MULTIPLIERS] ||
			1.0;
		baseCost *= resolutionMultiplier;
		// DALL-E 3 base cost is 2.0, HD (2K) doubles it to 4.0
	}

	// Handle when fast or hq is passed in the params
	// TODO: For Imagen 3 or 4 : highQuality = true means Imagen 3 HQ (1.0 credits), false means Fast (0.5 credits)
	// if (modelName === ImageModelDisplayName.Imagen_3) {
	// 	baseCost = highQuality ? 1.0 : 0.5; // Fast: 0.5, HQ: 1.0
	// }

	// Apply number of images
	const totalCost = baseCost * numberOfImages;

	// Round to nearest 0.25 credit (since we have 0.25 increments) and ensure minimum of 0.25
	return Math.max(0.25, Math.ceil(totalCost * 4) / 4);
}

/**
 * Calculate credit cost for video generation
 */
export function calculateVideoCreditCost(
	modelName: VideoModelDisplayName,
	options: {
		duration?: number | string; // seconds or string like '5s'
		resolution?: string;
		withAudio?: boolean; // For Veo 3 with audio
	} = {}
): number {
	let { duration = 5, resolution = '540p', withAudio = false } = options;

	// Normalize duration if it's a string like '5s'
	if (typeof duration === 'string') {
		duration = parseInt(duration.replace('s', ''), 10);
	}

	// Get exact credit cost for the model
	let baseCost = MODEL_CREDIT_COSTS[modelName] || 5.0;

	// Special handling for per-second models (Veo 2, Veo 3)
	if (modelName === VideoModelDisplayName.Veo_2 || modelName === VideoModelDisplayName.Veo_3) {
		// Veo models are priced per second
		baseCost = withAudio && modelName === VideoModelDisplayName.Veo_3 ? 39.0 : 26.0;
		const totalCost = baseCost * duration;

		// Apply resolution multiplier for Veo models if needed
		const resolutionMultiplier =
			VIDEO_RESOLUTION_MULTIPLIERS[resolution as keyof typeof VIDEO_RESOLUTION_MULTIPLIERS] ||
			1.0;
		return Math.max(1, Math.ceil(totalCost * resolutionMultiplier));
	}

	// For Luma Ray 2 and Ray 2 Flash: 5s = base, 9s = 2x base (after resolution multiplier)
	if (
		modelName === VideoModelDisplayName.Luma_Ray_2 ||
		modelName === VideoModelDisplayName.Luma_Ray_2_Flash
	) {
		const resolutionMultiplier =
			VIDEO_RESOLUTION_MULTIPLIERS[resolution as keyof typeof VIDEO_RESOLUTION_MULTIPLIERS] ||
			1.0;
		let cost = baseCost * resolutionMultiplier;
		if (duration === 9) {
			cost *= 2;
		}
		// For 5s or any other duration, use base cost (no scaling except for 9s)
		return Math.max(1, Math.ceil(cost));
	}

	// For MiniMax V1: base cost is for 6 seconds
	if (modelName === VideoModelDisplayName.MiniMax_V1) {
		const durationMultiplier = duration / 6;
		baseCost *= durationMultiplier;
	} else {
		// For other models, apply duration scaling from 5-second base
		const durationMultiplier = duration / 5;
		baseCost *= durationMultiplier;
	}

	// Round up to nearest whole credit and ensure minimum of 1
	return Math.max(1, Math.ceil(baseCost));
}
