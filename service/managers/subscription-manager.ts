import type { SupabaseClient } from '@supabase/supabase-js';
import {
	generateSubscriptionPaymentId,
	generateUserSubscriptionId,
	generateSubscriptionCycleId,
} from '@/types/payment';
import * as PaymentManager from './payment-manager';
import * as CreemHelper from '../helpers/creem-payment-helper';
import {
	hasActiveSubscription,
	createUserSubscription,
	updateUserSubscription as updateUserSubscriptionDB,
	getByProviderSubscriptionId,
	UserSubscriptionUpdate,
} from '../db/user-subscription';
import { getLatestProcessingPayment } from '../db/subscription-payment';
import {
	createSubscriptionCycle,
	getCurrentSubscriptionCycleForPlan,
	updateCycleUsage,
} from '../db/subscription-cycle';
import { getSubscriptionPlan } from '../db/subscription-plan';
import type {
	SubscriptionPaymentInitiateResponse,
	SubscriptionPayment,
	UserSubscription,
	CreemWebhookSubscription,
} from '@/types/payment';
import { createSupabaseServerClient } from '@/utils/supabase/server';

// Re-export utility functions
export { getSubscriptionPlan, getSubscriptionPlanByCreemProductId } from '../db/subscription-plan';

/**
 * FUNCTIONAL SUBSCRIPTION MANAGER
 * Functions that orchestrate subscription workflows
 */

// Generic result type for subscription operations - consistent with PaymentManager
type SubscriptionResult<T> = {
	data: T | null;
	error: string | null;
};

export async function initiateSubscriptionPayment(
	supabase: SupabaseClient,
	userId: string,
	userEmail: string,
	userFullName: string,
	subscriptionPlanId: string
): Promise<SubscriptionPaymentInitiateResponse> {
	const transactionId = generateSubscriptionPaymentId();

	try {
		// 1. Get and validate subscription plan
		const { data: subscriptionPlan, error: planError } =
			getSubscriptionPlan(subscriptionPlanId);
		if (planError || !subscriptionPlan) {
			return {
				success: false,
				transaction_id: transactionId,
				error: planError || 'Invalid subscription plan',
			};
		}

		// 2. Check for duplicate active subscription
		// const hasActiveSub = await hasActiveSubscription(supabase, userId, subscriptionPlanId);
		// if (hasActiveSub) {
		// 	return {
		// 		success: false,
		// 		transaction_id: transactionId,
		// 		error: 'User already has an active subscription for this plan',
		// 	};
		// }

		// 3. Check for existing pending payment (idempotency)
		// const existingPayment = await PaymentManager.getLatestPendingPayment(supabase, userId, subscriptionPlanId);
		// if (existingPayment && existingPayment.provider_payment_link) {
		//   return {
		//     success: true,
		//     transaction_id: existingPayment.id,
		//     payment_link: existingPayment.provider_payment_link,
		//   };
		// }

		// 2. Create checkout session with Creem
		const creemCheckout = await CreemHelper.createCheckoutSession(
			userId,
			userEmail,
			userFullName,
			subscriptionPlan.creem_product_id,
			subscriptionPlanId,
			transactionId
		);

		// 4. Create initial payment record
		const { data: payment, error: paymentError } = await PaymentManager.createInitialPayment(
			supabase,
			userId,
			subscriptionPlan.id,
			subscriptionPlan.price_minor,
			subscriptionPlan.currency,
			'creem',
			subscriptionPlan.creem_product_id,
			transactionId,
			creemCheckout.checkoutUrl
		);

		if (paymentError) {
			return {
				success: false,
				transaction_id: transactionId,
				error: paymentError,
			};
		}

		// 5. Return success response
		return {
			success: true,
			transaction_id: transactionId,
			payment_link: creemCheckout.checkoutUrl,
		};
	} catch (error) {
		console.error('Subscription initiation failed:', error);

		return {
			success: false,
			transaction_id: transactionId,
			error: error instanceof Error ? error.message : 'Internal server error',
		};
	}
}

/**
 * Create subscription from payment - reusable for webhooks, return URL verification, and manual processing
 */
export async function createSubscriptionFromPayment(
	supabase: SupabaseClient,
	payment: SubscriptionPayment,
	providerSubscription: CreemWebhookSubscription,
	webhookEventId?: string
): Promise<{ success: boolean; message: string; error?: string; subscriptionId?: string }> {
	try {
		// Check if user already has an active subscription for this plan
		const hasActiveSub = await hasActiveSubscription(
			supabase,
			payment.user_id,
			payment.subscription_plan_id
		);
		if (hasActiveSub) {
			return {
				success: true,
				message: `User ${payment.user_id} already has an active subscription for plan ${payment.subscription_plan_id}`,
			};
		}

		// Create new subscription
		const subscriptionId = generateUserSubscriptionId();
		const now = new Date().toISOString();
		const nextMonth = new Date();
		nextMonth.setMonth(nextMonth.getMonth() + 1);

		const { data: newSubscription, error: subscriptionError } = await createUserSubscription(
			supabase,
			{
				id: subscriptionId,
				user_id: payment.user_id,
				subscription_plan_id: payment.subscription_plan_id,
				status: 'active',
				start_at: providerSubscription.created_at,
				end_at: providerSubscription.current_period_end_date || nextMonth.toISOString(),
				next_billing_at:
					providerSubscription.next_transaction_date || nextMonth.toISOString(),
				last_subscription_payment_id: payment.id,
				provider_subscription_id: providerSubscription.id,
				created_by_webhook_event_id: webhookEventId,
				last_modified_by_webhook_event_id: webhookEventId,
				created_at: providerSubscription.created_at,
				updated_at: now,
			}
		);

		if (subscriptionError || !newSubscription) {
			return {
				success: false,
				message: 'Failed to create subscription',
				error: subscriptionError?.message || 'Unknown error',
			};
		}

		// Create initial subscription cycle
		await createInitialSubscriptionCycle(supabase, newSubscription);

		// Update user metadata for pro subscription
		const supabaseAdmin = await createSupabaseServerClient(true);
		await supabaseAdmin.auth.admin.updateUserById(payment.user_id, {
			user_metadata: {
				has_free_subscription: true,
				is_pro_user: true,
				pro_subscription_started_at: now,
				pro_subscription_expires_at: newSubscription.end_at,
				subscription_plan_id: newSubscription.subscription_plan_id,
				subscription_status: 'active',
				last_subscription_update: now,
			},
		});

		return {
			success: true,
			message: `Subscription created successfully: ${subscriptionId}`,
			subscriptionId,
		};
	} catch (error) {
		return {
			success: false,
			message: 'Failed to create subscription from payment',
			error: error instanceof Error ? error.message : 'Unknown error',
		};
	}
}

/**
 * Create initial subscription cycle
 */
async function createInitialSubscriptionCycle(
	supabase: SupabaseClient,
	subscription: UserSubscription
): Promise<void> {
	const cycleId = generateSubscriptionCycleId();
	const now = new Date().toISOString();
	//internal plan
	const { data: zecoSubscriptionPlan, error: planError } = getSubscriptionPlan(
		subscription.subscription_plan_id
	);
	if (planError || !zecoSubscriptionPlan) {
		throw new Error(`Failed to get subscription plan: ${planError}`);
	}

	const { error } = await createSubscriptionCycle(supabase, {
		id: cycleId,
		user_subscription_id: subscription.id,
		user_id: subscription.user_id,
		subscription_payment_id: subscription.last_subscription_payment_id,
		cycle_index: 0,
		cycle_start_at: subscription.start_at,
		cycle_end_at: subscription.end_at,
		tier1_tokens_granted: zecoSubscriptionPlan.tier1_token_limit,
		tier2_tokens_granted: zecoSubscriptionPlan.tier2_token_limit,
		credits_granted: zecoSubscriptionPlan.cycle_credits,
		tier1_tokens_used: 0,
		tier2_tokens_used: 0,
		credits_used: 0,
		is_current: true,
		created_at: now,
		updated_at: now,
	});

	if (error) {
		throw new Error(`Failed to create initial cycle: ${error.message}`);
	}

	// Update subscription with cycle reference
	await updateUserSubscription(supabase, subscription.id, {
		current_subscription_cycle_id: cycleId,
		updated_at: now,
	});
}

/**
 * Get user subscription by provider subscription id
 * @param supabase
 * @param userId
 * @param providerSubscriptionId
 * @returns
 */
export async function getUserSubscriptionByProviderSubscriptionId(
	supabase: SupabaseClient,
	userId: string,
	providerSubscriptionId: string
): Promise<SubscriptionResult<UserSubscription>> {
	const { data, error } = await getByProviderSubscriptionId(
		supabase,
		userId,
		providerSubscriptionId
	);
	if (error) {
		return {
			data: null,
			error: `Failed to get user subscription by provider subscription id: ${error.message}`,
		};
	}
	return { data, error: null };
}

export async function updateUserSubscription(
	supabase: SupabaseClient,
	subscriptionId: string,
	subscription: UserSubscriptionUpdate
): Promise<SubscriptionResult<UserSubscription>> {
	const { data, error } = await updateUserSubscriptionDB(supabase, subscriptionId, subscription);
	if (error) {
		return {
			data: null,
			error: `Failed to update user subscription: ${error.message}`,
		};
	}
	return { data, error: null };
}

export async function getUserSubscriptionStatus(
	supabase: SupabaseClient,
	userId: string
): Promise<
	SubscriptionResult<{
		isPro: boolean;
		status: 'free' | 'pro' | 'processing' | 'need_support';
		hasRecentProcessingPayment: boolean;
	}>
> {
	try {
		// Use DB layer function to check if user is Pro
		const isPro = await hasActiveSubscription(supabase, userId, 'zeco-pro-monthly-plan');

		// If user is Pro, return early - no need to check processing payments
		if (isPro) {
			return {
				data: {
					isPro: true,
					status: 'pro',
					hasRecentProcessingPayment: false,
				},
				error: null,
			};
		}

		// Use DB layer function to get latest processing payment (any age)
		const { data: processingPayment, error: paymentError } = await getLatestProcessingPayment(
			supabase,
			userId
		);

		if (paymentError) {
			return {
				data: null,
				error: `Failed to check processing payments: ${paymentError.message}`,
			};
		}

		let status: 'free' | 'pro' | 'processing' | 'need_support' = 'free';
		let hasRecentProcessingPayment = false;

		if (processingPayment) {
			const createdAt = new Date(processingPayment.created_at).getTime();
			const now = Date.now();
			const fifteenMinutes = 15 * 60 * 1000;
			if (now - createdAt > fifteenMinutes) {
				status = 'need_support';
			} else {
				status = 'processing';
				hasRecentProcessingPayment = true;
			}
		}

		return {
			data: {
				isPro: false,
				status,
				hasRecentProcessingPayment,
			},
			error: null,
		};
	} catch (error) {
		console.error('Error getting user subscription status:', error);
		return {
			data: null,
			error: error instanceof Error ? error.message : 'Unknown error',
		};
	}
}

/**
 * Check if user has access to a specific model tier and type
 */
export async function checkUserModelAccess(
	supabase: SupabaseClient,
	userId: string,
	modelTier: 1 | 2,
	type: 'token' | 'credit'
): Promise<{
	hasAccess: boolean;
	error?: string;
	remainingUsage?: {
		tier1Tokens?: number;
		tier2Tokens?: number;
		credits?: number;
	};
}> {
	try {
		// Single check for pro subscription status
		const isProUser = await hasActiveSubscription(supabase, userId, 'zeco-pro-monthly-plan');

		// Validate tier 2 access early
		if (modelTier === 2 && !isProUser) {
			return {
				hasAccess: false,
				error: 'Tier 2 models require a Pro subscription. Please upgrade to access advanced models.',
			};
		}

		// Determine plan ID based on user's subscription status
		const planId = isProUser ? 'zeco-pro-monthly-plan' : 'zeco-free-plan';

		// Get current subscription cycle for the determined plan
		const { data: cycle, error: cycleError } = await getCurrentSubscriptionCycleForPlan(
			supabase,
			userId,
			planId
		);

		if (cycleError || !cycle) {
			return {
				hasAccess: false,
				error: 'No active subscription cycle found for your current plan',
			};
		}

		// Helper function to calculate remaining usage
		const getRemainingUsage = () => ({
			tier1Tokens: cycle.tier1_tokens_granted - cycle.tier1_tokens_used,
			tier2Tokens: cycle.tier2_tokens_granted - cycle.tier2_tokens_used,
			credits: cycle.credits_granted - cycle.credits_used,
		});

		// Check availability based on type and tier
		let remaining: number;
		let errorMessage: string;

		if (type === 'token') {
			const usedField = modelTier === 1 ? 'tier1_tokens_used' : 'tier2_tokens_used';
			const grantedField = modelTier === 1 ? 'tier1_tokens_granted' : 'tier2_tokens_granted';
			remaining = cycle[grantedField] - cycle[usedField];
			errorMessage = `You have exceeded your Tier ${modelTier} token limit`;
		} else if (type === 'credit') {
			remaining = cycle.credits_granted - cycle.credits_used;
			errorMessage = 'You have exceeded your credit limit';
		} else {
			return {
				hasAccess: false,
				error: 'Invalid usage type',
			};
		}

		const remainingUsage = getRemainingUsage();

		return {
			hasAccess: remaining > 0,
			error: remaining <= 0 ? errorMessage : undefined,
			remainingUsage,
		};
	} catch (error) {
		console.error('Error checking model access:', error);
		return {
			hasAccess: false,
			error: error instanceof Error ? error.message : 'Unknown error',
		};
	}
}

/**
 * Update user usage for tokens or credits
 */
export async function updateUserUsage(
	supabase: SupabaseClient,
	userId: string,
	usageType: 'tier1_tokens' | 'tier2_tokens' | 'credits',
	amount: number
): Promise<{
	success: boolean;
	error?: string;
	updatedUsage?: {
		tier1TokensUsed: number;
		tier2TokensUsed: number;
		creditsUsed: number;
	};
}> {
	try {
		// Determine user's current plan (prioritize pro if they have it)
		const isProUser = await hasActiveSubscription(supabase, userId, 'zeco-pro-monthly-plan');
		const planId = isProUser ? 'zeco-pro-monthly-plan' : 'zeco-free-plan';

		// Get current subscription cycle for the user's plan
		const { data: cycle, error: cycleError } = await getCurrentSubscriptionCycleForPlan(
			supabase,
			userId,
			planId
		);

		if (cycleError || !cycle) {
			return {
				success: false,
				error: 'No active subscription cycle found for your current plan',
			};
		}

		// Update the usage
		const updateField = `${usageType}_used` as
			| 'tier1_tokens_used'
			| 'tier2_tokens_used'
			| 'credits_used';
		const newUsageValue = cycle[updateField] + amount;

		const { error: updateError } = await updateCycleUsage(supabase, cycle.id, {
			[updateField]: newUsageValue,
		});

		if (updateError) {
			console.log('updateError', updateError);
			return {
				success: false,
				error: updateError,
			};
		}

		// Return updated usage amounts
		const updatedUsage = {
			tier1TokensUsed: usageType === 'tier1_tokens' ? newUsageValue : cycle.tier1_tokens_used,
			tier2TokensUsed: usageType === 'tier2_tokens' ? newUsageValue : cycle.tier2_tokens_used,
			creditsUsed: usageType === 'credits' ? newUsageValue : cycle.credits_used,
		};

		return {
			success: true,
			updatedUsage,
		};
	} catch (error) {
		console.error('Error updating usage:', error);
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Unknown error',
		};
	}
}

/**
 * Handle subscription expiry - update user metadata to remove pro access
 */
export async function handleSubscriptionExpiry(
	supabaseAdmin: SupabaseClient,
	userId: string
): Promise<{
	success: boolean;
	error?: string;
}> {
	try {
		const now = new Date().toISOString();
		await supabaseAdmin.auth.admin.updateUserById(userId, {
			user_metadata: {
				has_free_subscription: true,
				is_pro_user: false,
				subscription_plan_id: 'zeco-free-plan',
				subscription_status: 'expired',
				last_subscription_update: now,
			},
		});

		console.log(`Subscription expired for user ${userId}, reverted to free plan`);

		return {
			success: true,
		};
	} catch (error) {
		console.error('Failed to handle subscription expiry:', error);
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Unknown error',
		};
	}
}
