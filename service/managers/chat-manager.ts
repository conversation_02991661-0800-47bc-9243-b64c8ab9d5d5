import { redirect } from 'next/navigation';
import { SupabaseClient } from '@supabase/supabase-js';
import {
	checkIfChatBelongsToUser as checkIfChatBelongsToUserQuery,
	getPublicAccessTimeForChat as getPublicAccessTimeForChatQuery,
} from '../db/chat-history';
import { getChatMessagesByChatId } from '../db/chat-message';
import { ChatType, ChatMessage } from '@/types/chat';
import { createSupabaseServerClient } from '@/utils/supabase/server';

async function checkIfChatBelongsToUser(
	supabase: SupabaseClient,
	type: ChatType,
	userId: string,
	chatId: string
) {
	const { data: belongsToUser, error } = await checkIfChatBelongsToUserQuery(
		supabase,
		userId,
		chatId
	);

	if (error) {
		console.error('Error checking chat ownership:', error);
		redirect(`/${type}`);
	}

	if (!belongsToUser) {
		console.warn(`Attempt to access chat ${chatId} by user ${userId} denied.`);
		redirect(`/${type}`);
	}
}

export async function getChatMessagesFromChatId(
	type: ChatType,
	chatId: string,
	isSharedChat: boolean = false
): Promise<ChatMessage[]> {
	const supabase = await createSupabaseServerClient();
	const {
		data: { user },
	} = await supabase.auth.getUser();

	if (!user) {
		redirect(`/login?next=/${type}/${chatId}`);
	}

	let fetchedMessages;
	if (isSharedChat) {
		let lastSharedAtTime;
		try {
			lastSharedAtTime = await getPublicAccessTimeForChat(supabase, chatId);
		} catch (error) {
			redirect(`/${type}`);
		}

		if (!lastSharedAtTime) {
			redirect(`/${type}`);
		}

		const { data, error } = await getChatMessagesByChatId(supabase, chatId, lastSharedAtTime);

		if (error || !data) {
			console.error('Error fetching shared chat messages:', error);
			throw new Error('Failed to fetch shared chat messages');
		}

		fetchedMessages = data;
	} else {
		await checkIfChatBelongsToUser(supabase, type, user.id, chatId);

		const { data, error } = await getChatMessagesByChatId(supabase, chatId);

		if (error || !data) {
			console.error('Error fetching chat messages:', error);
			throw new Error('Failed to fetch chat messages');
		}

		fetchedMessages = data;
	}

	return fetchedMessages || [];
}

export async function getPublicAccessTimeForChat(supabase: SupabaseClient, chatId: string) {
	const { data: lastSharedAt, error } = await getPublicAccessTimeForChatQuery(supabase, chatId);
	if (error) {
		console.error('Error fetching chat history for shared chat:', error);
		throw error;
	}
	return lastSharedAt;
}
