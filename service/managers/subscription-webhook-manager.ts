import type { SupabaseClient } from '@supabase/supabase-js';
import type {
	CreemWebhookPayload,
	CreemWebhookSubscription,
	CreemWebhookCheckout,
	SubscriptionPayment,
	CurrencyCode,
	CreemProviderMetadata,
} from '@/types/payment';
import { generateWebhookEventId } from '@/types/payment';
import * as SubscriptionManager from './subscription-manager';
import {
	createWebhookEvent,
	isWebhookEventAlreadyProcessed,
	markWebhookEventAsProcessed,
	markWebhookEventAsFailed,
} from '../db/webhook-event';

import {
	createSubscriptionCycle,
	getCurrentSubscriptionCycle,
	updateSubscriptionCycle,
} from '@/service/db/subscription-cycle';
import { generateSubscriptionPaymentId, generateSubscriptionCycleId } from '@/types/payment';
import { getPaymentById, markPaymentAsSuccess, createPayment } from './payment-manager';
import { getSubscriptionPlan, SubscriptionPlanWithProvider } from '../db/subscription-plan';
import { createSupabaseServerClient } from '@/utils/supabase/server';

export interface WebhookProcessingResult {
	success: boolean;
	message: string;
	error?: string;
}

export async function processSubscriptionWebhook(
	supabase: SupabaseClient,
	payload: CreemWebhookPayload
): Promise<WebhookProcessingResult> {
	const { eventType, id: webhookId } = payload;

	// 1. Check if webhook already processed
	const isProcessed = await isWebhookEventAlreadyProcessed(supabase, webhookId, 'creem');
	if (isProcessed) {
		return {
			success: true,
			message: `Webhook ${webhookId} already processed`,
		};
	}

	// 2. Create webhook event record for tracking
	const webhookEventId = generateWebhookEventId();
	const now = new Date().toISOString();

	const { data: webhookEvent, error: createError } = await createWebhookEvent(supabase, {
		id: webhookEventId,
		webhook_id: webhookId,
		event_type: eventType,
		provider: 'creem',
		status: 'pending',
		payload: payload as Record<string, any>,
		retry_count: 0,
		created_at: now,
		updated_at: now,
	});

	if (createError || !webhookEvent) {
		console.error('Failed to create webhook event record:', createError);
		return {
			success: false,
			message: 'Failed to track webhook event',
			error: createError?.message || 'Unknown error',
		};
	}

	// 3. Process the webhook
	try {
		let result: WebhookProcessingResult;

		switch (payload.eventType) {
			case 'checkout.completed':
				result = await handleCheckoutCompleted(supabase, payload.object);
				break;
			case 'subscription.paid':
				result = await handleSubscriptionPaid(supabase, payload.object, webhookEventId);
				break;
			case 'subscription.canceled':
				result = await handleSubscriptionCanceled(supabase, payload.object, webhookEventId);
				break;
			case 'subscription.expired':
				result = await handleSubscriptionExpired(supabase, payload.object, webhookEventId);
				break;
			case 'subscription.active':
				result = await handleSubscriptionActive(supabase, payload.object);
				break;
			default:
				// This case should not be reachable if all event types are handled
				// It's a type guard for future development
				const unhandledEventType: never = payload;
				console.warn(`Unsupported event type: ${unhandledEventType}`);
				result = {
					success: false,
					message: `Unsupported event type`,
				};
		}

		// 4. Update webhook event status based on result
		if (result.success) {
			await markWebhookEventAsProcessed(supabase, webhookEventId);
		} else {
			await markWebhookEventAsFailed(
				supabase,
				webhookEventId,
				result.error || result.message
			);
		}

		return result;
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : 'Unknown error';

		console.error('Webhook processing failed:', {
			error: errorMessage,
			webhookId,
			eventType,
		});

		// Mark webhook event as failed
		await markWebhookEventAsFailed(supabase, webhookEventId, errorMessage);

		return {
			success: false,
			message: 'Webhook processing failed',
			error: errorMessage,
		};
	}
}

async function handleCheckoutCompleted(
	supabase: SupabaseClient,
	checkout: CreemWebhookCheckout
): Promise<WebhookProcessingResult> {
	// One-time payments are not handled in this manager
	if (checkout.product.billing_type === 'one-time') {
		return {
			success: true,
			message: `Ignoring one-time payment for now: ${checkout.id}`,
		};
	}

	// This is the first payment for a new subscription.
	// The 'subscription.paid' event will handle the actual creation/renewal.
	// We can log this event for tracking purposes.
	console.log(`Checkout completed for new subscription: ${checkout.subscription.id}`);

	return {
		success: true,
		message: `Checkout for subscription ${checkout.subscription.id} tracked.`,
	};
}

async function handleSubscriptionPaid(
	supabase: SupabaseClient,
	creemSubscription: CreemWebhookSubscription,
	webhookEventId: string
): Promise<WebhookProcessingResult> {
	const { id: providerSubscriptionId, metadata } = creemSubscription;

	const userId = metadata.user_id;
	const subscriptionPlanId = metadata.subscription_plan_id;

	const { data: subscriptionPlan, error: planError } = getSubscriptionPlan(subscriptionPlanId);
	if (planError || !subscriptionPlan) {
		return {
			success: false,
			message: 'Invalid subscription plan',
			error: planError || 'Subscription plan not found',
		};
	}

	const { data: existingSubscription, error: subscriptionError } =
		await SubscriptionManager.getUserSubscriptionByProviderSubscriptionId(
			supabase,
			userId,
			providerSubscriptionId
		);

	if (subscriptionError) {
		return {
			success: false,
			message: 'Failed to check existing subscription',
			error: subscriptionError,
		};
	}

	if (existingSubscription) {
		// Handle a renewal payment for an existing subscription
		return await handleSubscriptionRenewal(
			supabase,
			creemSubscription,
			subscriptionPlan,
			webhookEventId
		);
	} else {
		return await handleNewSubscription(supabase, creemSubscription, webhookEventId);
	}
}

async function handleSubscriptionCanceled(
	supabase: SupabaseClient,
	subscription: CreemWebhookSubscription,
	webhookEventId: string
): Promise<WebhookProcessingResult> {
	const { id: providerSubscriptionId, metadata } = subscription;
	const { data: existingSubscription, error: subscriptionError } =
		await SubscriptionManager.getUserSubscriptionByProviderSubscriptionId(
			supabase,
			metadata.user_id,
			providerSubscriptionId
		);

	if (subscriptionError || !existingSubscription) {
		return {
			success: false,
			message: 'Subscription not found to cancel',
			error: `Failed to find subscription with provider_id: ${subscription.id} - ${subscriptionError}`,
		};
	}

	const { data: updatedSubscription, error: updateError } =
		await SubscriptionManager.updateUserSubscription(supabase, existingSubscription.id, {
			status: 'cancelled',
			end_at: subscription.canceled_at || new Date().toISOString(),
			last_modified_by_webhook_event_id: webhookEventId,
			updated_at: new Date().toISOString(),
		});

	if (updateError || !updatedSubscription) {
		return {
			success: false,
			message: 'Failed to update subscription status to canceled',
			error: updateError || 'Unknown error',
		};
	}

	return {
		success: true,
		message: `Subscription ${existingSubscription.id} has been marked as canceled.`,
	};
}

async function handleSubscriptionExpired(
	supabase: SupabaseClient,
	subscription: CreemWebhookSubscription,
	webhookEventId: string
): Promise<WebhookProcessingResult> {
	const { id: providerSubscriptionId, metadata } = subscription;
	const { data: existingSubscription, error: subscriptionError } =
		await SubscriptionManager.getUserSubscriptionByProviderSubscriptionId(
			supabase,
			metadata.user_id,
			providerSubscriptionId
		);

	if (subscriptionError || !existingSubscription) {
		return {
			success: false,
			message: 'Subscription not found to expire',
			error: `Failed to find subscription with provider_id: ${subscription.id} - ${subscriptionError}`,
		};
	}

	const { data: updatedSubscription, error: updateError } =
		await SubscriptionManager.updateUserSubscription(supabase, existingSubscription.id, {
			status: 'expired',
			end_at: subscription.current_period_end_date,
			last_modified_by_webhook_event_id: webhookEventId,
			updated_at: new Date().toISOString(),
		});

	if (updateError || !updatedSubscription) {
		return {
			success: false,
			message: 'Failed to update subscription status to expired',
			error: updateError || 'Unknown error',
		};
	}

	const supabaseAdmin = await createSupabaseServerClient(true);

	// Update user metadata to remove pro access
	const metadataResult = await SubscriptionManager.handleSubscriptionExpiry(
		supabaseAdmin,
		metadata.user_id
	);
	if (!metadataResult.success) {
		console.error('Failed to update user metadata on expiry:', metadataResult.error);
		// Don't fail the webhook - database was updated successfully
	}

	return {
		success: true,
		message: `Subscription ${existingSubscription.id} has been marked as expired.`,
	};
}

async function handleSubscriptionActive(
	supabase: SupabaseClient,
	subscription: CreemWebhookSubscription
): Promise<WebhookProcessingResult> {
	return {
		success: true,
		message: `Subscription ${subscription.id} has been marked as active.`,
	};
}

async function handleNewSubscription(
	supabase: SupabaseClient,
	creemSubscription: CreemWebhookSubscription,
	webhookEventId: string
): Promise<WebhookProcessingResult> {
	const { data: subscriptionPayment, error: paymentError } = await getPaymentById(
		supabase,
		creemSubscription.metadata.subscription_payment_id
	);

	if (paymentError || !subscriptionPayment) {
		return {
			success: false,
			message: 'Subscription payment not found',
			error: `Failed to find subscription payment with id: ${creemSubscription.metadata.subscription_payment_id} - ${paymentError}`,
		};
	}

	// mark it as success and update the subscription payment
	const { error: successError } = await markPaymentAsSuccess(
		supabase,
		subscriptionPayment,
		creemSubscription.collection_method,
		webhookEventId,
		creemSubscription.id,
		creemSubscription.last_transaction_id,
		creemSubscription.customer.id,
		creemSubscription.product.id
	);

	if (successError) {
		return {
			success: false,
			message: 'Failed to mark payment as success',
			error: successError,
		};
	}

	const result = await SubscriptionManager.createSubscriptionFromPayment(
		supabase,
		subscriptionPayment,
		creemSubscription,
		webhookEventId
	);

	if (result.error) {
		return {
			success: false,
			message: 'Failed to create new subscription',
			error: result.error,
		};
	}

	return {
		success: true,
		message: `Successfully created new subscription ${result.subscriptionId}`,
	};
}

async function handleSubscriptionRenewal(
	supabase: SupabaseClient,
	subscription: CreemWebhookSubscription,
	subscriptionPlan: SubscriptionPlanWithProvider,
	webhookEventId: string
): Promise<WebhookProcessingResult> {
	console.log(`Handling renewal for subscription: ${subscription.id}`);

	try {
		// Get the existing subscription to update
		const { data: existingSubscription, error: subscriptionError } =
			await SubscriptionManager.getUserSubscriptionByProviderSubscriptionId(
				supabase,
				subscription.metadata.user_id,
				subscription.id
			);

		if (subscriptionError || !existingSubscription) {
			return {
				success: false,
				message: 'Subscription not found for renewal',
				error: `Failed to find subscription with provider_id: ${subscription.id} - ${subscriptionError}`,
			};
		}

		// 1. Create a new payment record for the renewal
		const providerMetadata: CreemProviderMetadata = {
			provider: 'creem',
			customer_id: subscription.customer.id,
			subscription_id: subscription.id,
			product_id: subscription.product.id,
			transaction_id: subscription.last_transaction_id,
		};

		const paymentData: SubscriptionPayment = {
			id: generateSubscriptionPaymentId(),
			user_id: existingSubscription.user_id,
			subscription_plan_id: subscriptionPlan.id,
			provider: 'creem',
			provider_metadata: providerMetadata,
			webhook_event_id: webhookEventId, // FK to our internal WebhookEvent record
			amount_minor: subscription.product.price,
			currency: subscription.product.currency as CurrencyCode,
			status: 'success',
			initiated_at: subscription.last_transaction_date,
			completed_at: subscription.last_transaction_date,
			created_at: new Date().toISOString(),
			updated_at: new Date().toISOString(),
		};

		const { data: newPayment, error: createError } = await createPayment(supabase, paymentData);

		if (createError || !newPayment) {
			return {
				success: false,
				message: 'Failed to create renewal payment record',
				error: createError || 'Unknown payment creation error',
			};
		}

		// 2. Update the existing subscription with renewal information
		const renewalTimestamp = new Date().toISOString();
		const renewalData = {
			status: 'active' as const,
			end_at: subscription.current_period_end_date,
			next_billing_at:
				subscription.next_transaction_date || subscription.current_period_end_date,
			last_subscription_payment_id: newPayment.id,
			last_modified_by_webhook_event_id: webhookEventId,
			updated_at: renewalTimestamp,
		};

		const { data: updatedSubscription, error: updateError } =
			await SubscriptionManager.updateUserSubscription(
				supabase,
				existingSubscription.id,
				renewalData
			);

		if (updateError || !updatedSubscription) {
			return {
				success: false,
				message: 'Failed to update subscription with renewal information',
				error: updateError || 'Unknown error',
			};
		}

		// 3. Create the next subscription cycle for the renewal
		const { data: currentCycle, error: currentCycleError } = await getCurrentSubscriptionCycle(
			supabase,
			existingSubscription.id
		);

		if (currentCycleError) {
			console.warn(
				'Could not fetch current cycle, will create cycle with index 0:',
				currentCycleError
			);
		}

		const nextCycleIndex = currentCycle ? currentCycle.cycle_index + 1 : 0;
		const cycleId = generateSubscriptionCycleId();
		const cycleTimestamp = new Date().toISOString();

		// Calculate cycle dates
		const cycleStartDate = subscription.current_period_start_date || cycleTimestamp;
		const cycleEndDate = subscription.current_period_end_date || cycleTimestamp;

		const newCycleData = {
			id: cycleId,
			user_subscription_id: existingSubscription.id,
			user_id: existingSubscription.user_id,
			subscription_payment_id: newPayment.id,
			cycle_index: nextCycleIndex,
			cycle_start_at: cycleStartDate,
			cycle_end_at: cycleEndDate,
			tier1_tokens_granted: subscriptionPlan.tier1_token_limit,
			tier2_tokens_granted: subscriptionPlan.tier2_token_limit,
			credits_granted: subscriptionPlan.cycle_credits,
			tier1_tokens_used: 0,
			tier2_tokens_used: 0,
			credits_used: 0,
			is_current: true,
			created_at: cycleTimestamp,
			updated_at: cycleTimestamp,
		};

		// Mark previous cycle as not current (if exists)
		if (currentCycle) {
			await updateSubscriptionCycle(supabase, currentCycle.id, {
				is_current: false,
				updated_at: cycleTimestamp,
			});
		}

		const { data: newCycle, error: cycleError } = await createSubscriptionCycle(
			supabase,
			newCycleData
		);

		if (cycleError || !newCycle) {
			return {
				success: false,
				message: 'Failed to create subscription cycle for renewal',
				error: cycleError?.message || 'Unknown cycle creation error',
			};
		}

		return {
			success: true,
			message: `Subscription renewal for ${subscription.id} processed successfully. Payment: ${newPayment.id}, Cycle: ${newCycle.id}`,
		};
	} catch (error) {
		console.error('Renewal processing error:', error);
		return {
			success: false,
			message: 'Renewal processing failed',
			error: error instanceof Error ? error.message : 'Unknown error',
		};
	}
}
