import type { SupabaseClient } from '@supabase/supabase-js';
import type { Provider, SubscriptionPayment, CreemProviderMetadata } from '@/types/payment';
import {
	createSubscriptionPayment,
	updateSubscriptionPayment,
	getSubscriptionPaymentById,
	type SubscriptionPaymentInsert,
	type SubscriptionPaymentUpdate,
	updateSubscriptionPaymentStatusToProcessing,
} from '../db/subscription-payment';

/**
 * FUNCTIONAL PAYMENT MANAGER
 * Simple functions for payment operations
 */

// Result type for manager layer
type PaymentResult<T> = {
	data: T | null;
	error: string | null;
};

export async function createPayment(
	supabase: SupabaseClient,
	data: SubscriptionPaymentInsert
): Promise<PaymentResult<SubscriptionPayment>> {
	const { data: payment, error } = await createSubscriptionPayment(supabase, data);

	if (error || !payment) {
		return {
			data: null,
			error: `Failed to create payment: ${error?.message || 'No data returned'}`,
		};
	}

	return { data: payment, error: null };
}

export async function updatePaymentStatus(
	supabase: SupabaseClient,
	paymentId: string,
	updateData: SubscriptionPaymentUpdate
): Promise<PaymentResult<SubscriptionPayment>> {
	const { data: payment, error } = await updateSubscriptionPayment(
		supabase,
		paymentId,
		updateData
	);

	if (error || !payment) {
		return {
			data: null,
			error: `Failed to update payment: ${error?.message || 'No data returned'}`,
		};
	}

	return { data: payment, error: null };
}

export async function getPaymentById(
	supabase: SupabaseClient,
	paymentId: string
): Promise<PaymentResult<SubscriptionPayment>> {
	const { data: payment, error } = await getSubscriptionPaymentById(supabase, paymentId);

	if (error) {
		return {
			data: null,
			error: `Failed to get payment: ${error.message}`,
		};
	}

	return { data: payment, error: null };
}

export async function markPaymentAsProcessing(
	supabase: SupabaseClient,
	paymentId: string,
	providerData: CreemProviderMetadata
): Promise<PaymentResult<SubscriptionPayment>> {
	const { data: payment, error } = await updateSubscriptionPaymentStatusToProcessing(
		supabase,
		paymentId,
		providerData
	);

	if (error || !payment) {
		return {
			data: null,
			error: `Failed to update payment: ${error?.message || 'No data returned'}`,
		};
	}

	return { data: payment, error: null };
}

export async function markPaymentAsSuccess(
	supabase: SupabaseClient,
	payment: SubscriptionPayment,
	paymentMethod: string,
	webhookEventId: string,
	providerSubscriptionId: string,
	providerTransactionId: string,
	providerCustomerId: string,
	providerProductId: string
): Promise<PaymentResult<SubscriptionPayment>> {
	// Create consolidated provider metadata
	const providerMetadata: CreemProviderMetadata = {
		provider: 'creem',
		customer_id: providerCustomerId,
		subscription_id: providerSubscriptionId,
		product_id: providerProductId,
		transaction_id: providerTransactionId,
		payment_link: payment.provider_metadata?.payment_link, // Preserve existing payment link if any
	};

	return updatePaymentStatus(supabase, payment.id, {
		status: 'success',
		completed_at: new Date().toISOString(),
		updated_at: new Date().toISOString(),
		provider_metadata: providerMetadata,
		payment_method: paymentMethod,
		webhook_event_id: webhookEventId,
	});
}

export async function markPaymentAsFailed(
	supabase: SupabaseClient,
	paymentId: string,
	errorMessage?: string
): Promise<PaymentResult<SubscriptionPayment>> {
	return updatePaymentStatus(supabase, paymentId, {
		status: 'failed',
		error: errorMessage,
		completed_at: new Date().toISOString(),
		updated_at: new Date().toISOString(),
	});
}

export async function createInitialPayment(
	supabase: SupabaseClient,
	userId: string,
	planId: string,
	amountMinor: number,
	currency: 'USD' | 'INR',
	provider: Provider,
	providerProductId: string,
	transactionId: string,
	providerPaymentLink: string
): Promise<PaymentResult<SubscriptionPayment>> {
	// Create consolidated provider metadata
	const providerMetadata: CreemProviderMetadata = {
		provider: 'creem',
		product_id: providerProductId,
		payment_link: providerPaymentLink,
	};

	const paymentData: SubscriptionPaymentInsert = {
		id: transactionId,
		user_id: userId,
		subscription_plan_id: planId,
		amount_minor: amountMinor,
		currency,
		status: 'pending',
		provider,
		provider_metadata: providerMetadata,
		initiated_at: new Date().toISOString(),
		created_at: new Date().toISOString(),
		updated_at: new Date().toISOString(),
	};

	return createPayment(supabase, paymentData);
}
