import { Creem } from 'creem';
import crypto from 'crypto';
import { CreemCheckoutSessionMetadata } from '@/types/payment';

const {
	CREEM_API_KEY,
	NEXT_PUBLIC_APP_URL: APP_URL,
	CREEM_TEST_API_KEY,
	CREEM_ENV,
	CREEM_WEBHOOK_SECRET,
	CREEM_TEST_WEBHOOK_SECRET,
} = process.env;

if (!CREEM_API_KEY || !APP_URL || !CREEM_TEST_API_KEY) {
	throw new Error(
		'Creem environment variables missing - check CREEM_API_KEY, CREEM_TEST_API_KEY and NEXT_PUBLIC_APP_URL'
	);
}

const isProd = CREEM_ENV === 'production';
const creemApiKey = isProd ? CREEM_API_KEY : CREEM_TEST_API_KEY;
// Initialize Creem SDK
const creem = new Creem({
	serverIdx: isProd ? 0 : 1, // 0: production, 1: test
});

// Creem API response types
export interface CreemCheckoutResponse {
	id: string;
	checkoutUrl: string;
	successUrl: string;
}

export interface CreemCustomerData {
	email: string;
	name: string;
	userId: string;
}

/**
 * FUNCTIONAL CREEM PAYMENT HELPER
 * Pure functions for Creem payment provider integrations
 */

/**
 * Create checkout session with Creem
 */
export async function createCheckoutSession(
	userId: string,
	userEmail: string,
	userName: string,
	productId: string,
	planId: string,
	requestId: string
): Promise<CreemCheckoutResponse> {
	try {
		const successUrl = `${APP_URL}/subscriptions/payments/${requestId}/success`;

		const checkoutSession = await creem.createCheckout({
			xApiKey: creemApiKey,
			createCheckoutRequest: {
				productId: productId,
				successUrl: successUrl,
				requestId: requestId,
				customer: {
					email: userEmail,
				},
				metadata: {
					user_email: userEmail,
					user_name: userName,
					user_id: userId,
					subscription_plan_id: planId,
					subscription_payment_id: requestId,
				} satisfies CreemCheckoutSessionMetadata,
			},
		});

		if (!checkoutSession.checkoutUrl || !checkoutSession.id) {
			throw new Error('Failed to create checkout session');
		}

		return {
			id: checkoutSession.id,
			checkoutUrl: checkoutSession.checkoutUrl,
			successUrl: checkoutSession.successUrl || successUrl,
		};
	} catch (error) {
		console.error('Creem checkout creation failed:', error);
		throw new Error(
			`Creem checkout creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
		);
	}
}

/**
 * CREEM SIGNATURE VERIFICATION
 * Functions for verifying Creem webhook and return URL signatures
 */

export interface CreemReturnParams {
	request_id: string | null;
	checkout_id?: string | null;
	order_id?: string | null;
	customer_id?: string | null;
	subscription_id?: string | null;
	product_id?: string | null;
}

/**
 * Generate Creem signature for verification
 */
export function generateCreemSignature(params: CreemReturnParams, apiKey: string): string {
	const data = Object.entries(params)
		.map(([key, value]) => `${key}=${value}`)
		.concat(`salt=${apiKey}`)
		.join('|');
	return crypto.createHash('sha256').update(data).digest('hex');
}

/**
 * Verify Creem signature for return URL verification
 */
export function verifyCreemReturnSignature(
	params: CreemReturnParams & { signature?: string }
): boolean {
	try {
		const { signature, ...otherParams } = params;

		if (!signature) {
			return false;
		}
		const expectedSignature = generateCreemSignature(otherParams, creemApiKey);

		return expectedSignature === signature;
	} catch (error) {
		console.error('Signature verification error:', error);
		return false;
	}
}

/**
 * Verify webhook signature using Creem's official method
 * Based on: https://docs.creem.io/learn/webhooks/verify-webhook-requests
 */
export function verifyCreemWebhookSignature(payload: string, receivedSignature: string): boolean {
	try {
		const creemWebhookSecret = isProd ? CREEM_WEBHOOK_SECRET : CREEM_TEST_WEBHOOK_SECRET;

		// Generate signature using Creem's official method
		const computedSignature = crypto
			.createHmac('sha256', creemWebhookSecret!)
			.update(payload)
			.digest('hex');

		// Compare signatures using secure comparison
		return receivedSignature === computedSignature;
	} catch (error) {
		console.error('Signature verification error:', error);
		return false;
	}
}

export async function generateCustomerPortalLink(customerId: string): Promise<string> {
	try {
		const customerPortalLogin = await creem.generateCustomerLinks({
			xApiKey: creemApiKey,
			createCustomerPortalLinkRequestEntity: {
				customerId: customerId,
			},
		});

		if (!customerPortalLogin.customerPortalLink) {
			throw new Error('Failed to generate customer portal link');
		}

		return customerPortalLogin.customerPortalLink;
	} catch (error) {
		console.error('Creem customer portal link generation failed:', error);
		throw new Error(
			`Creem customer portal link generation failed: ${
				error instanceof Error ? error.message : 'Unknown error'
			}`
		);
	}
}
