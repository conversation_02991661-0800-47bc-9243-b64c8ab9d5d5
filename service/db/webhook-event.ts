import type { SupabaseClient } from '@supabase/supabase-js';
import type { WebhookEvent, WebhookEventStatus, WebhookProvider } from '@/types/payment';

// Types derived from interface - single source of truth
export type WebhookEventInsert = WebhookEvent;
export type WebhookEventUpdate = Partial<Omit<WebhookEvent, 'id' | 'created_at'>> & {
	updated_at: string;
};

// DB Operations
export async function createWebhookEvent(
	supabase: SupabaseClient,
	data: WebhookEventInsert
): Promise<{ data: WebhookEvent | null; error: any }> {
	const { data: result, error } = await supabase
		.from('webhook_event')
		.insert(data)
		.select()
		.single();

	return { data: result, error };
}

export async function updateWebhookEvent(
	supabase: SupabaseClient,
	id: string,
	data: WebhookEventUpdate
): Promise<{ data: WebhookEvent | null; error: any }> {
	const { data: result, error } = await supabase
		.from('webhook_event')
		.update(data)
		.eq('id', id)
		.select()
		.single();

	return { data: result, error };
}

export async function getWebhookEventById(
	supabase: SupabaseClient,
	id: string
): Promise<{ data: WebhookEvent | null; error: any }> {
	const { data, error } = await supabase.from('webhook_event').select('*').eq('id', id).single();

	return { data, error };
}

export async function getWebhookEventByWebhookId(
	supabase: SupabaseClient,
	webhookId: string,
	provider: WebhookProvider = 'creem'
): Promise<{ data: WebhookEvent | null; error: any }> {
	const { data, error } = await supabase
		.from('webhook_event')
		.select('*')
		.eq('webhook_id', webhookId)
		.eq('provider', provider)
		.single();

	return { data, error };
}

export async function isWebhookEventAlreadyProcessed(
	supabase: SupabaseClient,
	webhookId: string,
	provider: WebhookProvider = 'creem'
): Promise<boolean> {
	const { data } = await supabase
		.from('webhook_event')
		.select('id')
		.eq('webhook_id', webhookId)
		.eq('provider', provider)
		.eq('status', 'processed')
		.single();

	return !!data;
}

export async function markWebhookEventAsProcessed(
	supabase: SupabaseClient,
	id: string
): Promise<{ data: WebhookEvent | null; error: any }> {
	return updateWebhookEvent(supabase, id, {
		status: 'processed',
		processed_at: new Date().toISOString(),
		updated_at: new Date().toISOString(),
	});
}

export async function markWebhookEventAsFailed(
	supabase: SupabaseClient,
	id: string,
	errorMessage: string,
	incrementRetry: boolean = true
): Promise<{ data: WebhookEvent | null; error: any }> {
	// First get current retry count if we need to increment
	let retryCount = 0;
	if (incrementRetry) {
		const { data: currentEvent } = await getWebhookEventById(supabase, id);
		retryCount = (currentEvent?.retry_count || 0) + 1;
	}

	return updateWebhookEvent(supabase, id, {
		status: 'failed',
		error_message: errorMessage,
		retry_count: retryCount,
		updated_at: new Date().toISOString(),
	});
}
