import type { SupabaseClient } from '@supabase/supabase-js';
import type { SubscriptionPlan, BillingPeriod, CurrencyCode } from '@/types/payment';

/* ────────────────────────────────────────────────────────────────────
   CONFIGURATION-BASED SUBSCRIPTION PLANS (Current Implementation)
   Single source of truth for all subscription plans
   ───────────────────────────────────────────────────────────────── */

const isProd = process.env.CREEM_ENV === 'production';

export interface SubscriptionPlanWithProvider extends SubscriptionPlan {
	creem_product_id: string;
}

export const SUBSCRIPTION_PLANS: Record<string, SubscriptionPlanWithProvider> = {
	'zeco-free-plan': {
		id: 'zeco-free-plan',
		name: 'ZECO Free',
		tier1_token_limit: 100000,
		tier2_token_limit: 10000,
		cycle_credits: 25,
		price_minor: 0,
		currency: 'USD' as CurrencyCode,
		billing_period: 'lifetime' as BillingPeriod,
		active: true,
		created_at: new Date().toISOString(),
		updated_at: new Date().toISOString(),
		creem_product_id: '',
	},
	'zeco-pro-monthly-plan': {
		id: 'zeco-pro-monthly-plan',
		name: 'ZECO Pro',
		tier1_token_limit: 3000000,
		tier2_token_limit: 300000,
		cycle_credits: 500,
		price_minor: 1499, // $15.00 in cents (USD)
		currency: 'USD' as CurrencyCode,
		billing_period: 'monthly' as BillingPeriod,
		description: 'Monthly subscription plan with full access to AI models',
		active: true,
		created_at: new Date().toISOString(),
		updated_at: new Date().toISOString(),
		creem_product_id: isProd
			? process.env.CREEM_MONTHLY_PRODUCT_ID!
			: process.env.CREEM_TEST_MONTHLY_PRODUCT_ID!,
	},
};

/**
 * Get subscription plan by plan ID
 */
export function getSubscriptionPlan(planId: string): {
	data: SubscriptionPlanWithProvider | null;
	error: string | null;
} {
	const plan = SUBSCRIPTION_PLANS[planId as keyof typeof SUBSCRIPTION_PLANS];
	if (!plan) {
		return {
			data: null,
			error: `Invalid plan ID: ${planId}`,
		};
	}
	return { data: plan, error: null };
}

/**
 * Get subscription plan by Creem product ID
 * This is the correct way to map Creem webhooks to our plans
 */
export function getSubscriptionPlanByCreemProductId(
	productId: string
): SubscriptionPlanWithProvider | null {
	const plans = Object.values(SUBSCRIPTION_PLANS);
	const plan = plans.find((p) => p.creem_product_id === productId);
	return plan || null;
}
