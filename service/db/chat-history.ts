import type { SupabaseClient } from '@supabase/supabase-js';
import type { ChatHistory, ChatType } from '@/types/chat';

/**
 * DB Operations for chat_history table
 * Following the established pattern: functions accept supabase as first parameter
 */

export async function getChatHistoriesForUser(
	supabase: SupabaseClient,
	userId: string
): Promise<{ data: ChatHistory[] | null; error: any }> {
	const { data, error } = await supabase
		.from('chat_history')
		.select('*')
		.eq('user_id', userId)
		.order('last_modified_at', { ascending: false });

	if (error) {
		return { data: null, error };
	}

	const chatHistories = data.map((chat) => ({
		chatId: chat.chat_id,
		userId: chat.user_id,
		createdAt: chat.created_at,
		chatType: chat.chat_type,
		title: chat.title,
		historyContext: chat.history_context,
		lastModifiedAt: chat.last_modified_at,
	}));

	return { data: chatHistories, error: null };
}

export async function checkIfChatBelongsToUser(
	supabase: SupabaseClient,
	userId: string,
	chatId: string
): Promise<{ data: boolean; error: any }> {
	const { data: history, error } = await supabase
		.from('chat_history')
		.select('chat_id')
		.eq('chat_id', chatId)
		.eq('user_id', userId)
		.maybeSingle();

	if (error) {
		return { data: false, error };
	}

	return { data: !!history, error: null };
}

export async function createChatHistory(
	supabase: SupabaseClient,
	userId: string,
	chatId: string,
	title: string,
	type: ChatType
): Promise<{ data: any | null; error: any }> {
	const { data, error } = await supabase
		.from('chat_history')
		.insert({
			chat_id: chatId,
			user_id: userId,
			title: title,
			created_at: new Date().toISOString(),
			last_modified_at: new Date().toISOString(),
			chat_type: type,
			history_context: '',
		})
		.select()
		.single();

	return { data, error };
}

export async function updateChatHistoryLastModifiedAt(
	supabase: SupabaseClient,
	chatId: string
): Promise<{ data: any | null; error: any }> {
	const { data, error } = await supabase
		.from('chat_history')
		.update({
			last_modified_at: new Date().toISOString(),
		})
		.eq('chat_id', chatId)
		.select()
		.single();

	return { data, error };
}

export async function givePublicAccessToChat(
	supabase: SupabaseClient,
	chatId: string
): Promise<{ data: any | null; error: any }> {
	const { data, error } = await supabase
		.from('chat_history')
		.update({
			last_shared_at: new Date().toISOString(),
		})
		.eq('chat_id', chatId)
		.select()
		.single();

	return { data, error };
}

export async function getPublicAccessTimeForChat(
	supabase: SupabaseClient,
	chatId: string
): Promise<{ data: string | null; error: any }> {
	const { data: chatHistory, error } = await supabase
		.from('chat_history')
		.select('last_shared_at')
		.eq('chat_id', chatId)
		.maybeSingle();

	if (error) {
		return { data: null, error };
	}

	return { data: chatHistory?.last_shared_at || null, error: null };
}

export async function revokePublicAccessToChat(
	supabase: SupabaseClient,
	chatId: string
): Promise<{ data: any | null; error: any }> {
	const { data, error } = await supabase
		.from('chat_history')
		.update({
			last_shared_at: null,
		})
		.eq('chat_id', chatId)
		.select()
		.single();

	return { data, error };
}

export async function deleteChatHistory(
	supabase: SupabaseClient,
	userId: string,
	chatId: string
): Promise<{ data: any | null; error: any }> {
	const { data, error } = await supabase
		.from('chat_history')
		.delete()
		.eq('chat_id', chatId)
		.eq('user_id', userId)
		.select();

	return { data, error };
}

export async function clearAllChatHistories(
	supabase: SupabaseClient,
	userId: string
): Promise<{ data: any | null; error: any }> {
	const { data, error } = await supabase
		.from('chat_history')
		.delete()
		.eq('user_id', userId)
		.select();

	return { data, error };
}
