import type { SupabaseClient } from '@supabase/supabase-js';
import type { UserSubscription } from '@/types/payment';

// Types derived from interface - single source of truth
export type UserSubscriptionInsert = UserSubscription;
export type UserSubscriptionUpdate = Partial<Omit<UserSubscription, 'id' | 'created_at'>> & {
	updated_at: string;
};

// DB Operations
export async function createUserSubscription(
	supabase: SupabaseClient,
	data: UserSubscriptionInsert
): Promise<{ data: UserSubscription | null; error: any }> {
	const { data: result, error } = await supabase
		.from('user_subscription')
		.insert(data)
		.select()
		.single();

	return { data: result, error };
}

export async function updateUserSubscription(
	supabase: SupabaseClient,
	id: string,
	data: UserSubscriptionUpdate
): Promise<{ data: UserSubscription | null; error: any }> {
	const { data: result, error } = await supabase
		.from('user_subscription')
		.update(data)
		.eq('id', id)
		.select()
		.single();

	return { data: result, error };
}

export async function getUserSubscriptionById(
	supabase: SupabaseClient,
	id: string
): Promise<{ data: UserSubscription | null; error: any }> {
	const { data, error } = await supabase
		.from('user_subscription')
		.select('*')
		.eq('id', id)
		.single();

	return { data, error };
}

export async function hasActiveSubscription(
	supabase: SupabaseClient,
	userId: string,
	planId: string
): Promise<boolean> {
	const { data } = await supabase
		.from('user_subscription')
		.select('id')
		.eq('user_id', userId)
		.eq('subscription_plan_id', planId)
		.eq('status', 'active')
		.single();

	return !!data;
}

export async function cancelUserSubscription(
	supabase: SupabaseClient,
	id: string
): Promise<{ data: UserSubscription | null; error: any }> {
	const updateData: UserSubscriptionUpdate = {
		updated_at: new Date().toISOString(),
	};

	updateData.status = 'cancelled';

	return updateUserSubscription(supabase, id, updateData);
}

export async function getByProviderSubscriptionId(
	supabase: SupabaseClient,
	userId: string,
	providerSubscriptionId: string
): Promise<{ data: UserSubscription | null; error: any }> {
	const { data, error } = await supabase
		.from('user_subscription')
		.select('*')
		.eq('user_id', userId)
		.eq('status', 'active')
		.eq('provider_subscription_id', providerSubscriptionId)
		.order('created_at', { ascending: false })
		.limit(1)
		.maybeSingle();

	return { data, error };
}
