import type { SupabaseClient } from '@supabase/supabase-js';
import type { SubscriptionCycle } from '@/types/payment';

// Types derived from interface - single source of truth
export type SubscriptionCycleInsert = SubscriptionCycle;
export type SubscriptionCycleUpdate = Partial<Omit<SubscriptionCycle, 'id' | 'created_at'>> & {
	updated_at: string;
};

// DB Operations
export async function createSubscriptionCycle(
	supabase: SupabaseClient,
	data: SubscriptionCycleInsert
): Promise<{ data: SubscriptionCycle | null; error: any }> {
	const { data: result, error } = await supabase
		.from('subscription_cycle')
		.insert(data)
		.select()
		.single();

	return { data: result, error };
}

export async function updateSubscriptionCycle(
	supabase: SupabaseClient,
	id: string,
	data: SubscriptionCycleUpdate
): Promise<{ data: SubscriptionCycle | null; error: any }> {
	const { data: result, error } = await supabase
		.from('subscription_cycle')
		.update(data)
		.eq('id', id)
		.select()
		.single();

	return { data: result, error };
}

export async function getSubscriptionCycleById(
	supabase: SupabaseClient,
	id: string
): Promise<{ data: SubscriptionCycle | null; error: any }> {
	const { data, error } = await supabase
		.from('subscription_cycle')
		.select('*')
		.eq('id', id)
		.single();

	return { data, error };
}

export async function getCurrentSubscriptionCycle(
	supabase: SupabaseClient,
	userSubscriptionId: string
): Promise<{ data: SubscriptionCycle | null; error: any }> {
	const { data, error } = await supabase
		.from('subscription_cycle')
		.select('*')
		.eq('user_subscription_id', userSubscriptionId)
		.eq('is_current', true)
		.single();

	return { data, error };
}

export async function getCurrentSubscriptionCycleByUserId(
	supabase: SupabaseClient,
	userId: string
): Promise<{ data: SubscriptionCycle | null; error: any }> {
	const { data, error } = await supabase
		.from('subscription_cycle')
		.select('*')
		.eq('user_id', userId)
		.eq('is_current', true)
		.single();

	return { data, error };
}

export async function getCurrentSubscriptionCycleForPlan(
	supabase: SupabaseClient,
	userId: string,
	planId: string
): Promise<{ data: SubscriptionCycle | null; error: any }> {
	// First, get the user subscription for this plan
	const { data: subscription, error: subError } = await supabase
		.from('user_subscription')
		.select('id')
		.eq('user_id', userId)
		.eq('subscription_plan_id', planId)
		.eq('status', 'active')
		.single();

	if (subError || !subscription) {
		console.log('No active subscription found for plan:', { userId, planId, subError });
		return { data: null, error: subError };
	}

	// Then get the current cycle for that subscription
	const { data, error } = await supabase
		.from('subscription_cycle')
		.select('*')
		.eq('user_subscription_id', subscription.id)
		.eq('is_current', true)
		.single();

	return { data, error };
}

export async function updateCycleUsage(
	supabase: SupabaseClient,
	cycleId: string,
	usageUpdate: {
		tier1_tokens_used?: number;
		tier2_tokens_used?: number;
		credits_used?: number;
	}
): Promise<{ data: SubscriptionCycle | null; error: any }> {
	const updateData: SubscriptionCycleUpdate = {
		...usageUpdate,
		updated_at: new Date().toISOString(),
	};

	return updateSubscriptionCycle(supabase, cycleId, updateData);
}
