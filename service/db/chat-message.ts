import type { SupabaseClient } from '@supabase/supabase-js';
import type { ChatMessage, Response } from '@/types/chat';

/**
 * DB Operations for chat_message table
 * Following the established pattern: functions accept supabase as first parameter
 */

export async function getChatMessageById(
	supabase: SupabaseClient,
	messageId: string
): Promise<{ data: ChatMessage | null; error: any }> {
	const { data: message, error } = await supabase
		.from('chat_message')
		.select('*')
		.eq('id', messageId)
		.single();

	if (error) {
		return { data: null, error };
	}

	const chatMessage: ChatMessage = {
		id: message.id,
		chatId: message.chat_id,
		createdAt: message.created_at,
		prompt: message.prompt,
		response: message.response,
	};

	return { data: chatMessage, error: null };
}

export async function getChatMessagesByChatId(
	supabase: SupabaseClient,
	chatId: string,
	beforeTimestamp?: string
): Promise<{ data: ChatMessage[] | null; error: any }> {
	let query = supabase
		.from('chat_message')
		.select('*')
		.eq('chat_id', chatId)
		.order('created_at', { ascending: true });

	if (beforeTimestamp) {
		query = query.lte('created_at', beforeTimestamp);
	}

	const { data, error } = await query;

	if (error) {
		return { data: null, error };
	}

	const chatMessages = data.map((message) => ({
		id: message.id,
		chatId: message.chat_id,
		createdAt: message.created_at,
		prompt: message.prompt,
		response: message.response,
	}));

	return { data: chatMessages, error: null };
}

export async function createChatMessage(
	supabase: SupabaseClient,
	chatMessage: ChatMessage
): Promise<{ data: ChatMessage | null; error: any }> {
	const { data, error } = await supabase
		.from('chat_message')
		.insert({
			id: chatMessage.id,
			chat_id: chatMessage.chatId,
			created_at: chatMessage.createdAt,
			prompt: chatMessage.prompt,
			response: chatMessage.response,
		})
		.select()
		.single();

	if (error) {
		return { data: null, error };
	}

	const newChatMessage: ChatMessage = {
		id: data.id,
		chatId: data.chat_id,
		createdAt: data.created_at,
		prompt: data.prompt,
		response: data.response,
	};

	return { data: newChatMessage, error: null };
}

export async function createMultipleChatMessages(
	supabase: SupabaseClient,
	chatId: string,
	chatMessages: ChatMessage[]
): Promise<{ data: ChatMessage[] | null; error: any }> {
	const messagesToInsert = chatMessages.map((message) => ({
		id: message.id,
		chat_id: chatId,
		created_at: message.createdAt,
		prompt: message.prompt,
		response: message.response,
	}));

	const { data, error } = await supabase.from('chat_message').insert(messagesToInsert).select();

	if (error) {
		return { data: null, error };
	}

	const newChatMessages = data.map((message) => ({
		id: message.id,
		chatId: message.chat_id,
		createdAt: message.created_at,
		prompt: message.prompt,
		response: message.response,
	}));

	return { data: newChatMessages, error: null };
}

export async function updateChatMessageResponses(
	supabase: SupabaseClient,
	messageId: string,
	responses: Response[]
): Promise<{ data: ChatMessage | null; error: any }> {
	const { data, error } = await supabase
		.from('chat_message')
		.update({
			response: responses,
		})
		.eq('id', messageId)
		.select()
		.single();

	if (error) {
		return { data: null, error };
	}

	const updatedMessage: ChatMessage = {
		id: data.id,
		chatId: data.chat_id,
		createdAt: data.created_at,
		prompt: data.prompt,
		response: data.response,
	};

	return { data: updatedMessage, error: null };
}

export async function appendChatMessageResponse(
	supabase: SupabaseClient,
	messageId: string,
	newResponse: Response
): Promise<{ data: ChatMessage | null; error: any }> {
	// First get the existing message
	const { data: existingMessage, error: fetchError } = await supabase
		.from('chat_message')
		.select('response')
		.eq('id', messageId)
		.single();

	if (fetchError) {
		return { data: null, error: fetchError };
	}

	// Append the new response
	const updatedResponses = [...(existingMessage.response || []), newResponse];

	// Update the message
	const { data, error } = await supabase
		.from('chat_message')
		.update({
			response: updatedResponses,
		})
		.eq('id', messageId)
		.select()
		.single();

	if (error) {
		return { data: null, error };
	}

	const updatedMessage: ChatMessage = {
		id: data.id,
		chatId: data.chat_id,
		createdAt: data.created_at,
		prompt: data.prompt,
		response: data.response,
	};

	return { data: updatedMessage, error: null };
}

export async function deleteChatMessage(
	supabase: SupabaseClient,
	messageId: string
): Promise<{ data: any | null; error: any }> {
	const { data, error } = await supabase
		.from('chat_message')
		.delete()
		.eq('id', messageId)
		.select();

	return { data, error };
}

export async function deleteChatMessagesByChatId(
	supabase: SupabaseClient,
	chatId: string
): Promise<{ data: any | null; error: any }> {
	const { data, error } = await supabase
		.from('chat_message')
		.delete()
		.eq('chat_id', chatId)
		.select();

	return { data, error };
}
