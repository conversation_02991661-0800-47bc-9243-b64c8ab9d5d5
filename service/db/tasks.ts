import type { SupabaseClient } from '@supabase/supabase-js';
import type { Task, TaskData, TaskStatus } from '@/types/chat';

/**
 * DB Operations for tasks table
 * Following the established pattern: functions accept supabase as first parameter
 */

export async function createTask(
	supabase: SupabaseClient,
	requestId: string,
	userId: string,
	chatId: string,
	messageId: string,
	taskData: TaskData
): Promise<{ data: any | null; error: any }> {
	const { data, error } = await supabase
		.from('tasks')
		.insert({
			request_id: requestId,
			user_id: userId,
			chat_id: chatId,
			message_id: messageId,
			status: 'PENDING',
			task_data: taskData,
			created_at: new Date().toISOString(),
		})
		.select()
		.single();

	return { data, error };
}

export async function getTaskByRequestId(
	supabase: SupabaseClient,
	requestId: string
): Promise<{ data: Task | null; error: any }> {
	const { data: task, error } = await supabase
		.from('tasks')
		.select('*')
		.eq('request_id', requestId)
		.single();

	if (error) {
		return { data: null, error };
	}

	if (!task) {
		return {
			data: null,
			error: { message: `Task not found for request ID: ${requestId}` },
		};
	}

	const { request_id, message_id, chat_id, user_id, status, task_data, created_at, updated_at } =
		task;

	const taskData: Task = {
		requestId: request_id,
		messageId: message_id,
		chatId: chat_id,
		userId: user_id,
		status: status,
		data: task_data,
		payload: task.payload,
		createdAt: created_at,
		updatedAt: updated_at,
	};

	return { data: taskData, error: null };
}

export async function getTasksByUserId(
	supabase: SupabaseClient,
	userId: string,
	status?: TaskStatus
): Promise<{ data: Task[] | null; error: any }> {
	let query = supabase
		.from('tasks')
		.select('*')
		.eq('user_id', userId)
		.order('created_at', { ascending: false });

	if (status) {
		query = query.eq('status', status);
	}

	const { data, error } = await query;

	if (error) {
		return { data: null, error };
	}

	const tasks = data.map((task) => ({
		requestId: task.request_id,
		messageId: task.message_id,
		chatId: task.chat_id,
		userId: task.user_id,
		status: task.status,
		data: task.task_data,
		payload: task.payload,
		createdAt: task.created_at,
		updatedAt: task.updated_at,
	}));

	return { data: tasks, error: null };
}

export async function getTasksByChatId(
	supabase: SupabaseClient,
	chatId: string,
	status?: TaskStatus
): Promise<{ data: Task[] | null; error: any }> {
	let query = supabase
		.from('tasks')
		.select('*')
		.eq('chat_id', chatId)
		.order('created_at', { ascending: false });

	if (status) {
		query = query.eq('status', status);
	}

	const { data, error } = await query;

	if (error) {
		return { data: null, error };
	}

	const tasks = data.map((task) => ({
		requestId: task.request_id,
		messageId: task.message_id,
		chatId: task.chat_id,
		userId: task.user_id,
		status: task.status,
		data: task.task_data,
		payload: task.payload,
		createdAt: task.created_at,
		updatedAt: task.updated_at,
	}));

	return { data: tasks, error: null };
}

export async function updateTaskStatus(
	supabase: SupabaseClient,
	requestId: string,
	status: TaskStatus,
	payload: any
): Promise<{ data: Task | null; error: any }> {
	const { data, error } = await supabase
		.from('tasks')
		.update({
			status: status,
			payload: payload,
			updated_at: new Date().toISOString(),
		})
		.eq('request_id', requestId)
		.select()
		.single();

	if (error) {
		return { data: null, error };
	}

	const updatedTask: Task = {
		requestId: data.request_id,
		messageId: data.message_id,
		chatId: data.chat_id,
		userId: data.user_id,
		status: data.status,
		data: data.task_data,
		payload: data.payload,
		createdAt: data.created_at,
		updatedAt: data.updated_at,
	};

	return { data: updatedTask, error: null };
}

export async function deleteTask(
	supabase: SupabaseClient,
	requestId: string
): Promise<{ data: any | null; error: any }> {
	const { data, error } = await supabase
		.from('tasks')
		.delete()
		.eq('request_id', requestId)
		.select();

	return { data, error };
}

export async function deleteTasksByUserId(
	supabase: SupabaseClient,
	userId: string
): Promise<{ data: any | null; error: any }> {
	const { data, error } = await supabase.from('tasks').delete().eq('user_id', userId).select();

	return { data, error };
}

export async function deleteTasksByChatId(
	supabase: SupabaseClient,
	chatId: string
): Promise<{ data: any | null; error: any }> {
	const { data, error } = await supabase.from('tasks').delete().eq('chat_id', chatId).select();

	return { data, error };
}
