import type { SupabaseClient } from '@supabase/supabase-js';
import type { Provider, SubscriptionPayment, CreemProviderMetadata } from '@/types/payment';

// Types derived from interface - single source of truth
export type SubscriptionPaymentInsert = SubscriptionPayment;
export type SubscriptionPaymentUpdate = Partial<Omit<SubscriptionPayment, 'id' | 'created_at'>> & {
	updated_at: string;
};

// DB Operations
export async function createSubscriptionPayment(
	supabase: SupabaseClient,
	data: SubscriptionPaymentInsert
): Promise<{ data: SubscriptionPayment | null; error: any }> {
	const { data: result, error } = await supabase
		.from('subscription_payment')
		.insert(data)
		.select()
		.single();

	return { data: result, error };
}

export async function updateSubscriptionPayment(
	supabase: SupabaseClient,
	id: string,
	data: SubscriptionPaymentUpdate
): Promise<{ data: SubscriptionPayment | null; error: any }> {
	const { data: result, error } = await supabase
		.from('subscription_payment')
		.update(data)
		.eq('id', id)
		.select()
		.single();

	return { data: result, error };
}

export async function updateSubscriptionPaymentStatusToProcessing(
	supabase: SupabaseClient,
	id: string,
	providerData: CreemProviderMetadata
): Promise<{ data: SubscriptionPayment | null; error: any }> {
	const providerMetadata: Partial<CreemProviderMetadata> = {
		...providerData,
		provider: 'creem',
	};

	const { data: result, error } = await supabase
		.from('subscription_payment')
		.update({
			status: 'processing',
			provider_metadata: providerMetadata,
		})
		.eq('id', id)
		.eq('status', 'pending')
		.select()
		.single();

	return { data: result, error };
}

export async function getSubscriptionPaymentById(
	supabase: SupabaseClient,
	id: string
): Promise<{ data: SubscriptionPayment | null; error: any }> {
	const { data, error } = await supabase
		.from('subscription_payment')
		.select('*')
		.eq('id', id)
		.single();

	return { data, error };
}

export async function getExistingCustomerId(
	supabase: SupabaseClient,
	userId: string,
	provider: Provider = 'creem'
): Promise<string | null> {
	const { data } = await supabase
		.from('subscription_payment')
		.select('provider_metadata')
		.eq('user_id', userId)
		.eq('provider', provider)
		.not('provider_metadata->>customer_id', 'is', null)
		.order('created_at', { ascending: false })
		.limit(1)
		.single();

	return data?.provider_metadata?.customer_id || null;
}

/**
 * Get the latest processing payment for a user
 * Returns payment only if it's 'processing' status and created within last 15 minutes
 */
export async function getLatestProcessingPayment(
	supabase: SupabaseClient,
	userId: string
): Promise<{ data: SubscriptionPayment | null; error: any }> {
	const { data, error } = await supabase
		.from('subscription_payment')
		.select('*')
		.eq('user_id', userId)
		.eq('status', 'processing')
		.order('created_at', { ascending: false })
		.limit(1)
		.maybeSingle();

	return { data, error };
}
