import type { SupabaseClient } from '@supabase/supabase-js';
import type { Experimental_GeneratedImage as GeneratedImage } from 'ai';
import { nanoid } from '@/lib';
import { base64ToUint8Array } from '@/utils/image/local-image-processing';

/**
 * DB Operations for Supabase Storage (ai-generated-images, ai-generated-videos)
 * Following the established pattern: functions accept supabase as first parameter
 */

export async function storeGeneratedImage(
	supabase: SupabaseClient,
	image: GeneratedImage,
	userId: string
): Promise<{ data: string | null; error: any }> {
	const timestamp = new Date().getTime();
	const fileExtension = image.mimeType.split('/').pop() || 'png';
	const fileName = `${userId}/${timestamp}/${nanoid()}.${fileExtension}`;

	const { error: uploadError } = await supabase.storage
		.from('ai-generated-images')
		.upload(fileName, image.uint8Array, {
			contentType: image.mimeType,
			upsert: true,
		});

	if (uploadError) {
		return { data: null, error: uploadError };
	}

	const {
		data: { publicUrl },
	} = supabase.storage.from('ai-generated-images').getPublicUrl(fileName);

	return { data: publicUrl, error: null };
}

export async function storeBase64Image(
	supabase: SupabaseClient,
	base64DataUrl: string,
	userId: string
): Promise<{ data: string | null; error: any }> {
	// Extract base64 from data URL
	const base64Match = base64DataUrl.match(/^data:(.*?);base64,(.*)$/);
	if (!base64Match) {
		return {
			data: null,
			error: { message: 'Invalid base64 image data' },
		};
	}

	const mimeType = base64Match[1];
	const base64Data = base64Match[2];
	const uint8Array = base64ToUint8Array(base64Data);
	const blob = new Blob([uint8Array], { type: mimeType });

	const timestamp = new Date().getTime();
	const fileExtension = mimeType.split('/').pop() || 'png';
	const fileName = `${userId}/uploads/${timestamp}/${nanoid()}.${fileExtension}`;

	const { error: uploadError } = await supabase.storage
		.from('ai-generated-images')
		.upload(fileName, blob, {
			contentType: mimeType,
			upsert: true,
		});

	if (uploadError) {
		return { data: null, error: uploadError };
	}

	const {
		data: { publicUrl },
	} = supabase.storage.from('ai-generated-images').getPublicUrl(fileName);

	return { data: publicUrl, error: null };
}

export async function storeUploadedImage(
	supabase: SupabaseClient,
	file: File | Blob,
	userId: string
): Promise<{ data: string | null; error: any }> {
	const timestamp = new Date().getTime();
	const fileExtension = file.type.split('/').pop() || 'png';
	const fileName = `${userId}/uploads/${timestamp}/${nanoid()}.${fileExtension}`;

	const { error: uploadError } = await supabase.storage
		.from('ai-generated-images')
		.upload(fileName, file, {
			contentType: file.type,
			upsert: true,
		});

	if (uploadError) {
		return { data: null, error: uploadError };
	}

	const {
		data: { publicUrl },
	} = supabase.storage.from('ai-generated-images').getPublicUrl(fileName);

	return { data: publicUrl, error: null };
}

export async function storeGeneratedVideo(
	supabase: SupabaseClient,
	file: File | Blob,
	userId: string
): Promise<{ data: string | null; error: any }> {
	const timestamp = new Date().getTime();
	const fileExtension = file.type.split('/').pop() || 'mp4';
	const fileName = `${userId}/${timestamp}/${nanoid()}.${fileExtension}`;

	const { error: uploadError } = await supabase.storage
		.from('ai-generated-videos')
		.upload(fileName, file, {
			contentType: file.type,
			upsert: true,
		});

	if (uploadError) {
		return { data: null, error: uploadError };
	}

	const {
		data: { publicUrl },
	} = supabase.storage.from('ai-generated-videos').getPublicUrl(fileName);

	return { data: publicUrl, error: null };
}

export async function storeGeneratedVideoUrl(
	supabase: SupabaseClient,
	videoUrl: string,
	userId: string
): Promise<{ data: string | null; error: any }> {
	try {
		// Fetch the video from the temporary URL
		const response = await fetch(videoUrl);
		if (!response.ok) {
			return {
				data: null,
				error: { message: `Failed to fetch video from URL: ${response.statusText}` },
			};
		}

		const videoBlob = await response.blob();
		const timestamp = new Date().getTime();
		const fileExtension = videoBlob.type.split('/').pop() || 'mp4';
		const fileName = `${userId}/${timestamp}/${nanoid()}.${fileExtension}`;

		const { error: uploadError } = await supabase.storage
			.from('ai-generated-videos')
			.upload(fileName, videoBlob, {
				contentType: videoBlob.type,
				upsert: true,
			});

		if (uploadError) {
			return { data: null, error: uploadError };
		}

		const {
			data: { publicUrl },
		} = supabase.storage.from('ai-generated-videos').getPublicUrl(fileName);

		return { data: publicUrl, error: null };
	} catch (error) {
		return {
			data: null,
			error: { message: error instanceof Error ? error.message : 'Unknown error' },
		};
	}
}

export async function deleteStoredFile(
	supabase: SupabaseClient,
	bucket: 'ai-generated-images' | 'ai-generated-videos',
	filePath: string
): Promise<{ data: any | null; error: any }> {
	const { data, error } = await supabase.storage.from(bucket).remove([filePath]);

	return { data, error };
}

export async function getSignedUrl(
	supabase: SupabaseClient,
	bucket: 'ai-generated-images' | 'ai-generated-videos',
	filePath: string,
	expiresIn: number = 3600 // 1 hour default
): Promise<{ data: string | null; error: any }> {
	const { data, error } = await supabase.storage
		.from(bucket)
		.createSignedUrl(filePath, expiresIn);

	if (error) {
		return { data: null, error };
	}

	return { data: data.signedUrl, error: null };
}
