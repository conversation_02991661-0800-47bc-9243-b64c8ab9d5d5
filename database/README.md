# Database Optimizations Guide

This directory contains database optimizations for the ZecoAI subscription system.

## Files

- `optimizations.sql` - Complete database optimization script with indexes, RLS policies, and performance tuning

## Implementation Steps

### 1. Pre-Production Setup

```bash
# Connect to your Supabase project
psql "postgresql://postgres:<EMAIL>:5432/postgres"

# Run the optimizations script
\i database/optimizations.sql
```

### 2. Production Deployment

**⚠️ Important: Run during low-traffic periods**

```sql
-- Enable concurrent index creation (non-blocking)
-- This is already included in the optimizations.sql file
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_name ON table_name (columns);
```

### 3. Monitoring Setup

After deployment, set up monitoring:

```sql
-- Enable pg_stat_statements for query performance monitoring
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Monitor index usage
SELECT schemaname, tablename, indexname, idx_scan
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;
```

## Performance Impact

### Expected Improvements

1. **Subscription Queries**: 10-50x faster for user subscription lookups
2. **Chat Loading**: 5-20x faster for chat history retrieval
3. **Webhook Processing**: 5-10x faster with deduplication indexes
4. **Usage Tracking**: 10-100x faster for current cycle lookups

### Critical Indexes

| Index                                 | Purpose                   | Expected Impact |
| ------------------------------------- | ------------------------- | --------------- |
| `idx_subscription_cycle_user_current` | Current cycle lookup      | 10-100x faster  |
| `idx_user_subscription_user_status`   | Active subscription check | 10-50x faster   |
| `idx_webhook_event_dedup`             | Webhook deduplication     | 5-10x faster    |
| `idx_chat_history_user_recent`        | Chat sidebar loading      | 5-20x faster    |

## Row Level Security (RLS)

RLS policies are implemented for:

- ✅ `subscription_cycle` - User can only access their cycles
- ✅ `subscription_payment` - User can only access their payments
- ✅ `user_subscription` - User can only access their subscriptions
- ✅ `chat_history` - User can only access their chats (+ public shared chats)
- ✅ `chat_message` - User can only access messages from their chats
- ✅ `tasks` - User can only access their tasks

**Service Role Bypass**: All policies include service role bypass for webhook processing and system operations.

## Maintenance Schedule

### Daily

```sql
VACUUM ANALYZE subscription_cycle;
VACUUM ANALYZE chat_message;
VACUUM ANALYZE webhook_event;
```

### Weekly

```sql
VACUUM ANALYZE subscription_payment;
VACUUM ANALYZE user_subscription;
VACUUM ANALYZE chat_history;
VACUUM ANALYZE tasks;
```

### Monthly

- Review slow query log
- Analyze index usage statistics
- Update table statistics
- Review RLS policy performance

## Production Checklist

- [ ] Apply optimizations during maintenance window
- [ ] Monitor query performance for 24-48 hours
- [ ] Verify RLS policies are working correctly
- [ ] Set up automated VACUUM/ANALYZE schedule
- [ ] Configure connection pooling (PgBouncer recommended)
- [ ] Enable query performance monitoring
- [ ] Document any custom optimizations

## Troubleshooting

### Slow Queries After Optimization

1. Check if indexes are being used:

```sql
EXPLAIN ANALYZE SELECT * FROM table WHERE conditions;
```

2. Update table statistics:

```sql
ANALYZE table_name;
```

3. Check for table bloat:

```sql
SELECT schemaname, tablename, n_dead_tup, n_live_tup
FROM pg_stat_user_tables
WHERE n_dead_tup > n_live_tup;
```

### RLS Policy Issues

1. Test policy with specific user:

```sql
SET row_security = on;
SET ROLE authenticated;
SELECT set_config('request.jwt.claims', '{"sub":"user-id"}', true);
SELECT * FROM table_name; -- Should only return user's data
```

2. Verify service role bypass:

```sql
SET ROLE service_role;
SELECT * FROM table_name; -- Should return all data
```

## Performance Testing

Use the test queries in `optimizations.sql` to verify performance improvements:

```sql
-- Test current subscription cycle lookup
EXPLAIN ANALYZE
SELECT * FROM subscription_cycle
WHERE user_id = 'test-user-id' AND is_current = true;

-- Should show "Index Scan" with low cost
```

## Support

For issues with database optimizations:

1. Check Supabase logs for any constraint violations
2. Monitor index usage with `pg_stat_user_indexes`
3. Review query plans with `EXPLAIN ANALYZE`
4. Test RLS policies in isolated sessions
