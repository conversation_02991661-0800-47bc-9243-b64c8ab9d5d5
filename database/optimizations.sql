-- ===================================================================
-- DATABASE OPTIMIZATIONS FOR ZECOAI SUBSCRIPTION SYSTEM
-- ===================================================================
-- This file contains database indexes, RLS policies, and optimizations
-- for the subscription and chat system.
--
-- Run this after initial schema creation for production performance.
-- ===================================================================

-- ===================================================================
-- PERFORMANCE INDEXES
-- ===================================================================

-- -------------------------------------------------------------------
-- SUBSCRIPTION_CYCLE TABLE INDEXES
-- High-frequency user usage tracking queries
-- -------------------------------------------------------------------

-- Critical index for getCurrentSubscriptionCycleByUserId() - called on every request
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_cycle_user_current 
ON subscription_cycle (user_id, is_current) 
WHERE is_current = true;

-- Index for getCurrentSubscriptionCycle() by subscription
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_cycle_subscription_current 
ON subscription_cycle (user_subscription_id, is_current) 
WHERE is_current = true;

-- Index for cycle history queries (ordered by cycle_index)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_cycle_subscription_index 
ON subscription_cycle (user_subscription_id, cycle_index DESC);

-- Index for payment-linked cycle queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_cycle_payment 
ON subscription_cycle (subscription_payment_id) 
WHERE subscription_payment_id IS NOT NULL;

-- -------------------------------------------------------------------
-- SUBSCRIPTION_PAYMENT TABLE INDEXES
-- Payment processing and webhook handling
-- -------------------------------------------------------------------

-- User payment history (most recent first)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_payment_user_recent 
ON subscription_payment (user_id, created_at DESC);

-- Provider customer lookup for external API calls
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_payment_provider_customer 
ON subscription_payment (provider, (provider_metadata->>'customer_id')) 
WHERE provider_metadata->>'customer_id' IS NOT NULL;

-- Provider subscription lookup for cancellations/updates
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_payment_provider_subscription 
ON subscription_payment (provider, (provider_metadata->>'subscription_id')) 
WHERE provider_metadata->>'subscription_id' IS NOT NULL;

-- Webhook event idempotency (critical for webhook processing)
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_payment_webhook_event 
ON subscription_payment (webhook_event_id) 
WHERE webhook_event_id IS NOT NULL;

-- Processing payment queries (getLatestProcessingPayment)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_payment_user_processing 
ON subscription_payment (user_id, status, created_at DESC) 
WHERE status = 'processing';

-- Payment status queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_payment_status_created 
ON subscription_payment (status, created_at DESC);

-- -------------------------------------------------------------------
-- USER_SUBSCRIPTION TABLE INDEXES
-- Active subscription checks and user access control
-- -------------------------------------------------------------------

-- Critical index for hasActiveSubscription() - called frequently for access control
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_subscription_user_status 
ON user_subscription (user_id, status) 
WHERE status = 'active';

-- Plan-specific subscription queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_subscription_user_plan_status 
ON user_subscription (user_id, subscription_plan_id, status);

-- Provider subscription lookup for webhook processing
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_subscription_provider 
ON user_subscription (provider_subscription_id, user_id) 
WHERE provider_subscription_id IS NOT NULL;

-- Recent subscriptions for user dashboard
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_subscription_user_recent 
ON user_subscription (user_id, created_at DESC);

-- Webhook audit trail queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_subscription_webhook_created 
ON user_subscription (created_by_webhook_event_id) 
WHERE created_by_webhook_event_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_subscription_webhook_modified 
ON user_subscription (last_modified_by_webhook_event_id) 
WHERE last_modified_by_webhook_event_id IS NOT NULL;

-- -------------------------------------------------------------------
-- WEBHOOK_EVENT TABLE INDEXES
-- Webhook processing and idempotency
-- -------------------------------------------------------------------

-- Critical unique index for webhook deduplication
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_event_dedup 
ON webhook_event (webhook_id, provider);

-- Processing status queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_event_processing 
ON webhook_event (status, retry_count, created_at ASC) 
WHERE status IN ('pending', 'failed');

-- Event type analytics and debugging
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_event_type_status 
ON webhook_event (event_type, status, created_at DESC);

-- Failed webhook retry queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_event_failed_retry 
ON webhook_event (status, retry_count, created_at ASC) 
WHERE status = 'failed' AND retry_count < 3;

-- -------------------------------------------------------------------
-- CHAT_HISTORY TABLE INDEXES
-- Chat listing and management
-- -------------------------------------------------------------------

-- Primary user chat listing (sorted by recent activity)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_chat_history_user_recent 
ON chat_history (user_id, last_modified_at DESC);

-- Chat ownership verification
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_chat_history_ownership 
ON chat_history (chat_id, user_id);

-- Shared chat queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_chat_history_shared 
ON chat_history (is_shared, last_shared_at DESC) 
WHERE is_shared = true;

-- -------------------------------------------------------------------
-- CHAT_MESSAGE TABLE INDEXES
-- Message retrieval and ordering
-- -------------------------------------------------------------------

-- Primary message ordering within chats
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_chat_message_chat_order 
ON chat_message (chat_id, created_at ASC);

-- Recent messages for chat loading
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_chat_message_chat_recent 
ON chat_message (chat_id, created_at DESC);

-- Message search within chats (if needed for future features)
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_chat_message_content_search 
-- ON chat_message USING GIN (to_tsvector('english', content_text));

-- -------------------------------------------------------------------
-- TASKS TABLE INDEXES
-- Background task processing
-- -------------------------------------------------------------------

-- Task processing queue
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_processing 
ON tasks (status, created_at ASC) 
WHERE status IN ('pending', 'processing');

-- User task history
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_user_recent 
ON tasks (user_id, created_at DESC);

-- ===================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- ===================================================================

-- Enable RLS on all user-scoped tables
ALTER TABLE subscription_cycle ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_payment ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscription ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_message ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;

-- -------------------------------------------------------------------
-- SUBSCRIPTION_CYCLE RLS POLICIES
-- -------------------------------------------------------------------

-- Users can only access their own subscription cycles
CREATE POLICY subscription_cycle_user_access ON subscription_cycle
    FOR ALL USING (auth.uid()::text = user_id);

-- Service role can access all cycles (for webhook processing)
CREATE POLICY subscription_cycle_service_access ON subscription_cycle
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- -------------------------------------------------------------------
-- SUBSCRIPTION_PAYMENT RLS POLICIES
-- -------------------------------------------------------------------

-- Users can only access their own payments
CREATE POLICY subscription_payment_user_access ON subscription_payment
    FOR ALL USING (auth.uid()::text = user_id);

-- Service role can access all payments (for webhook processing)
CREATE POLICY subscription_payment_service_access ON subscription_payment
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- -------------------------------------------------------------------
-- USER_SUBSCRIPTION RLS POLICIES
-- -------------------------------------------------------------------

-- Users can only access their own subscriptions
CREATE POLICY user_subscription_user_access ON user_subscription
    FOR ALL USING (auth.uid()::text = user_id);

-- Service role can access all subscriptions (for webhook processing)
CREATE POLICY user_subscription_service_access ON user_subscription
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- -------------------------------------------------------------------
-- CHAT_HISTORY RLS POLICIES
-- -------------------------------------------------------------------

-- Users can only access their own chats
CREATE POLICY chat_history_user_access ON chat_history
    FOR ALL USING (auth.uid()::text = user_id);

-- Public read access for shared chats
CREATE POLICY chat_history_shared_read ON chat_history
    FOR SELECT USING (is_shared = true);

-- Service role can access all chats (for maintenance)
CREATE POLICY chat_history_service_access ON chat_history
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- -------------------------------------------------------------------
-- CHAT_MESSAGE RLS POLICIES
-- -------------------------------------------------------------------

-- Users can only access messages from their own chats
CREATE POLICY chat_message_user_access ON chat_message
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM chat_history 
            WHERE chat_history.chat_id = chat_message.chat_id 
            AND chat_history.user_id = auth.uid()::text
        )
    );

-- Public read access for shared chat messages
CREATE POLICY chat_message_shared_read ON chat_message
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM chat_history 
            WHERE chat_history.chat_id = chat_message.chat_id 
            AND chat_history.is_shared = true
        )
    );

-- Service role can access all messages
CREATE POLICY chat_message_service_access ON chat_message
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- -------------------------------------------------------------------
-- TASKS RLS POLICIES
-- -------------------------------------------------------------------

-- Users can only access their own tasks
CREATE POLICY tasks_user_access ON tasks
    FOR ALL USING (auth.uid()::text = user_id);

-- Service role can access all tasks (for background processing)
CREATE POLICY tasks_service_access ON tasks
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- ===================================================================
-- QUERY OPTIMIZATIONS AND MAINTENANCE
-- ===================================================================

-- -------------------------------------------------------------------
-- VACUUM AND ANALYZE SCHEDULE RECOMMENDATIONS
-- -------------------------------------------------------------------

-- Run these commands periodically for optimal performance:
-- 
-- High-frequency tables (daily):
-- VACUUM ANALYZE subscription_cycle;
-- VACUUM ANALYZE chat_message;
-- VACUUM ANALYZE webhook_event;
--
-- Medium-frequency tables (weekly):
-- VACUUM ANALYZE subscription_payment;
-- VACUUM ANALYZE user_subscription;
-- VACUUM ANALYZE chat_history;
-- VACUUM ANALYZE tasks;

-- -------------------------------------------------------------------
-- TABLE STATISTICS UPDATES
-- -------------------------------------------------------------------

-- Update table statistics for query planner optimization
ANALYZE subscription_cycle;
ANALYZE subscription_payment;
ANALYZE user_subscription;
ANALYZE webhook_event;
ANALYZE chat_history;
ANALYZE chat_message;
ANALYZE tasks;

-- -------------------------------------------------------------------
-- CONNECTION POOLING RECOMMENDATIONS
-- -------------------------------------------------------------------

-- For production, configure connection pooling:
-- 
-- PgBouncer configuration recommendations:
-- - pool_mode = transaction (for better concurrency)
-- - default_pool_size = 25 (adjust based on load)
-- - max_client_conn = 100 (adjust based on traffic)
-- - reserve_pool_size = 5 (for emergency connections)

-- -------------------------------------------------------------------
-- MONITORING QUERIES
-- -------------------------------------------------------------------

-- Monitor index usage (run periodically):
-- SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
-- FROM pg_stat_user_indexes 
-- WHERE schemaname = 'public'
-- ORDER BY idx_scan DESC;

-- Monitor table performance:
-- SELECT schemaname, tablename, seq_scan, seq_tup_read, idx_scan, idx_tup_fetch,
--        n_tup_ins, n_tup_upd, n_tup_del
-- FROM pg_stat_user_tables 
-- WHERE schemaname = 'public'
-- ORDER BY seq_scan DESC;

-- Monitor slow queries (enable pg_stat_statements extension):
-- SELECT query, calls, total_time, mean_time, rows
-- FROM pg_stat_statements 
-- WHERE query LIKE '%subscription_%' OR query LIKE '%chat_%'
-- ORDER BY mean_time DESC 
-- LIMIT 10;

-- ===================================================================
-- PERFORMANCE TESTING QUERIES
-- ===================================================================

-- Test critical query performance after applying indexes:

-- 1. Test current subscription cycle lookup (most critical)
-- EXPLAIN ANALYZE 
-- SELECT * FROM subscription_cycle 
-- WHERE user_id = 'test-user-id' AND is_current = true;

-- 2. Test active subscription check
-- EXPLAIN ANALYZE 
-- SELECT * FROM user_subscription 
-- WHERE user_id = 'test-user-id' AND status = 'active';

-- 3. Test webhook deduplication
-- EXPLAIN ANALYZE 
-- SELECT * FROM webhook_event 
-- WHERE webhook_id = 'test-webhook-id' AND provider = 'creem';

-- 4. Test chat history loading
-- EXPLAIN ANALYZE 
-- SELECT * FROM chat_history 
-- WHERE user_id = 'test-user-id' 
-- ORDER BY last_modified_at DESC 
-- LIMIT 20;

-- ===================================================================
-- BACKUP AND RECOVERY CONSIDERATIONS
-- ===================================================================

-- For production deployments:
-- 1. Enable Point-in-Time Recovery (PITR)
-- 2. Set up automated daily backups
-- 3. Test backup restoration procedures
-- 4. Monitor database size growth
-- 5. Implement log retention policies

-- Critical tables for backup priority:
-- 1. user_subscription (business critical)
-- 2. subscription_payment (financial data)
-- 3. subscription_cycle (usage tracking)
-- 4. chat_history (user data)
-- 5. webhook_event (audit trail)

-- ===================================================================
-- END OF OPTIMIZATIONS
-- ===================================================================