import { type NextRequest } from 'next/server';
import { updateSession } from '@/utils/supabase/middleware';

export async function middleware(request: NextRequest) {
	return await updateSession(request);
}

export const config = {
	matcher: [
		/*
		 * Match all request paths except for the ones starting with:
		 * - _next/static (static files)
		 * - _next/image (image optimization files)
		 * - favicon.ico (favicon file)
		 * - manifest.json (PWA manifest file)
		 * - api/video/fal (webhook URL for fal ai)
		 * - api/subscriptions/creem/webhook (webhook URL for creem payments)
		 * Feel free to modify this pattern to include more paths.
		 */
		'/((?!_next/static|_next/image|favicon.ico|manifest.json|api/video/fal|api/webhooks/creem|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
	],
};
